// FRONTEND CHANGES (ChatTwo.tsx)

// 1. Update the useEffect hook for socket connection
useEffect(() => {
  // Connect to socket if not already connected
  if (!socket) {
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);
  }

  // Set up socket event listeners when socket is available
  if (socket) {
    // Join the chat room as soon as possible
    if (chatroomId) {
      console.log(`Joining room: ${chatroomId}`);
      socket.emit('join room', chatroomId);
    }

    // Listen for new messages
    socket.on('message received', (newMessage) => {
      console.log('New message received:', newMessage);
      
      // Check if this message belongs to our chat room
      if (chatroomId && newMessage.chatRoomId && 
          newMessage.chatRoomId.toString() === chatroomId.toString()) {
        
        // Format message for GiftedChat
        const formattedMessage = {
          _id: newMessage._id,
          text: newMessage.text,
          createdAt: new Date(newMessage.createdAt),
          user: {
            _id: newMessage.user._id,
            name: newMessage.user.name || '',
            avatar: newMessage.user.avatar || 'defaultUser.webp'
          },
          messageStatus: newMessage.messageStatus
        };
        
        // Update messages state
        setMessages((previousMessages) => 
          GiftedChat.append(previousMessages, [formattedMessage])
        );
        
        // If the message is from the partner, mark it as read
        if (newMessage.user._id.toString() === partner._id.toString()) {
          // Mark message as read if we're the receiver
          socket.emit('message_read', {
            messageIds: [newMessage._id],
            chatRoomId: chatroomId,
            receiverId: userId
          });
        }
      }
    });

    // Listen for message status updates
    socket.on('message_status_update', ({ messageIds, status, chatRoomId: roomId }) => {
      // Only process updates for our chat room
      if (chatroomId && roomId && chatroomId.toString() === roomId.toString()) {
        setMessages((prevMessages) => {
          return prevMessages.map(msg => {
            if (messageIds.includes(msg._id)) {
              return { ...msg, messageStatus: status };
            }
            return msg;
          });
        });
      }
    });

    // Listen for new conversation (when a new chat room is created)
    socket.on('new conversation', (newConversation) => {
      console.log('New conversation received:', newConversation);
      
      // If we don't have a chatroomId yet, and this conversation involves our partner
      if (!chatroomId && newConversation.partner && 
          newConversation.partner._id.toString() === partner._id.toString()) {
        
        // Update our chatroomId
        setChatroomId(newConversation._id);
        
        // Join the new room
        socket.emit('join room', newConversation._id);
        
        // Update messages if needed
        if (newConversation.messages && newConversation.messages.length > 0) {
          const formattedMessages = newConversation.messages.map(msg => ({
            _id: msg._id,
            text: msg.text,
            createdAt: new Date(msg.createdAt),
            user: {
              _id: msg.user._id,
              name: msg.user.name || '',
              avatar: msg.user.avatar || 'defaultUser.webp'
            },
            messageStatus: msg.messageStatus
          }));
          
          setMessages((previousMessages) => 
            GiftedChat.append(previousMessages, formattedMessages)
          );
        }
      }
    });

    // Clean up event listeners when component unmounts
    return () => {
      socket.off('message received');
      socket.off('message_status_update');
      socket.off('new conversation');
    };
  }
}, [socket, chatroomId, partner._id, userId]);

// 2. Update the onSend function to handle new conversations
const onSend = useCallback((messages = []) => {
  if (messages.length === 0) return;
  
  const newMessage = messages[0];
  
  // Add the new message to the UI immediately (optimistic update)
  setMessages(previousMessages => GiftedChat.append(previousMessages, messages));
  
  // Prepare message for sending to server
  const messageToSend = {
    senderId: userId,
    receiverId: partner._id,
    chatRoomId: chatroomId, // This might be null for new conversations
    text: newMessage.text,
    messageStatus: MESSAGE_STATUS.SENT
  };
  
  // Send message via socket
  socket.emit('new message', messageToSend);
  
  // Start typing indicator timeout
  clearTimeout(typingTimeout.current);
  setIsTyping(false);
  socket.emit('stop_typing', { roomId: chatroomId, userId });
}, [userId, partner._id, chatroomId, socket]);

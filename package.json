{"name": "hollystone", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@birdwingo/react-native-instagram-stories": "^1.3.12", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-camera-roll/camera-roll": "^5.10.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-vector-icons/ionicons": "^7.3.1-alpha.9", "@react-navigation/native": "^6.1.8", "@react-navigation/stack": "^6.3.18", "@shopify/flash-list": "^1.7.5", "axios": "^1.5.1", "d3-shape": "^3.2.0", "dayjs": "^1.11.10", "lottie-react-native": "^7.2.2", "mobx": "^6.10.2", "mobx-persist": "^0.4.1", "mobx-react-lite": "^4.0.5", "moti": "^0.27.0", "native-base": "^3.4.28", "react": "18.2.0", "react-native": "0.72.6", "react-native-camera": "^4.2.1", "react-native-countdown-circle-timer": "^3.2.1", "react-native-credit-card-input": "^1.0.0", "react-native-flip-card": "^3.5.7", "react-native-gesture-handler": "^2.13.2", "react-native-get-location": "^4.0.0", "react-native-gifted-chat": "^2.4.0", "react-native-image-picker": "^7.0.1", "react-native-insta-story": "^1.1.9", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.8.0", "react-native-maps-directions": "^1.9.0", "react-native-numeric-pad": "^1.1.5", "react-native-onesignal": "^5.2.3", "react-native-permissions": "^5.2.6", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.1", "react-native-reanimated": "^3.5.4", "react-native-reanimated-carousel": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "3.3.2", "react-native-screens": "^3.25.0", "react-native-share": "^11.1.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "^14.2.0", "react-native-swiper": "^1.6.0", "react-native-view-shot": "^3.7.0", "react-native-vision-camera": "^3.3.1", "react-native-webview": "^13.6.2", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}
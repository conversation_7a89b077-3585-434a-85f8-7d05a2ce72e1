import React, { createContext, useContext, useState } from 'react';

const UnreadContext = createContext();

export const UnreadProvider = ({ children }) => {
    const [unreadCount, setUnreadCount] = useState(0);

    // Okunmamış mesajları hesaplayan fonksiyon
    const getUnreadMessagesCount = (conversations, currentUserId) => {
        let count = 0;
        
        conversations.forEach(conversation => {
            const unreadMessages = conversation.messages.filter(
                (msg) =>
                    (msg.messageStatus === 'delivered' || msg.messageStatus === 'sent') &&
                    msg.senderId !== currentUserId // Sadece karşı tarafın mesajlarını say
            );
            count += unreadMessages.length;
        });

        setUnreadCount(count);
    };

    return (
        <UnreadContext.Provider value={{ unreadCount, getUnreadMessagesCount }}>
            {children}
        </UnreadContext.Provider>
    );
};

export const useUnread = () => useContext(UnreadContext);

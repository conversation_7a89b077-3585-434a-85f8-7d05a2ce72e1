import { useNavigation } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import { FlatList, ImageBackground, ScrollView, StyleSheet, Text, View, Dimensions } from "react-native";
import { screens } from "../../navigation";
import Layout from "../../constants/Layout";
import Header from "../../components/Header";
import NotifyView from "../../components/NotifyView";
import RootList from "../../components/RootList";
import Menu from "../../components/Menu";
import { black, gray_t8, green_t1, white } from "../../constants/Color";
import { get, getImageURL, post } from "../../networking/Server";
import Toast from "../../components/Toast";
import { SafeAreaView } from "react-native-safe-area-context";
import { Spinner } from "native-base";
import ConcertItem from "../../components/ConcertItem";
import BottomBar from "../../components/BottomBar";
import { MainStore } from "../../stores/MainStore";
import { useRoute } from "@react-navigation/native";
import { OneSignal } from "react-native-onesignal";
import { useUnread } from "../../functions/UnreadContext";
import { AnnouncementStore } from "../../stores/AnnouncementStore";
import { socket } from "../../networking/Socket";


const dayjs = require("dayjs");

// Make Root an observer if using MobX
const Root: React.FC = () => {
  const getChatInfo = () => {
    get("chat").then((res: any) => {
      if (res.type === "success") {
        getUnreadMessagesCount(res.conversations);
      } else {
        startToast(res.error, "error");
        setTimeout(() => navigation.pop(), 2000);
      }
    });
  };

  const [showWarning, setShowWarning] = useState(false);
  const { getUnreadMessagesCount } = useUnread();
  const navigation = useNavigation<screens>();
  const [statusToast, setStatusToast]: any = React.useState(null);
  const [typeToast, setTypeToast] = React.useState("");
  const [subtitleToast, setSubTitleToast] = React.useState("");
  const [menu, setMenu] = React.useState(false);
  const [isTyping, setIsTyping] = React.useState("");
  const [conDai, setConDai] = React.useState([]);
  const [conDai_, setConDai_] = React.useState([]);
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const flatListRef = React.useRef<FlatList>(null);
  const date = dayjs();
  const day = date.format("DD");
  const month = date.format("MMMM");
  const day_ = date.format("dddd");
  const route = useRoute();
  const [refresh, setRefresh] = useState(false);

  const startToast = (message: string, type: string) => {
    setStatusToast(true);
    setSubTitleToast(message);
    setTypeToast(type);
    setTimeout(() => setStatusToast(false), 1500);
  };

  async function logUserInfo() {
    try {
      const oneSignalId = await OneSignal.User.pushSubscription.getIdAsync();
      await post("users/save-push-id", { oneSignalId });
    } catch (error) {
      console.error("Kullanıcı bilgilerini alırken hata oluştu:", error);
    }
  }

  const getDailyConcert = () => {
    try {
      post("search", { type: 0 }).then((res: any) => {
        setConDai(res.data);
        setConDai_(res.data);
      });
    } catch (e: any) {
      startToast(e, "error");
    }
  };

  const onSearch = (search: string) => {
    if (search) {
      const search_ = search.toLowerCase();
      let filterConDai = conDai_.filter((item: any) =>
        item.name.toLowerCase().match(search_)
      );
      setConDai(filterConDai);
    } else {
      setConDai(conDai_);
    }
  };

  React.useEffect(() => {
    if (route.params?.refresh) {
      getDailyConcert();
      getChatInfo();
      setRefresh(true);
    }
  }, [route.params]);

  useEffect(() => {
    if (showWarning) {
      startToast("Bu modül henüz aktif değil!", "error");
    }
  }, [showWarning]);

  React.useEffect(() => {
    getDailyConcert();
    getChatInfo();
    logUserInfo();

  }, []);

  const fetchAllData = async () => {

    await Promise.all([
      getDailyConcert(),
      getChatInfo(),
      logUserInfo(),
    ]);
};

 React.useEffect(() => {
        fetchAllData();

        socket.on('updateData', () => {
            fetchAllData();
        });

        return () => {
            socket.off('updateData');
        };
    }, []);

  // Görünür öğelerin değişimini izlemek için viewabilityConfig
  const viewabilityConfig = React.useRef({
    itemVisiblePercentThreshold: 50
  }).current;

  // Görünür öğeler değiştiğinde çağrılacak fonksiyon
  const onViewableItemsChanged = React.useRef(({ viewableItems }: { viewableItems: Array<any> }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  React.useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (AnnouncementStore.announcements && AnnouncementStore.announcements.length > 1) {
      interval = setInterval(() => {
        flatListRef.current?.scrollToIndex({
          animated: true,
          index: (currentIndex + 1) % AnnouncementStore.announcements.length,
        });
        setCurrentIndex((prevIndex) => (prevIndex + 1) % AnnouncementStore.announcements.length);
      }, 3000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [AnnouncementStore.announcements, currentIndex]);

  const renderDot = (index: number) => {
    const isActive = index === currentIndex;
    return (
      <View
        key={index}
        style={[
          styles.dot,
          { backgroundColor: isActive ? "green" : "white", width: isActive ? 20 : 10 },
        ]}
      />
    );
  };

  return (
    <View style={styles.main}>
      <SafeAreaView style={styles.safeAreaView}>
        <View style={styles.toastView}>
          <Toast
            type={typeToast}
            subtitle={subtitleToast}
            status={statusToast}
            successColor={green_t1}
          />
        </View>
      </SafeAreaView>

      <Menu
        menuColor={green_t1}
        navigation={navigation}
        menuStatus={menu}
        onMenu={(set: boolean) => setMenu(set)}
      />
      <View style={styles.headerArea} >
      <Header
                  onChangeText={(text: string) => {
                    setIsTyping(text);
                    onSearch(text);
                  }}
                  menuStatus={menu}
                  onMenu={(set: boolean) => setMenu(set)}
                />
      </View>


      {AnnouncementStore.isLoading ? (
        <View style={[styles.topBack, styles.noAnnouncementView]}>
          <Spinner color={white} size={20} mt={Layout.screen.width / 2.8} />
        </View>

      ) : AnnouncementStore.announcements && AnnouncementStore.announcements.length > 1 ? (
        <View style={styles.topBack}>
          <FlatList
            ref={flatListRef}
            data={AnnouncementStore.announcements}
            horizontal
            showsHorizontalScrollIndicator={false}
            pagingEnabled
            style={styles.topBack}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={viewabilityConfig}
            renderItem={({ item }) => (
              <ImageBackground source={{ uri: getImageURL(item?.image) }} style={styles.topBack}>

                <NotifyView
                  navigation={navigation}
                  content={item?.content}
                  header={item?.header}
                  detail={item?.detail}
                  detailId={item?.detailId}
                  bottomDesc="antalya"
                />
              </ImageBackground>
            )}
            keyExtractor={(_item, index) => index.toString()}
          />
          <View style={styles.dotsContainer}>
            {AnnouncementStore.announcements.map((_, index) => renderDot(index))}
          </View>
        </View>
      ) : AnnouncementStore.announcements && AnnouncementStore.announcements.length === 1 ? (
        <ImageBackground
          source={{ uri: getImageURL(AnnouncementStore.announcements[0]?.image) }}
          style={styles.topBack}
        >
          <Header
            onChangeText={(text: string) => {
              setIsTyping(text);
              onSearch(text);
            }}
            menuStatus={menu}
            onMenu={(set: boolean) => setMenu(set)}
          />
          <NotifyView
            navigation={navigation}
            content={AnnouncementStore.announcements[0]?.content}
            header={AnnouncementStore.announcements[0]?.header}
            detail={AnnouncementStore.announcements[0]?.detail}
            detailId={AnnouncementStore.announcements[0]?.detailId}
            bottomDesc="antalya"
          />
        </ImageBackground>
      ) : (
        <View style={[styles.topBack, styles.noAnnouncementView]}>
          <Header
            onChangeText={(text: string) => {
              onSearch(text);
              setIsTyping(text);
            }}
            menuStatus={menu}
            onMenu={(set: boolean) => setMenu(set)}
          />
          <Text style={styles.noAnnouncement}>{MainStore.language.no_notify}</Text>
        </View>
      )}

      <ImageBackground
        source={require("../../assets/root/background.png")}
        resizeMode="cover"
        style={styles.bottomBack}
      >
        <Text style={styles.date}>
          {day} {month} {day_}
        </Text>

        {isTyping ? (
          <View style={styles.isTypingView}>
            {conDai.length >= 1 ? (
              <FlatList
                showsVerticalScrollIndicator={false}
                data={conDai}
                numColumns={2}
                renderItem={({ item, index }) => (
                  <ConcertItem key={index} item={item} navigation={navigation} />
                )}
              />
            ) : (
              <View style={styles.noResultView}>
                <Text style={styles.noResultText}>{MainStore.language.no_result_root}</Text>
              </View>
            )}
          </View>
        ) : (
          <ScrollView style={styles.rootListScrollView}>
            <RootList
              refresh={refresh}
              setRefresh={setRefresh}
              conDai={conDai}
              navigation={navigation}
              showWarning={showWarning}
              setShowWarning={setShowWarning}
              announcements={AnnouncementStore.announcements} // Pass store data
            />
          </ScrollView>
        )}
      </ImageBackground>
      <BottomBar type={1} navigation={navigation} />
    </View>
  );
};

export default Root;

// Styles remain unchanged
const styles = StyleSheet.create({
  main: { flex: 1 },
  safeAreaView: {
    top: 0,
    zIndex: 99999999999,
    position: "absolute",
    alignSelf: "center",
  },
  headerArea:{
    position: 'absolute',
    top: 10,
    zIndex: 10,
    right: 15,
  },
  toastView: {
    width: Layout.screen.width / 1.1,
    zIndex: 9999999,
  },
  topBack: {
    width: Layout.screen.width,
    height: Layout.screen.height / 2.5,
  },
  noAnnouncementView: {
    alignItems: "center",
    backgroundColor: gray_t8,
  },
  noAnnouncement: {
    color: white,
    fontWeight: "bold",
    fontSize: 14,
    textAlign: "center",
    marginTop: 40,
  },
  bottomBack: {
    width: Layout.screen.width,
    height: Layout.screen.height / 1.17,
    top: -Layout.screen.height / 16,
    zIndex: 2,
    paddingTop: Layout.screen.height / 16,
  },
  date: {
    position: "absolute",
    right: 15,
    color: black,
    top: Layout.screen.height / 60,
    fontSize: 16,
    fontFamily: "Helvetica Neue LT Pro",
  },
  isTypingView: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignSelf: "center",
    width: Layout.screen.width / 1.06,
    marginTop: 10,
  },
  noResultView: {
    alignItems: "center",
    width: Layout.screen.width / 1.06,
    paddingTop: 20,
  },
  noResultText: {
    fontWeight: "bold",
    textAlign: "center",
    fontSize: 14,
  },
  rootListScrollView: {
    marginBottom: Dimensions.get("window").height / 4,
  },
  dotsContainer: {
    flexDirection: "row",
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    bottom: -180,
    left: 20,
    top: 20,
  },
  dot: {
    width: 20,
    top: 20,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
});
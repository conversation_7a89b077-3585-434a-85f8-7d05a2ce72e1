import { useNavigation } from "@react-navigation/native";
import React from "react";
import { FlatList, ImageBackground, StyleSheet, Text, TouchableOpacity, View, LayoutAnimation } from "react-native";
import { screens } from "../../../navigation";
import Layout from "../../../constants/Layout";
import NotifyView from "../../../components/NotifyView";
import Menu from "../../../components/Menu";
import Swiper from "react-native-swiper";
import HeaderFive from "../../../components/HeaderFive";
import { black, black_t2, gray_t1, gray_t15, gray_t8, gray_t9, green_t1, kremrengi, red_t1, white } from "../../../constants/Color";
import { getImageURL, post } from "../../../networking/Server";
import DailyItem from "../../../components/DailyItem";
import BottomBar from "../../../components/BottomBar";
import Svg, { Path } from 'react-native-svg';
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import isoWeek from "dayjs/plugin/isoWeek"; // ✅ isoWeek eklentisini ekliyoruz
import { AnnouncementStore } from "../../../stores/AnnouncementStore";

const dayjs = require('dayjs');
dayjs.extend(isoWeek); // ✅ Eklentiyi aktif hale getiriyoruz

const SkeletonLoader = () => {
    return (
        <SkeletonPlaceholder borderRadius={4}>
            <View style={{ flexDirection: "row", flexWrap: "wrap", justifyContent: "space-between" }}>
                {[1, 2, 3, 4, 5, 6].map((_, index) => (
                    <View key={index} style={{ width: Layout.screen.width / 2.3, height: 120, marginBottom: 15 }}>
                        <SkeletonPlaceholder.Item width="100%" height={120} borderRadius={10} />
                    </View>
                ))}
            </View>
        </SkeletonPlaceholder>
    );
};

const DailyActivity: React.FC = (props: any) => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    const [conDai, setConDai] = React.useState(props?.route?.params?.conDai);
    const [conDai_] = React.useState(props?.route?.params?.conDai);

    const [filterType, setFilterType] = React.useState<'daily' | 'weekly'>('daily');
    const todayDayOfWeek = dayjs().isoWeekday(); // Bugünün haftadaki günü (1 = Pazartesi, 7 = Pazar)


    const onSearch = (search: string) => {
        if (search) {
            const search_ = search.toLowerCase();
            let filterConDai = conDai_.filter((item: any) => {
                return (
                    (item.name.toLowerCase()).match(search_)
                );
            })
            setConDai(filterConDai);
        } else {
            setConDai(conDai_);
        }
    }

    
    
    // -- DATE -- //
    const date = dayjs();
    const day = date.format("DD");
    const month = date.format("MMMM");
    const day_ = date.format("dddd");

    const [isLoading, setIsLoading] = React.useState(false);

    const animateTransition = () => {
            LayoutAnimation.configureNext({
                duration: 600, // Daha uzun bir animasyon süresi (ms)
                update: {
                    type: LayoutAnimation.Types.spring, // Spring tipi animasyon daha yumuşak olur
                    springDamping: 0.5, // Damping ile geçişleri yumuşat
                },
                delete: {
                    type: LayoutAnimation.Types.easeInEaseOut,
                    property: LayoutAnimation.Properties.opacity,
                }
            });
        };

    const handleFilterChange = (type: 'daily' | 'weekly') => {

        animateTransition();

        setIsLoading(true); // 📌 Listeyi gizlemek için loading başlat
        setFilterType(type); // 📌 Butonun hemen değişmesi için setState
    
        setTimeout(() => { // 📌 Listeyi güncellemeyi simüle etmek için bekleme süresi
            setIsLoading(false);
        }, 500);
    };
    


    const filteredConDai = React.useMemo(() => {
        return filterType === 'daily'
            ? conDai.filter((item: any) => item.dayofweek === todayDayOfWeek) // Sadece bugüne ait etkinlikleri göster
            : conDai; // Haftalık program için hepsini göster
    }, [filterType, conDai, todayDayOfWeek]);
    
    const filteredAnnouncements = AnnouncementStore.announcements.filter(a => a.type === 2);


    return (
        <View style={styles.main}>

            {/* MENU */}
            <Menu
                menuColor={green_t1}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />

            {/* NOTIFY SWIPER AREA */}
            <View style={styles.swiperMainView}>
                <HeaderFive
                    searchIconColor={gray_t15}
                    logoStyle={false}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menu.png')}
                    backIcon={white}
                    logo={false}
                    navigation={navigation}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    onSearch={onSearch}
                    menuStatus={menu}
                />
                
                    <Swiper
                        dotStyle={styles.dotStyle}
                        activeDotStyle={styles.activeDotStyle}
                        dotColor={white}
                        activeDotColor={green_t1}
                    >
                        {filteredAnnouncements.map((announcement: any, index: React.Key) => (
              <ImageBackground
                key={index}
                source={{ uri: getImageURL(announcement?.image) }}
                style={styles.topBack}
                resizeMode="cover"
              >
<NotifyView
                                    navigation={navigation}
                                    content={announcement?.content}
                                    header={announcement?.header}
                                    detail={announcement?.detail}
                                    detailId={announcement?.detailId}
                                />
              </ImageBackground>
            ))}
                    </Swiper>
              
                <View style={styles.svg}>
                    <Svg width='100%' height={50} viewBox="0 0 370 50">
                      <Path
                        fill="#F4F4F4"
                        d="M0 41H101.5H172.618C177.444 41 182.175 39.6568 186.281 37.1207L197 30.5L214.5 19.5L234.555 7.64904C243.03 2.64155 252.692 0 262.536 0H318.5H390V557H0V41Z"
                      />
                    </Svg>
                </View>
            </View>

            {/* NOTIFY SWIPER AREA */}
            <View style={styles.bottomBack} >
                
                {/* DATE */}
                <Text style={styles.date}>{day} {month} {day_}</Text>

                <View style={styles.filterContainer}>
    <View style={styles.filterContainerInner}>
        <TouchableOpacity 
            style={[styles.filterButton, filterType === 'daily' && styles.activeFilter]}
            onPress={() => handleFilterChange('daily')}
        >
            <Text style={[styles.filterText, filterType === 'daily' && styles.activeFilterText]}>
                Günlük Etkinlikler
            </Text>
        </TouchableOpacity>

        <TouchableOpacity 
            style={[styles.filterButton, filterType === 'weekly' && styles.activeFilter]}
            onPress={() => handleFilterChange('weekly')}
        >
            <Text style={[styles.filterText, filterType === 'weekly' && styles.activeFilterText]}>
                Haftalık Program
            </Text>
        </TouchableOpacity>
    </View>
</View>

    

<View style={styles.whiteArea}>
    {isLoading ? (
        <SkeletonLoader /> // 🔽 Yüklenme sırasında skeleton göster
    ) : filteredConDai && filteredConDai.length > 0 ? (
        <FlatList
            data={filteredConDai}
            showsVerticalScrollIndicator={false}
            columnWrapperStyle={{ marginBottom: 15 }}
            ListFooterComponent={<View style={{ height: 350 }} />}
            numColumns={2}
            renderItem={({ item, index }) => {
                return (
                    <DailyItem
                        key={index}
                        item={item}
                        navigation={navigation}
                    />
                );
            }}
        />
    ) : (
        <View style={styles.noActivityContainer}>
            <Text style={styles.noActivityText}>Aktif etkinlik bulunmadı.</Text>
        </View>
    )}
</View>

            </View>
            <BottomBar navigation={navigation} type={1} />
            
        </View>
    )
}
export default DailyActivity;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1 },
    swiperMainView: {
        width: Layout.screen.width,
        height: Layout.screen.height / 2.5
    },
    svg: {
        width: Layout.screen.width,
        top: -Layout.screen.height / 18.6,
        left:1
        
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Layout.screen.height / 8,
    },
    loadingText: {
        fontSize: 16,
        fontFamily: 'MADE TOMMY',
        color: gray_t1,
        textAlign: 'center'
    },
    noActivityContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Layout.screen.height / 8,
    },
    noActivityText: {
        fontSize: 16,
        fontFamily: 'MADE TOMMY',
        color: gray_t1,
        textAlign: 'center'
    },
    dotStyle: {
        width: 11,
        height: 7,
        borderRadius: 4,
        right: 150,
        bottom: 25
    },
    activeDotStyle: {
        width: 26,
        height: 7,
        borderRadius: 4,
        right: 150,
        bottom: 25
    },
    topBack: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3
    },
    bottomBack: {
        width: Layout.screen.width,
        top: -Layout.screen.height / 16,
        zIndex: 2,
        paddingTop: Layout.screen.height / 16,
        backgroundColor: kremrengi,
    },
    date: {
        position: 'absolute',
        right: 15,
        color: black,
        top: -Layout.screen.height / 25 ,
        fontSize: 16,
        fontFamily: 'Helvetica Neue LT Pro',
        zIndex: 99999,
    },
    whiteArea: {
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        marginTop: 40,
        flexDirection: 'row',
        justifyContent: 'space-between',
        top: -Layout.screen.height / 19,
    },
    filterContainer: {
        marginBottom: 15,
        marginTop: -45,
        alignItems: 'center',

    },
    filterContainerInner: {
        backgroundColor: gray_t9,
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: 315,
        borderRadius: 50,

    },
    filterButton: {
        paddingVertical: 8,
        paddingHorizontal: 13,
        borderRadius: 20,
        borderColor: gray_t1,
        backgroundColor: 'transparent',
    },
    activeFilter: {
 backgroundColor: gray_t8,
        opacity: 0.6, 
     },
    filterText: {
        fontSize: 14,
        color: black_t2,
        fontFamily: 'MADE TOMMY',

    },
    activeFilterText: {
        color: black,
    }
});
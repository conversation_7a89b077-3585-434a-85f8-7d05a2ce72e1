import React, { useEffect } from "react";
import { StyleSheet, View, ImageBackground, Image, TouchableOpacity, ScrollView, Text } from "react-native";
import Layout from "../../../constants/Layout";
import Menu from "../../../components/Menu";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderTwo from "../../../components/HeaderTwo";
import { shadow } from "../../../constants/Shadow";
import { black, blue_t2, gray_t4, green_t1, white, yellow_t1, kremrengi, black_t2, red_t1 } from "../../../constants/Color";
import ConcertList from "../../../components/ConcertList";
import { get, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import dayjs from "dayjs";
import { Spinner } from "native-base";
import { MainStore } from "../../../stores/MainStore";
import YeniAcilmaActivity from "../../../components/YeniAcilmaActivity";


const DailyActivityDetail: React.FC = (props: any) => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- LOADING -- // 
    const [loading, setLoading] = React.useState(false);

    // STATUSSES -- //
    const [hollyTicket, setHollyTicket] = React.useState(false); // 🎯 ANIMATION CIRCLE state
    const [openIndex, setOpenIndex] = React.useState<number | null>(1); // 🎯 Açılan kartın index'ini takip et


    // -- ACTIVITY -- //
    const [activity, setActivity]: any = React.useState({});

    const getDetail = () => {
        setLoading(true);
        get(`daily-events/detail?id=${props.route.params.activityId}`).then((res: any) => {
            if (res.type == "success") {
                setLoading(false);
                setActivity(res.dailyActivity);
            } else {
                setTimeout(() => {
                    navigation.pop()
                }, 2000);
                startToast(res.error, "error");
            }
        });
    };

    useEffect(() => {
        getDetail();
    }, []);

    return (
        <View
            style={styles.main}
        >

            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>

            {/* MENU */}
            <Menu
                menuColor={green_t1}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />

            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderTwo
                    onMenu={(set: boolean) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                    navigation={navigation}
                    leftIcon={black}
                    logo={false}
                    menuIcon={require('../../../assets/header/menu.png')}
                />
            </SafeAreaView>

            {
                loading ?
                    <Spinner style={styles.spinnerLoading} color={green_t1} size={18} />
                    :
                    <ScrollView showsVerticalScrollIndicator={false}>

                        {/* STABIL */}
                        <View style={[shadow, { zIndex: 2 }]}>
                            <Image
                                resizeMode="cover"
                                style={styles.concertImg}
                                source={{ uri: getImageURL(activity?.image) }}
                            />
                        </View>



                        {/* ANIMATION CIRCLE */}
                        {
                            hollyTicket ?
                                <Image
                                    source={require('../../../assets/root/dailyProcessGreen.png')}
                                    style={styles.dailyProcessGreen}
                                />
                                :
                                <Image
                                    source={require('../../../assets/root/dailyProcessBlack.png')}
                                    style={styles.dailyProcessBlack}
                                />
                        }

                        {/* ALL LISTS */}
                        <View style={styles.allListView}>

                            {/* ARTIST */}
                            <Text style={styles.artistName}>{activity?.name}</Text>

                            {/* STAGE */}
                            {
                                activity?.stageId == 1 ?
                                    <Image
                                        source={require('../../../assets/root/atriumgreen.png')}
                                        style={styles.stage}
                                    />
                                    :
                                    <Text style={styles.stageText}>{activity?.stageName}</Text>
                            }

                            <View style={styles.swiperCart} >

                                {/* Açılır-Kapanır Kartlar */}

                    <YeniAcilmaActivity
                        items={[
                            {
                                borderTopLeftRadius: 15,
                                 borderBottomLeftRadius:15,
                                color: green_t1,
                                soltext: "KURALLAR",
                                soltextColor: white,
                                soltextfontsize: 16,
                                kavisrengi: white,
                                content: (
                                    <ScrollView style={styles.rulesScroll} >
                                        <Text style={styles.rulesText}>
                                            {"· 18 yaş sınırı vardır.\n\n" +
                                            "· Etkinlik girişinde bilet kontrolü yapılacaktır.\n\n" +
                                            "· Satın alınan biletlerde iptal, iade ve değişiklik yapılamaz.\n\n" +
                                            "· Konser alanına yiyecek, içecek ve kesici alet sokmak yasaktır.\n\n" +
                                            "· Güvenlik sebebiyle çanta kontrolü yapılacaktır."}
                                        </Text>
                                    </ScrollView>
                                ),
                            },
                            {
                                borderTopRightRadius: 15,
                                borderBottomRightRadius:15,
                                color: white,
                                soltext: "DETAYLAR",
                                soltextColor: black_t2,
                                soltextfontsize: 16,
                                kavisrengi: kremrengi,
                                content: (
                                    <View style={styles.detailsContainer}>
                        <Text style={styles.detailsText}>
                            {dayjs(activity?.date).format("D MMMM YYYY").toUpperCase()}
                        </Text>
                        <Text style={styles.detailsText}>
                        Kapı Açılış Saati: {dayjs(activity?.gateDate).format("HH:mm")}
                        </Text>
                        <Text style={styles.detailsText}>
                            Etkinlik Başlangıcı: {dayjs(activity?.date).format("HH:mm")}
                        </Text>
                        <Text style={[styles.detailsText, { marginTop: 15 }]}>
                            Ücretsiz Giriş
                        </Text>
                    </View>
                                ),
                            },
                        ]}
                        openIndex={openIndex}
            setOpenIndex={(index) => {
                setOpenIndex(index);
                setHollyTicket(index === 0); // 🎯 Eğer "KURALLAR" açılırsa ANIMATION CIRCLE değişsin
            }}
                    />


                            </View>

                           
                            

                        </View>
                    </ScrollView>
            }
            
        </View >
    )
}
export default DailyActivityDetail;

// -- STYLES -- //
const styles = StyleSheet.create({
    concertInfoFirstMain: {
        paddingTop: 30,
        paddingHorizontal: 20,
        height: 295
    },
    concertInfoFirstTitle: {
        fontWeight: 'bold',
        color: white,
        fontSize: 18
    },
    swiperCart: {
        paddingTop: 25,  // 🎯 Üst boşluğu artırdık
        alignSelf: 'center',
    },
    rulesScroll: {
        maxHeight: 230,  // 🎯 Scroll yapılabilmesi için maksimum yükseklik
        paddingHorizontal: 10,  // 🎯 İçerik düzgün hizalansın diye iç boşluk
        width: 260
    },
    rulesText: {
        color: white,
        fontSize: 14,
        textAlign: 'left',  // 🎯 Metni sola yasladık
        lineHeight: 20,  // 🎯 Daha okunaklı olması için satır aralığı
    },
    detailsContainer: {
        paddingHorizontal: 15,  // 🎯 İçerik kenarlara yapışmasın
        paddingVertical: 10,
    },
    detailsText: {
        color: black_t2,
        fontSize: 14,
        fontFamily: 'MADE TOMMY',
        textAlign: 'left',  // 🎯 Metni sola yasladık
        lineHeight: 20,  // 🎯 Daha iyi görünüm
        marginBottom: 8,  // 🎯 Detaylar arasına boşluk ekledik
    },
    concertInfoCardView: {
        borderWidth: 1,
        borderColor: yellow_t1,
        borderRadius: 8,
        height: 44,
        marginTop: 8,
        backgroundColor: white,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8
    },
    concertInfoCardName: { fontSize: 10 },
    concertInfoCardNo: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 2
    },
    concertInfoCardLogo: {
        width: 28,
        height: 17
    },
    concertInfoFirstButtonsView: {
        position: 'absolute',
        bottom: 10,
        alignSelf: 'center'
    },
    concertInfoFirstButton: {
        borderRadius: 15,
        backgroundColor: white,
        height: 40,
        width: Layout.screen.width - 230,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertInfoFirstText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: blue_t2,
    },
    concertInfoSecondMain: {
        width: Layout.screen.width - 195,
        left: -25,
        height: '100%'
    },
    concertInfoSecondAlt: { margin: 20 },
    concertInfoSecondListView: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    concertInfoSecondListLeft: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold',
    },
    concertInfoSecondListRight: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold',
        marginRight: 20
    },
    concertInfoSecondGrayWhiteLine: {
        height: 1,
        marginVertical: 15,
        width: '100%',
        backgroundColor: white
    },
    concertInfoSecondButtonsView: {
        position: 'absolute',
        bottom: 0,
        alignSelf: 'center',
        marginBottom: 10
    },
    concertInfoSecondButton: {
        borderRadius: 15,
        height: 40,
        width: 180,
        backgroundColor: blue_t2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertInfoSecondButtonText: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold'
    },
    concertInfoThirdMain: {
        width: Layout.screen.width - 190,
        left: 15,
        marginVertical: 10,
        flex: 1
    },


    concertInfoThirdPieceAltViewText: {
        fontSize: 24,
        top: -2
    },
    concertInfoThirdPiece: {
        fontSize: 16,
        color: white,
        fontWeight: 'bold',
    },
    main: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        backgroundColor: kremrengi,
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
        alignSelf: 'center'
    },
    concertImg: {
        alignSelf: 'center',
        borderBottomLeftRadius: 204,
        width: 246,
        height: 300,
        borderBottomRightRadius: 204
    },
    artistName: {
        color: black,
        alignSelf: 'center',
        fontSize: 25,
        fontWeight: 'bold',

    },
    stage: {
        height: 10,
        width: 118,
        alignSelf: 'center',
        marginTop: 5
    },
    stageText: {
        fontFamily: 'Helvetica Neue LT Pro',
        textAlign: 'center',
        fontSize: 18,
        marginTop: 5,
        color: green_t1
    },
    safeAreaView: {
        flexGrow: 1,
        zIndex: 1,
        position: 'absolute',
        alignSelf: 'center'
    },
    spinnerLoading: { marginTop: 90 },
    dailyProcessGreen: {
        width: 321,
        height: 161,
        top: 175,
        alignSelf: 'center',
        position: 'absolute',
    },
    dailyProcessBlack: {
        width: 321,
        height: 161,
        top: 175,
        alignSelf: 'center',
        position: 'absolute',
    },
    allListView: {
        marginVertical: 70,
        marginBottom: 50,
        marginTop: 50,
    },
    list: {
        backgroundColor: white,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        height: 295,
        marginVertical: 20,
        width: Layout.screen.width / 1.17,
        marginRight: 20,
        alignSelf: 'center',
    },
    rulesAltView: {
        flex: 1,
        width: Layout.screen.width / 1.5,
        paddingTop: 40,
        marginLeft: 60,
        paddingHorizontal: 10,
        zIndex: -1,
    },
    altText: {
        color: gray_t4,
        fontWeight: 'bold',
        fontSize: 16
    },
    altTextsView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20
    },
    rulesButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: green_t1
    },
    greenFold: {
        height: 295,
        position: 'absolute',
        right: -Layout.screen.width / 20,
        width: 31,
        borderRadius: 10
    },
    rules: {
        fontSize: 16,
        fontWeight: 'bold',
        zIndex: 2,
        color: white,
        position: 'absolute',
        width: 85,
        height: 15,
        right: -45,
        top: 35,
        transform: [{ rotate: '270deg' }]
    },
    winButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: green_t1,
        height: 40,
        width: 180,
        borderRadius: 15,
        alignSelf: 'center',
        justifyContent: 'center'
    },
    hPWhite: {
        height: 31.02,
        width: 28
    },
    win: {
        fontSize: 18,
        fontWeight: 'bold',
        color: white,
        marginLeft: 5
    },
});


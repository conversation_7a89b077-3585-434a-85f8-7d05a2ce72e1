import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  ImageBackground,
  Text,
  Dimensions,
  Animated,
} from "react-native";

const Slider = ({ data }: { data: Array<{ id: string; image: string }> }) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const screenWidth = Dimensions.get("window").width;
  const scrollX = useRef(new Animated.Value(0)).current;

  // Otomatik kaydırma için interval
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % data.length;
      scrollViewRef.current?.scrollTo({
        x: nextIndex * screenWidth,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    }, 3000); // Her 3 saniyede bir kaydır

    return () => clearInterval(interval); // Bileşen unmount olduğunda interval temizlenir
  }, [currentIndex, data.length, screenWidth]);

  // Noktaları render eden yardımcı fonksiyon
  const renderDots = () => {
    return (
      <View style={styles.dotsContainer}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              { backgroundColor: index === currentIndex ? "green" : "gray" },
            ]}
          />
        ))}
      </View>
    );
  };

  // Kaydırma işlemini dinleme
  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / screenWidth);
    setCurrentIndex(index);
  };

  return (
    <View style={styles.container}>
      {/* Slider */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { listener: handleScroll, useNativeDriver: false }
        )}
        scrollEventThrottle={16}
      >
        {data.map((item) => (
          <ImageBackground
            key={item.id}
            source={{ uri: item.image }}
            style={[styles.image, { width: screenWidth }]}
          >
            <Text style={styles.slideText}>Slide {item.id}</Text>
          </ImageBackground>
        ))}
      </ScrollView>

      {/* Noktalar */}
      {renderDots()}
    </View>
  );
};

export default Slider;

const styles = StyleSheet.create({
  container: {
    height: Dimensions.get("window").height / 3,
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  image: {
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  slideText: {
    color: "white",
    fontSize: 20,
    fontWeight: "bold",
  },
  dotsContainer: {
    flexDirection: "row",
    position: "absolute",
    bottom: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
});

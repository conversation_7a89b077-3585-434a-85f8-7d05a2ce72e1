import React, { useState } from "react";
import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView } from "react-native";
import HeaderTwo from "../../../components/HeaderTwo";
import { SafeAreaView } from "react-native-safe-area-context";
import Layout from "../../../constants/Layout";
import { black, blue_t2, blue_t3, green_t1, green_t2, white, yellow_t1, kremrengi, pink } from "../../../constants/Color";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Menu from "../../../components/Menu";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { Spinner } from "native-base";
import { MainStore } from "../../../stores/MainStore";
import BottomBar from "../../../components/BottomBar";
import YeniAcilma from '../../../components/YeniAcilma';
import { HollyPointsStoreInstance as HollyPointsStore } from "../../../stores/HollyPointsStore"; // Store'u import et
import { observer } from "mobx-react-lite";

const HollyPuan: React.FC = observer(() => {
   
    const navigation: any = useNavigation<screens>();

   
    const [menu, setMenu] = React.useState(false);

    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    const [loading, setLoading] = React.useState(false);
    const [useHollyPoints, setUseHollyPoints] = React.useState(0);
    const [hollyPointsValue, setHollyPointsValue] = React.useState(0);
    const [useLoading, setUseLoading] = React.useState(false);

    const { points } = HollyPointsStore;

    const [contract, setContract] = useState('');

    const [header, setHeader] = React.useState(false);

    
    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    // -- || - || + || BUTTONS -- //
    const changeUse = (status: boolean, hollyPoints: number) => {
        if (status) {
            if (useHollyPoints < hollyPoints)
                setUseHollyPoints(useHollyPoints + 1);
        } else {
            if (useHollyPoints > 0)
                setUseHollyPoints(useHollyPoints - 1);
        }
    }

 
    

    // -- GET VALUE OF HOLLYPOINTS IN TURKISH CURRENCY -- //
    const getSettings = () => {
        try {
            get("get-settings").then((res: any) => {
                if (res.type == "success") {
                    setHollyPointsValue(res.hollyPointsValue);
                    setContract(res.contract); // Store the contract text
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 1500);
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 1500);
        }
    }

    const useHollyPoint = () => {
        if (useHollyPoints == 0) {
            startToast("0 değeri gönderilemez.", "error");
            return;
        }
        setUseLoading(true);
        try {
            post("holly-points/use", {
                hollyPoints: useHollyPoints
            }).then((res: any) => {
                if (res.type == "success") {
                    // İşlem başarılıysa HollyPointsStore'daki puanları güncelle
                    HollyPointsStore.fetchHollyPoints();
                    setUseLoading(false);
                    setUseHollyPoints(0); // Kullanılan puanları sıfırla
                    startToast(res.message || "Holly Puanlar başarıyla kullanıldı.", "success");
                } else {
                    setUseLoading(false);
                    // Eğer cüzdan oluşturma hatası varsa vurgula
                    if (res.error === 'Lütfen cüzdan oluşturun!') {
                        startToast("Lütfen önce cüzdan oluşturun!", "error");
                        // İsteğe bağlı: Cüzdan oluşturma sayfasına yönlendirme
                        // setTimeout(() => {
                        //     navigation.navigate('CreateWallet');
                        // }, 2000);
                    } else {
                        startToast(res.error || "Bir şeyler ters gitti.", "error");
                    }
                }
            });
        } catch (e) {
            setUseLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
        }
    }
    

    React.useEffect(() => {
        getSettings();
        // Holly Points store'unu yükle
        HollyPointsStore.fetchHollyPoints();
    }, []);

    // Sayfa focus olduğunda Holly Points'i yeniden yükle
    useFocusEffect(
        React.useCallback(() => {
            HollyPointsStore.fetchHollyPoints();
        }, [])
    );



      const items = [
        { 
          title: 'Soldan1', 
          content: <View style={{ width: Layout.screen.width / 2, alignItems: 'center' }} >
            <Text style={{ left: 8, top: -30, color: white, fontFamily: 'MADE TOMMY', letterSpacing: 1.1 }}>kazancım</Text>
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 10
            }}>
                <Image
                    source={require('../../../assets/root/hPWhite.png')}
                    style={{ height: 40, width: 36.48 }}
                />
                <Text style={{ marginLeft: 10, fontSize: 30, color: white, fontFamily: 'MADE TOMMY' }}>{points?.earnings?.cashier}</Text>
            </View>
        </View>,
          color: blue_t2, 
          kavisrengi: '#D6B769',
          imageSource: require('../../../assets/root/paydikeyNew.png'), 
          imageWidth: 10,
          imageHeight: 100,
          borderTopLeftRadius: 10,
          borderBottomLeftRadius: 10,
        },
        
        { 
          title: 'Soldan2', 
          content: <View style={{ width: Layout.screen.width / 2, alignItems: 'center' }} >
            <Text style={{ top: -30, color: white, fontFamily: 'MADE TOMMY', letterSpacing: 1.1 }}>kazancım</Text>
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 10
            }}>
                <Image
                    source={require('../../../assets/root/hPWhite.png')}
                    style={{ height: 40, width: 36.48 }}
                />
                <Text style={{ marginLeft: 10, fontSize: 30, color: white, fontFamily: 'MADE TOMMY' }}>{points?.earnings?.hollyShop}</Text>
            </View>
        </View>, 
          color: '#D6B769',
          kavisrengi: '#BFA916',
          imageSource: require('../../../assets/root/hollyShopWhiteNew.png'), 
          imageWidth: 10,
          imageHeight: 100,
        },

        { 
          title: 'Soldan3', 
          content: <View style={{ width: Layout.screen.width / 2, alignItems: 'center' }} >
          <Text style={{ top: -30, color: white, fontFamily: 'MADE TOMMY', letterSpacing: 1.1 }}>kazancım</Text>
          <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10
          }}>
              <Image
                    source={require('../../../assets/root/hPWhite.png')}
                    style={{ height: 40, width: 36.48 }}
                />
                <Text style={{ marginLeft: 10, fontSize: 30, color: white, fontFamily: 'MADE TOMMY' }}>{points?.earnings?.hollySnap}</Text>
          </View>
      </View>, 
          color: '#BFA916', 
          kavisrengi: '#ffffff',
          imageSource: require('../../../assets/root/HollySnapWhiteNew.png'), 
          imageWidth: 13,
          imageHeight: 100,
        },
        { 
          title: 'Soldan4', 
          content: <View style={{ width: Layout.screen.width / 2, alignItems: 'center' }} >
          <Text style={{ left: 8, top: -30, color: black, fontFamily: 'MADE TOMMY', letterSpacing: 1.1 }}>kazancım</Text>
          <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10
          }}>
                 <Image
                       source={require('../../../assets/root/hP.png')}
                       style={{ height: 40, width: 36.48 }}
                   />
                   <Text style={{ marginLeft: 10, fontSize: 30, color: green_t1, fontFamily: 'MADE TOMMY' }}>{points?.earnings?.hollyTicket}</Text>
             </View>
         </View>,
          color: 'white',
          kavisrengi: kremrengi,
          imageSource: require('../../../assets/root/hollyticketdikeyNew.png'), 
          imageWidth: 10,
          imageHeight: 110,
          borderTopRightRadius: 10,
          borderBottomightRadius: 10,
        },
      ];


      const itemss = [
        { 
            title: 'Soldan1', 
            content: (
                <View style={{ width: Layout.screen.width / 1.5,  justifyContent: 'center', alignItems: 'flex-start',
                  }}>
                    <ScrollView style={{ height: 170, padding: 10 }}>
                        <View style={{ }}>
                          <Text style={{ fontSize: 14, color: 'white' }}>
                          {contract}
                          </Text>
                        </View>
                      </ScrollView>
                </View>
            ),        
            color: 'green', 
            kavisrengi: 'white',
            soltext: 'kurallar',
            soltextColor: 'white',
            soltextfontsize: 12,
            borderTopLeftRadius: 10,
            borderBottomLeftRadius: 10,
            
        },      
        { 
          title: 'Soldan2', 
          content: <View style={styles.expensesAltView}>
          <View style={styles.expensesAltItemView}>
              <Image
                  source={require('../../../assets/root/hollyticket.png')}
                  style={styles.expensesAltItemImg}
                  resizeMode="contain"
              />
              <View style={styles.expensesAltTwoView}>
                  <Image
                      source={require('../../../assets/root/hP.png')}
                      style={styles.expenseshPLogo}
                      resizeMode="contain"
                  />
                  <Text style={[styles.expensesText, { color: green_t2 }]}>{points?.spendings?.hollyTicket}</Text>
              </View>
          </View>
          <View style={styles.expensesAltItemView}>
              <Image
                  source={require('../../../assets/root/hollyshop.png')}
                  style={styles.expensesAltItemImg}
                  resizeMode="contain"
              />
              <View style={styles.expensesAltTwoView}>
                  <Image
                      source={require('../../../assets/root/hPBrown.png')}
                      style={styles.expenseshPLogo}
                      resizeMode="contain"
                  />
                  <Text style={[styles.expensesText, { color: yellow_t1 }]}>{points?.spendings?.hollyShop}</Text>
              </View>

          </View>
          <View style={styles.expensesAltItemView}>
              <Text style={styles.expensesAltText}>₺ {MainStore.language.expens}</Text>
              <View style={styles.expensesAltTwoView}>
                  <Image
                      source={require('../../../assets/root/hPBlue.png')}
                      style={styles.expenseshPLogo}
                      resizeMode="contain"
                  />
                  <Text style={styles.expensesText}>{points?.spendings?.realMoney}</Text>
              </View>
          </View>
        </View>, 
          color: white,
          kavisrengi: kremrengi,
          soltext: 'harcamalarım',
          soltextColor: black,
          soltextfontsize: 12,
          borderTopRightRadius: 10,
          borderBottomightRadius: 10,
        },
      ];
      
    


      return (
        <View style={styles.main}>
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t2}
                />
            </View>
    
            <Menu
                menuColor={green_t1}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
    
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <View style={[
                    {
                        paddingVertical: 5,
                        zIndex: 10,
                        paddingBottom: 20,
                        width: Layout.screen.width / 1,
                        alignSelf: 'center',
                        paddingHorizontal: 20,
                    },
                    { backgroundColor: header ? 'transparent' : "transparent" }
                ]}>
                    <HeaderTwo
                        onMenu={(set: boolean) => {
                            setMenu(set);
                        }}
                        menuStatus={menu}
                        navigation={navigation}
                        leftIcon={green_t1}
                        logo={require('../../../assets/root/hollyPuanLogo.png')}
                        menuIcon={require('../../../assets/header/menu.png')}
                    />
                </View>
    
                <ScrollView
                    style={styles.scrollview}
                    onScroll={handleScroll}
                    showsVerticalScrollIndicator={false}
                >
                    {/* CURRENT HOLLY PUAN AREA */}
                    <View>
                        <View style={styles.currentPuanView}>
                            <Text style={styles.currentPuanText}>{MainStore.language.current_score}</Text>
                            <View style={styles.currentPuanAltView}>
                                <Image
                                    source={require('../../../assets/root/hP.png')}
                                    style={styles.currentPuan}
                                    resizeMode="contain"
                                />
                                <Text style={styles.cP}>{points?.hollyPoints}</Text>
                            </View>
                            <Image
                                source={require('../../../assets/root/white-fold.png')}
                                style={styles.whiteFold}
                                resizeMode="cover"
                            />
                            <Image
                                source={require('../../../assets/root/hollyPuanLogo.png')}
                                style={styles.hollyPuanLogo}
                            />
                        </View>
                    </View>
    
                    {/* USE ₺ OR USE HOLLY PUAN AREA */}
                    <View style={styles.useView}>
                        <TouchableOpacity
                            onPress={() => {
                                useHollyPoint();
                            }}
                            style={styles.useLeftView}
                        >
                            {
                                useLoading ?
                                    <Spinner color={white} size={16} />
                                    :
                                    <Text style={styles.useLeftOneText}><Text style={styles.useLeftTwoText}>{hollyPointsValue * useHollyPoints} ₺</Text> Cüzdana Aktar</Text>
                            }
                        </TouchableOpacity>
                        <View style={styles.useRightView}>
                            <TouchableOpacity
                                onPress={() => {
                                    changeUse(false, points?.hollyPoints)
                                }}
                                style={styles.useRightLeftView}
                            >
                                <Text style={styles.useRightLeftText}>-</Text>
                            </TouchableOpacity>
                            <Text style={styles.useRightCenterText}>{useHollyPoints}</Text>
                            <TouchableOpacity
                                onPress={() => {
                                    changeUse(true, points?.hollyPoints)
                                }}
                                style={styles.useRightRightView}
                            >
                                <Text style={styles.useRightRightText}>+</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
    
                    {/* ALL EXPENSES */}
                    <View style={[{ marginBottom: 120 }]}>
                        <View style={styles.allEarningsView}>
                            <YeniAcilma items={items} />
                        </View>
                        <View style={styles.allEarningsView}>
                            <YeniAcilma items={itemss} />
                        </View>
                    </View>
                </ScrollView>
                
                <BottomBar navigation={navigation} type={1} />
            </SafeAreaView>
        </View>
    );
});

export default HollyPuan;

// -- STYLES -- //
const styles = StyleSheet.create({
    scrollview:{
        height: 100,
    },
    main: {
        flex : 1,
        backgroundColor: kremrengi
    },
    safeAreaView: { flexGrow: 1 },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    currentPuanView: {
        backgroundColor: white,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        height: 183,
        justifyContent: 'center',
        marginVertical: 20,
        width: Layout.screen.width / 1.17,
        marginRight: 20,
        alignSelf: 'center',
        alignItems: 'center'
    },
    currentPuanText: {
        fontSize: 16,
        position: 'absolute',
        top: 15,
        color: black,
        fontFamily: 'MADE TOMMY',
        letterSpacing: 1.1
    },
    currentPuanAltView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    currentPuan: {
        height: 60.48,
        width: 67,
        marginTop: 10
    },
    cP: {
        color: green_t2,
        fontSize: 40,
        marginTop: 5,
        marginLeft: 5,
        fontFamily: 'MADE TOMMY'
    },
    whiteFold: {
        height: 183,
        position: 'absolute',
        right: -Layout.screen.width / 20,
        width: Layout.screen.width / 14,
        borderRadius: 10
    },
    hollyPuanLogo: {
        position: 'absolute',
        width: 76,
        height: 13,
        right: -46,
        top: 47,
        transform: [{ rotate: '270deg' }]
    },
    useView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    useLeftView: {
        backgroundColor: pink,
        height: 40,
        width: Layout.screen.width / 2.3,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    useLeftOneText: {
        fontFamily: 'MADE TOMMY',
        color: white,
        letterSpacing: 1.05
    },
    useLeftTwoText: { fontSize: 14 },
    useRightView: {
        backgroundColor: white,
        height: 40,
        borderWidth: 1,
        borderColor: green_t1,
        width: Layout.screen.width / 2.3,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
    },
    useRightLeftView: {
        backgroundColor: green_t1,
        width: '25%',
        height: 34,
        marginLeft: 2,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center'
    },
    useRightLeftText: {
        fontWeight: 'bold',
        color: white,
        fontSize: 24
    },
    useRightCenterText: {
        color: green_t2,
        fontFamily: 'MADE TOMMY',
        fontSize: 20
    },
    useRightRightView: {
        backgroundColor: green_t1,
        width: '25%',
        height: 34,
        marginRight: 2,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center'
    },
    useRightRightText: {
        fontWeight: 'bold',
        color: white,
        fontSize: 24
    },
    allEarningsView: {
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        height: 200,
        marginVertical: 20,
        alignSelf: 'center',
    },
    expensesAltView: {
        flexDirection: 'row',
        width: Layout.screen.width / 1.4,
        justifyContent: 'space-between'
    },
    expensesAltItemView: {
        alignItems: 'center',
        padding: 10
    },
    expensesAltText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: blue_t2
    },
    expensesAltTwoView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    expensesAltItemImg: {
        height: 13,
        width: 72
    },
    expenseshPLogo: {
        height: 35,
        marginTop: 10,
        width: 31
    },
    expensesText: {
        marginLeft: 5,
        color: blue_t3,
        marginTop: 8,
        fontSize: 20,
        fontFamily: 'MADE TOMMY'
    },
});
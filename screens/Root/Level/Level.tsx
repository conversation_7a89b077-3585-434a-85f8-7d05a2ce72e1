import { useNavigation } from "@react-navigation/native";
import React from "react";
import { StyleSheet, View, ImageBackground, TouchableOpacity, Image, ScrollView, Text } from "react-native";
import Layout from "../../../constants/Layout";
import { black, black_t3, gray_t17, green_t1, white, krem<PERSON><PERSON> } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { get } from "../../../networking/Server";
import { BackIcon } from "../../../components/Svgs";


const Level = () => {


    // -- NAVIGATION -- //
    const navigation: any = useNavigation();

    const [modal, setModal] = React.useState(false);

    const insets = useSafeAreaInsets();

    const [levels, setLevels] = React.useState([]);
    const [level, setLevel] = React.useState(0);
    const [hollyPoints, setHollyPoints] = React.useState([]);
    const [completes, setCompletes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    const getLevel = () => {
        try {
            get("level").then((res: any) => {
                if (res.type == 'success') {
                    setLoading(false);
                    setCompletes(res.complete);
                    setHollyPoints(res.hollyPoints);
                    setLevels(res.levelData);
                    setLevel(res.level);
                } else {
                    navigation.pop();
                }
            });
        } catch (e) {
            navigation.pop();
        }
    }

    React.useEffect(() => {
        getLevel();
    }, []);

    return (
        <View
            style={styles.main}
        >

            {/* HEADER */}
            <View style={[styles.header, { zIndex: 8, marginTop: Math.max(insets.bottom, 0) ? 70 : 20, }]}>
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                >
                    <BackIcon
                        size={25}
                        color={black}
                    />

                </TouchableOpacity>
                <Image
                    source={require('../../../assets/root/level.png')}
                    style={styles.levelLogo}
                    resizeMode='cover'
                />
            </View>

            <ScrollView style={{ marginTop: Math.max(insets.bottom, 0) ? 110 : 40, }}>

                {
                    levels.map((item: any, index: React.Key | number | any) => {
                        if (level == index)
                            return (
                                <>
                                    <TouchableOpacity

                                        onPress={() => {
                                            setModal(!modal)
                                        }}
                                        style={[styles.touchOne, shadow]}
                                    >
                                        <View style={styles.lLogo}>
                                            <Image
                                                source={require('../../../assets/root/lLogo.png')}
                                                style={styles.lLogoImg}
                                            />
                                        </View>
                                        <View>
                                            <Text style={styles.levelTitle}>Level {index + 1}</Text>
                                            <Text style={styles.levelWinText}>{hollyPoints[index]} Holly Puan kazandın!</Text>
                                        </View>
                                        <Image
                                            source={require('../../../assets/root/hPBackgroundBlur.png')}
                                            style={styles.hPBackgroundBlur}

                                        />
                                    </TouchableOpacity>
                                    <View style={styles.whiteAreaView}>
                                        <Text style={styles.whiteAreaText}>Tüm görevleri tamamladın ve {index + 1}. seviyenin kilidini açtın</Text>
                                        {item.map((task: any, index: any) => (
                                            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                                                <Text key={index} style={[styles.process, { textDecorationLine: task.complete == true ? 'line-through' : 'none' }]}>{task.task}</Text>
                                                {task.target != 1 && <Text style={{ fontSize: 12, color: white, marginRight: 12 }}>{task.value}/{task.target}</Text>}
                                            </View>
                                        ))}
                                    </View>
                                </>
                            )
                        else if (completes[index] == false) {
                            return (
                                <View style={[styles.touchOne, { backgroundColor: '#e9e9e9', justifyContent: 'space-between' }, shadow]}>
                                    <View style={[styles.lLogoTwo, shadow]}>
                                        <Image
                                            source={require('../../../assets/root/lLogo.png')}
                                            style={styles.lLogoImgTwo}
                                        />
                                    </View>
                                    <Text style={[styles.levelTitle, { marginLeft: 20, fontSize: 15 }]}>Level {index + 1}</Text>
                                    <Image
                                        style={{
                                            width: 42,
                                            marginRight: 15,
                                            height: 47,
                                        }}
                                        resizeMode="contain"
                                        source={require('../../../assets/root/closeLock.png')}
                                    />
                                </View>
                            )
                        } else {
                            return (
                                <View style={[styles.touchOne, { backgroundColor: '#e9e9e9', justifyContent: 'space-between' }, shadow]}>
                                    <View style={[styles.lLogoTwo, shadow]}>
                                        <Image
                                            source={require('../../../assets/root/lLogo.png')}
                                            style={styles.lLogoImgTwo}
                                        />
                                    </View>
                                    <Text style={[styles.levelTitle, { marginLeft: 20, fontSize: 15 }]}>Level {index + 1}</Text>
                                    <Image
                                        style={{
                                            width: 42,
                                            marginRight: 15,
                                            height: 47,
                                        }}
                                        resizeMode="contain"
                                        source={require('../../../assets/root/openLock.png')}
                                    />
                                </View>
                            )
                        }
                    })
                }

            </ScrollView>
        </View>
    )
}
export default Level;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexGrow: 1,
        backgroundColor: kremrengi
    },
    header: {
        position: 'absolute',
        flexDirection: 'row',
        alignItems: 'center',
        left: 20,
        marginTop: 20,
    },
    leftWhiteIcon: {
        width: 17.5,
        height: 23
    },
    levelLogo: {
        height: 29,
        marginLeft: 20,
        width: 109,
        marginBottom: 10,
    },
    touchOne: {
        marginTop: 30,
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        backgroundColor: gray_t17,
        borderRadius: 6,
        minHeight: 73,
        flexDirection: 'row',
        alignItems: 'center',
    },
    lLogo: {
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center'
    },
    lLogoTwo: {
        width: 27,
        height: 27,
        borderRadius: 13.5,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        left: -13.5,
        backgroundColor: '#efefef'
    },
    lLogoImg: {
        width: 20,
        height: 26
    },
    lLogoImgTwo: {
        width: 12.4,
        height: 16
    },
    levelTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: black_t3
    },
    levelWinText: {
        fontSize: 12,
        color: black_t3,
        fontWeight: 'bold',
        marginTop: 3
    },
    hPBackgroundBlur: {
        width: 26,
        height: 58.5,
        position: 'absolute',
        right: 0
    },
    whiteAreaView: {
        minHeight: 16,
        alignSelf: 'center',
        width: Layout.screen.width / 1.4,
        backgroundColor: green_t1,
        borderBottomLeftRadius: 10,
        borderBottomRightRadius: 10
    },
    whiteAreaText: {
        fontSize: 9,
        textAlign: 'center',
        alignSelf: 'center',
        color: white,
        fontWeight: 'bold'
    },
    process: {
        fontSize: 12,
        color: white,
        margin: 5
    },
});
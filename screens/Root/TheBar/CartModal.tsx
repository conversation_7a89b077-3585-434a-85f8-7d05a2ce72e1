import React, { useState } from 'react';
import {
    View,
    Text,
    Modal,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    Image,
    TouchableWithoutFeedback,
    Dimensions,
    Alert,
    ActivityIndicator
  } from 'react-native';
import { useCart } from './CartContext';
import { useOrder } from './OrderContext';
import { colors } from './theme';
import { black_t2, kremrengi } from '../../../constants/Color';
import OrderTrackingModal from './OrderTrackingModal';

const { height } = Dimensions.get('window');

interface CartModalProps {
  visible: boolean;
  onClose: () => void;
}

const CartModal: React.FC<CartModalProps> = ({ visible, onClose }) => {
  const { cartItems, removeFromCart, updateQuantity, getTotalPrice, clearCart, placeOrder, qrcode, tableId } = useCart();
  const { setActiveOrder } = useOrder();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('cash'); // Varsayılan ödeme yöntemi
  const [showOrderTracking, setShowOrderTracking] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState<number | null>(null);

  const handleCheckout = async () => {
    if (!qrcode || !tableId) {
      Alert.alert('Hata', 'QR kod veya Masa ID bulunamadı!');
      return;
    }
    try {
      setIsLoading(true);
      // Sabit QR kod ve masa ID değerlerini kullanıyoruz
      // Gerçek uygulamada bu değerler dinamik olarak alınmalıdır
      

      const result = await placeOrder(qrcode, tableId, selectedPaymentMethod);

      setIsLoading(false);

      if (result.success) {
        // Aktif siparişi OrderContext'e kaydet
        if (result.orderId && qrcode && tableId) {
          setActiveOrder({
            orderId: result.orderId,
            qrcode: qrcode,
            status: 'created',
            date: new Date().toISOString(),
            tableId: tableId,
            totalAmount: getTotalPrice()
          });
        }

        // Sipariş başarılı, tracking modal'ını aç
        setCurrentOrderId(result.orderId || null);
        setShowOrderTracking(true);
        onClose(); // Cart modal'ını kapat
      } else {
        Alert.alert('Hata', result.message);
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Hata', 'Sipariş gönderilirken bir hata oluştu');
      console.error('Sipariş hatası:', error);
    }
  };

  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        visible={visible}
        onRequestClose={onClose}
      >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay} />
      </TouchableWithoutFeedback>

      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Sepetim</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>X</Text>
          </TouchableOpacity>
        </View>

        {cartItems.length === 0 ? (
          <View style={styles.emptyCartContainer}>
            <Text style={styles.emptyCartText}>Sepetiniz boş</Text>
          </View>
        ) : (
          <>
            <View style={styles.cartItemsWrapper}>
              <ScrollView
                style={styles.cartItemsContainer}
                contentContainerStyle={styles.cartItemsContent}
                showsVerticalScrollIndicator={false}
              >
                {cartItems.map(item => (
                  <View key={item.id} style={styles.cartItem}>
                    {item.image ? (
                      <Image
                        source={{ uri: `https://backendpos.hollystone.com.tr${item.image}` }}
                        style={styles.cartItemImage}
                      />
                    ) : (
                      <View style={styles.cartItemImage} />
                    )}

                    <View style={styles.cartItemContent}>
                      <Text style={styles.cartItemTitle}>{item.title}</Text>

                      {/* Varyasyon gösterimi */}
                      {item.variant && (
                        <Text style={styles.cartItemVariant}>
                          Seçenek: {item.variant.title}
                        </Text>
                      )}

                      {/* Ekstralar gösterimi */}
                      {item.addons && item.addons.length > 0 && (
                        <Text style={styles.cartItemAddons}>
                          Ekstralar: {item.addons.map(addon => addon.title).join(', ')}
                        </Text>
                      )}

                      <Text style={styles.cartItemPrice}>{parseFloat(item.price) * item.quantity} TL</Text>

                      <View style={styles.quantityContainer}>
                        <TouchableOpacity
                          onPress={() => updateQuantity(item.id, item.quantity - 1)}
                          style={styles.quantityButton}
                        >
                          <Text style={styles.quantityButtonText}>-</Text>
                        </TouchableOpacity>

                        <Text style={styles.quantityText}>{item.quantity}</Text>

                        <TouchableOpacity
                          onPress={() => updateQuantity(item.id, item.quantity + 1)}
                          style={styles.quantityButton}
                        >
                          <Text style={styles.quantityButtonText}>+</Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    <TouchableOpacity
                      onPress={() => removeFromCart(item.id)}
                      style={styles.removeButton}
                    >
                      <Image
                        source={require("../../../assets/root/delete.png")}
                        style={styles.deleteIcon}
                      />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>

            <View style={styles.totalContainer}>
              <Text style={styles.totalText}>Toplam</Text>
              <Text style={styles.totalPrice}>{getTotalPrice().toFixed(2)} TL</Text>
            </View>

            {/* Ödeme Yöntemi Seçimi */}
            <View style={styles.paymentMethodContainer}>
              <Text style={styles.paymentMethodTitle}>Ödeme Yöntemi:</Text>

              <View style={styles.paymentMethodOptions}>
                {/* PayCard ile Ödeme */}
                <TouchableOpacity
                  style={[
                    styles.paymentMethodOption,
                    selectedPaymentMethod === 'paycard' && styles.selectedPaymentMethod
                  ]}
                  onPress={() => setSelectedPaymentMethod('paycard')}
                >
                  <View style={styles.paymentMethodContent}>
                    <View style={[
                      styles.radioButton,
                      selectedPaymentMethod === 'paycard' && styles.radioButtonSelected
                    ]} />
                    <Text style={styles.paymentMethodText}>HollyPayCard ile Ödeme</Text>
                  </View>
                </TouchableOpacity>

                {/* Wallet ile Ödeme */}
                <TouchableOpacity
                  style={[
                    styles.paymentMethodOption,
                    selectedPaymentMethod === 'wallet' && styles.selectedPaymentMethod
                  ]}
                  onPress={() => setSelectedPaymentMethod('wallet')}
                >
                  <View style={styles.paymentMethodContent}>
                    <View style={[
                      styles.radioButton,
                      selectedPaymentMethod === 'wallet' && styles.radioButtonSelected
                    ]} />
                    <Text style={styles.paymentMethodText}>Cüzdan ile Ödeme</Text>
                  </View>
                </TouchableOpacity>

                {/* Nakit/Kredi Kartı ile Ödeme */}
                <TouchableOpacity
                  style={[
                    styles.paymentMethodOption,
                    selectedPaymentMethod === 'cash' && styles.selectedPaymentMethod
                  ]}
                  onPress={() => setSelectedPaymentMethod('cash')}
                >
                  <View style={styles.paymentMethodContent}>
                    <View style={[
                      styles.radioButton,
                      selectedPaymentMethod === 'cash' && styles.radioButtonSelected
                    ]} />
                    <Text style={styles.paymentMethodText}>Masa'da Nakit/Kredi Kartı</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={handleCheckout}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text style={styles.checkoutButtonText}>Siparişi Gönder</Text>
              )}
            </TouchableOpacity>
          </>
        )}
      </View>
      </Modal>

      {/* Sipariş Takip Modalı */}
      <OrderTrackingModal
        visible={showOrderTracking}
        onClose={() => setShowOrderTracking(false)}
        orderId={currentOrderId}
        qrcode={qrcode}
      />
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  quantityButtonText:{
    color: 'black',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center'
  },
  closeButtonText: {
    color: 'red',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    textAlignVertical: 'center'
  },

  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    maxHeight: height * 0.8, // Ekran yüksekliğinin %80'i
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 5,
    paddingLeft: 10,
    paddingRight: 10,
    alignContent: 'flex-end',
    alignItems: 'flex-end',
    backgroundColor: kremrengi,
    borderRadius: 30,
  },
  emptyCartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyCartText: {
    marginTop: 10,
    fontSize: 16,
    color: colors.textSecondary,
  },
  cartItemsWrapper: {
    // Esnek bir yükseklik ataması için flex kullanma
    minHeight: 350,
    maxHeight: height * 0.8, // Ekran yüksekliğinin en fazla %50'si
  },
  cartItemsContainer: {
    flex: 1,
  },
  cartItemsContent: {
    padding: 15,
    paddingBottom: 5, // Alt boşluğu azalttık
  },
  cartItem: {
    flexDirection: 'row',
    padding: 10,
    marginBottom: 10,
    backgroundColor: colors.cardBackground,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  cartItemImage: {
    width: 60,
    height: 60,
    borderRadius: 5,
    backgroundColor: colors.imagePlaceholder,
  },
  cartItemContent: {
    flex: 1,
    marginLeft: 10,
  },
  cartItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 5,
  },
  cartItemVariant: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: 2,
  },
  cartItemAddons: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 10,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    backgroundColor: colors.categoryLabelBackground,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    marginHorizontal: 10,
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  removeButton: {
    justifyContent: 'center',
    padding: 5,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  totalPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  checkoutButton: {
    backgroundColor: colors.primary,
    marginHorizontal: 15,
    marginTop: 5, // Üstteki elemana biraz daha yaklaştırdık
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  deleteIcon: {
    width: 20,
    height: 20,
  },
  // Ödeme yöntemi stilleri
  paymentMethodContainer: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 10,
  },
  paymentMethodOptions: {
    gap: 8,
  },
  paymentMethodOption: {
    padding: 12,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.cardBackground,
  },
  selectedPaymentMethod: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 2,
    borderColor: colors.border,
    backgroundColor: 'transparent',
    marginRight: 10,
  },
  radioButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  paymentMethodText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
  },
});

export default CartModal;
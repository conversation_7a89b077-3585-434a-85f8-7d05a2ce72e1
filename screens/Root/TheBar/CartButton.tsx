import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View, Image } from 'react-native';
import { useCart } from './CartContext';
import { colors } from './theme';

interface CartButtonProps {
  onPress: () => void;
}

const CartButton: React.FC<CartButtonProps> = ({ onPress }) => {
  const { getTotalItems, getTotalPrice } = useCart();
  
  const totalItems = getTotalItems();
  
  // Sepet boşsa butonu gösterme
  if (totalItems === 0) {
    return null;
  }
  
  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.8}>
      <View style={styles.iconContainer}>
        <Image
            style={styles.basketIcon}
            resizeMode="contain"
            source={require('../../../assets/header/basket-white.png')}
        />
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{totalItems}</Text>
        </View>
      </View>
       
      <Text style={styles.text}>Sepeti Görüntüle</Text>
      
      <Text style={styles.price}>{getTotalPrice().toFixed(2)} TL</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: colors.primary,
    borderRadius: 10,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'red',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  price: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  basketIcon: {
    height: 23,
    width: 23,
  },
});

export default CartButton;
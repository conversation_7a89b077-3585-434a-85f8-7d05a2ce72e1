import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ActiveOrder {
  orderId: number;
  qrcode: string;
  status: 'created' | 'completed' | 'cancelled';
  date: string;
  tableId: number;
  totalAmount: number;
}

interface OrderContextType {
  activeOrder: ActiveOrder | null;
  setActiveOrder: (order: ActiveOrder | null) => void;
  clearActiveOrder: () => void;
  hasActiveOrder: () => boolean;
  updateOrderStatus: (status: 'created' | 'completed' | 'cancelled') => void;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

export const OrderProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activeOrder, setActiveOrderState] = useState<ActiveOrder | null>(null);

  // AsyncStorage'dan aktif siparişi yükle
  useEffect(() => {
    loadActiveOrder();
  }, []);

  const loadActiveOrder = async () => {
    try {
      const storedOrder = await AsyncStorage.getItem('activeOrder');
      if (storedOrder) {
        const order = JSON.parse(storedOrder);
        setActiveOrderState(order);
      }
    } catch (error) {
      console.error('Aktif sipariş yüklenirken hata:', error);
    }
  };

  const setActiveOrder = async (order: ActiveOrder | null) => {
    try {
      if (order) {
        await AsyncStorage.setItem('activeOrder', JSON.stringify(order));
      } else {
        await AsyncStorage.removeItem('activeOrder');
      }
      setActiveOrderState(order);
    } catch (error) {
      console.error('Aktif sipariş kaydedilirken hata:', error);
    }
  };

  const clearActiveOrder = async () => {
    try {
      await AsyncStorage.removeItem('activeOrder');
      setActiveOrderState(null);
    } catch (error) {
      console.error('Aktif sipariş silinirken hata:', error);
    }
  };

  const hasActiveOrder = () => {
    return activeOrder !== null; // Her zaman son siparişi göster
  };

  const updateOrderStatus = async (status: 'created' | 'completed' | 'cancelled') => {
    if (activeOrder) {
      const updatedOrder = { ...activeOrder, status };
      setActiveOrderState(updatedOrder); // State'i hemen güncelle

      try {
        await AsyncStorage.setItem('activeOrder', JSON.stringify(updatedOrder));
      } catch (error) {
        console.error('Sipariş durumu güncellenirken hata:', error);
      }

      // Eğer sipariş tamamlandı veya iptal edildiyse, belirli bir süre sonra temizle
      if (status === 'completed' || status === 'cancelled') {
        setTimeout(() => {
          clearActiveOrder();
        }, 30000); // 30 saniye sonra temizle
      }
    }
  };

  return (
    <OrderContext.Provider
      value={{
        activeOrder,
        setActiveOrder,
        clearActiveOrder,
        hasActiveOrder,
        updateOrderStatus
      }}
    >
      {children}
    </OrderContext.Provider>
  );
};

export const useOrder = (): OrderContextType => {
  const context = useContext(OrderContext);
  if (!context) {
    throw new Error('useOrder must be used within an OrderProvider');
  }
  return context;
};

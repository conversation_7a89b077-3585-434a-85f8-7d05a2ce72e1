export const colors = {
    primary: '#00731C',        // Primary green color
    secondary: '#6B7280',      // Secondary gray color
    background: '#F4F4F4',     // Light beige background (updated from e0d8cd)
    cardBackground: '#FFFFFF', // White card background
    text: '#1F2937',           // Dark text color
    textSecondary: '#6B7280',  // Secondary text color
    border: '#E5E7EB',         // Border color
    error: '#DC2626',          // Error red color
    errorBackground: '#FEE2E2', // Light red background for errors
    success: '#059669',        // Success green color
    imagePlaceholder: '#F3F4F6', // Light gray for image placeholders
    categoryLabelBackground: '#F3F4F6', // Background for category labels
  };
  
  export const fonts = {
    // You can define font families here if you are using custom fonts
    regular: 'System',
    medium: 'System',
    bold: 'System',
  };
  
  export interface Shadow {
    shadowColor: string;
    shadowOffset: {
      width: number;
      height: number;
    };
    shadowOpacity: number;
    shadowRadius: number;
    elevation: number;
  }
  
  export const shadows: {
    small: Shadow;
    medium: Shadow;
    large: Shadow;
  } = {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 3.84,
      elevation: 3,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.2,
      shadowRadius: 5,
      elevation: 5,
    },
  };
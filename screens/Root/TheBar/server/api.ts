import { ApiResponse } from '../types';

export const fetchMenuData = async (qrcode: string, tableId: string): Promise<ApiResponse> => {
  const apiUrl = `https://backendpos.hollystone.com.tr/api/v1/qrmenu/${qrcode}${tableId ? `?tableId=${tableId}` : ''}`;
  console.log('Fetching data from:', apiUrl);
  
  const response = await fetch(apiUrl);

  
  if (!response.ok) {
    const errorText = await response.text();
    console.error('API Error Response:', errorText);
    throw new Error(`API request failed with status: ${response.status}`);
  }
  
  const contentType = response.headers.get("content-type");
  
  if (contentType && contentType.includes("application/json")) {
    return response.json();
  } else {
    const text = await response.text();
    console.error("Non-JSON response:", text);
    throw new Error("API did not return JSON");
  }
};
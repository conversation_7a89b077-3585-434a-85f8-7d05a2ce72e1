// CartContext.tsx - Sepet için Context API
import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import io from 'socket.io-client';

// Socket.IO bağlantısı
const socket = io('https://backendpos.hollystone.com.tr', {
  autoConnect: false,
  rejectUnauthorized: false,
});

// Socket bağlantısını başlat
const initSocket = () => {
  if (!socket.connected) {
    socket.connect();
  }
};

// Yeni sipariş bildirimi gönder
const sendNewOrderEvent = (qrcode: string) => {
  if (socket.connected) {
    socket.emit('new_qrorder_backend', {}, qrcode);
  } else {
    // Bağlantı yoksa önce bağlan, sonra eventi gönder
    initSocket();
    socket.emit('new_qrorder_backend', {}, qrcode);
  }
};
import { MenuItem } from './types';

interface CartItem extends MenuItem {
  quantity: number;
  variant_id?: number | null;
  variant?: any;
  addons_ids?: number[];
  addons?: any[];
  finalPrice?: number;
  notes?: string | null;
}

interface CartContextType {
  cartItems: CartItem[];
  qrcode: string | null;
  tableId: number | null;
  setQrcode: (qrcode: string) => void;
  setTableId: (tableId: number) => void;
  addToCart: (item: MenuItem & { variant_id?: number | null; variant?: any; addons_ids?: number[]; addons?: any[]; finalPrice?: number; notes?: string | null }) => void;
  removeFromCart: (itemId: number) => void;
  updateQuantity: (itemId: number, quantity: number) => void;
  clearCart: () => void;
  getTotalPrice: () => number;
  getTotalItems: () => number;
  placeOrder: (qrcode: string, tableId: number, paymentMethod: string) => Promise<{ success: boolean; message: string; orderId?: number }>;


}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [qrcode, setQrcode] = useState<string | null>(null);
  const [tableId, setTableId] = useState<number | null>(null);

  console.log(tableId)


  // Component mount olduğunda socket bağlantısını başlat
  useEffect(() => {
    initSocket();

    // Component unmount olduğunda bağlantıyı kapat
    return () => {
      if (socket.connected) {
        socket.disconnect();
      }
    };
  }, []);

  const addToCart = (item: MenuItem & { variant_id?: number | null; variant?: any; addons_ids?: number[]; addons?: any[]; finalPrice?: number; notes?: string | null }) => {
    // Aynı ürün, aynı varyasyon ve aynı eklentilerle sepette varsa miktarını artır
    const existingItemIndex = cartItems.findIndex(cartItem =>
      cartItem.id === item.id &&
      cartItem.variant_id === item.variant_id &&
      JSON.stringify(cartItem.addons_ids?.sort()) === JSON.stringify(item.addons_ids?.sort())
    );

    if (existingItemIndex > -1) {
      const updatedCartItems = [...cartItems];
      updatedCartItems[existingItemIndex].quantity += 1;
      setCartItems(updatedCartItems);
    } else {
      // Yoksa yeni öğe olarak ekle
      setCartItems([...cartItems, {
        ...item,
        quantity: 1,
        variant_id: item.variant_id || null,
        variant: item.variant || null,
        addons_ids: item.addons_ids || [],
        addons: item.addons || [],
        finalPrice: item.finalPrice || parseFloat(item.price),
        notes: item.notes || null
      }]);
    }
  };

  const removeFromCart = (itemId: number) => {
    setCartItems(cartItems.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    const updatedCartItems = cartItems.map(item =>
      item.id === itemId ? { ...item, quantity } : item
    );
    setCartItems(updatedCartItems);
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => {
      const itemPrice = item.finalPrice || parseFloat(item.price);
      return total + (itemPrice * item.quantity);
    }, 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const placeOrder = async (qrcode: string, tableId: number, paymentMethod: string) => {
    console.log(tableId)
    try {
      const orderData = {
        cartItems: cartItems.map(item => ({
          id: item.id,
          variant_id: item.variant_id || null,
          price: item.finalPrice || parseFloat(item.price),
          quantity: item.quantity || 1,
          notes: null,
          addons_ids: item.addons_ids || []
        })),
        customer: {
          name: "Guest",
          phone: ""
        },
        customerType: "WALKIN",
        deliveryType: null,
        tableId: tableId,
        paymentMethod: paymentMethod
      };

      console.log('Sipariş veriliyor:', JSON.stringify(orderData, null, 2));

      const response = await fetch(`https://backendpos.hollystone.com.tr/api/v1/qrmenu/${qrcode}/place-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Sipariş hatası:', errorText);
        throw new Error('Sipariş gönderilirken bir hata oluştu');
      }

      const responseData = await response.json();
      console.log('Sipariş yanıtı:', responseData);

      // Sipariş başarılı ise sepeti temizle
      clearCart();

      // Socket üzerinden yeni sipariş bildirimi gönder
      sendNewOrderEvent(qrcode);

      return {
        success: true,
        message: 'Sipariş başarıyla gönderildi',
        orderId: responseData.orderId
      };
    } catch (error) {
      console.error('Sipariş hatası:', error);
      return { success: false, message: error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu' };
    }
  };

  return (
    <CartContext.Provider
    value={{
      cartItems,
      qrcode,
      tableId,
      setQrcode,
      setTableId,
      addToCart,
      removeFromCart,
      updateQuantity,
      clearCart,
      getTotalPrice,
      getTotalItems,
      placeOrder
    }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};


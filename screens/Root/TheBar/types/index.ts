export interface Category {
    id: number;
    title: string;
    cat_image: string | null;
    sort_order: number;
    printer: Printer | null;
    parent_id: number | null;
    parent_title: string | null;
  }
  
  export interface Printer {
    id: number;
    name: string;
  }
  
  export interface MenuItem {
    id: number;
    title: string;
    description: string | null;
    price: string;
    net_price: string | null;
    tax_id: number | null;
    tax_title: string | null;
    tax_rate: number | null;
    tax_type: string | null;
    category_id: number;
    category_title: string;
    image: string | null;
    stock: number;
    is_stock_active: number;
    is_cocktail: number | null;
    inventory_item_id: number | null;
    is_recipe: number;
    is_visible_in_qr_menu: number;
    sales_quantity_value: number | null;
    sales_unit_id: number | null;
    printer_id: number | null;
    printer_path: string | null;
    sew_points: number | null;
    addons: any[];
    variants: any[];
  }
  
  export interface Campaign {
    id: number;
    campaign_name: string;
    description: string | null;
    start_date: string;
    end_date: string;
    tenant_id: number;
    created_at: string;
    updated_at: string;
    image_url: string | null;
  }
  
  export interface StoreSettings {
    tenant_id: number;
    store_name: string;
    store_image: string | null;
    address: string;
    phone: string;
    email: string;
    currency: string;
    image: string | null;
    slides: string;
    default_order_type: string;
    dine_in_enabled: number;
    delivery_enabled: number;
    takeaway_enabled: number;
    is_qr_menu_enabled: number;
    unique_qr_code: string;
    is_qr_order_enabled: number;
    autolock: string | null;
    facebook: string;
    instagram: string;
    twitter: string;
    whatsapp: string;
    background_color: string | null;
    text_color: string | null;
    primary_color: string | null;
    header_color: string | null;
    header_text_color: string | null;
    menu_button_background_color: string | null;
    menu_button_text_color: string | null;
    complaint_button_background_color: string | null;
    complaint_button_text_color: string | null;
    suggestion_button_background_color: string | null;
    suggestion_button_text_color: string | null;
    thank_you_button_background_color: string | null;
    thank_you_button_text_color: string | null;
    footer_background_color: string | null;
    footer_text_color: string | null;
    price_text_color: string | null;
    head_text_color: string | null;
  }
  
  export interface ApiResponse {
    categories: Category[];
    menuItems: MenuItem[];
    campaigns: Campaign[];
    storeSettings: StoreSettings;
    storeTable?: any;
    translations?: any[];
  }
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  ScrollView,
  Alert
} from 'react-native';
import { colors } from './theme';
import { useCart } from './CartContext';
import { useOrder } from './OrderContext';

interface OrderTrackingModalProps {
  visible: boolean;
  onClose: () => void;
  orderId: number | null;
  qrcode: string | null;
}

interface OrderStatus {
  id: number;
  date: string;
  delivery_type: string | null;
  customer_type: string;
  customer_id: string | null;
  table_id: number;
  status: 'created' | 'completed' | 'cancelled';
  payment_status: string;
  tenant_id: number;
  items: Array<{
    id: number;
    order_id: number;
    item_id: number;
    variant_id: number | null;
    price: number;
    quantity: number;
    status: string;
    date: string;
    notes: string | null;
    addons: string | null; // JSON string
    tenant_id: number;
    // Menu item bilgileri (JOIN ile gelecek)
    title?: string;
    variant_title?: string;
    addon_titles?: string[]; // Parse edilmiş addon isimleri
  }>;
}

const OrderTrackingModal: React.FC<OrderTrackingModalProps> = ({ 
  visible, 
  onClose, 
  orderId, 
  qrcode 
}) => {
  const [orderStatus, setOrderStatus] = useState<OrderStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { updateOrderStatus } = useOrder();

  // Sipariş durumu çekme
  const fetchOrderStatus = async () => {
    if (!orderId || !qrcode) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `https://backendpos.hollystone.com.tr/api/v1/qrmenu/${qrcode}/order-status/${orderId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Sipariş durumu alınamadı');
      }

      const data = await response.json();
      setOrderStatus(data.order);

      // OrderContext'teki aktif siparişin durumunu güncelle
      if (data.order && data.order.status) {
        console.log('Sipariş durumu güncelleniyor:', data.order.status);
        updateOrderStatus(data.order.status);
      }
    } catch (error) {
      console.error('Sipariş durumu hatası:', error);
      setError('Sipariş durumu yüklenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  // Modal açıldığında sipariş durumunu çek
  useEffect(() => {
    if (visible && orderId) {
      fetchOrderStatus();
      
      // Her 30 saniyede bir durumu güncelle
      const interval = setInterval(fetchOrderStatus, 30000);
      
      return () => clearInterval(interval);
    }
  }, [visible, orderId]);

  // Sipariş durumu metinleri
  const getStatusText = (status: string) => {
    switch (status) {
      case 'created': return 'Sipariş Oluşturuldu';
      case 'completed': return 'Sipariş Tamamlandı';
      case 'cancelled': return 'Sipariş İptal Edildi';
      default: return 'Bilinmeyen Durum';
    }
  };

  // Sipariş durumu renkleri
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created': return '#f59e0b';
      case 'completed': return '#10b981';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  // Toplam tutarı hesapla
  const calculateTotalAmount = (items: any[]) => {
    return items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  };

  // Addon'ları parse et
  const parseAddons = (addonsJson: string | null) => {
    if (!addonsJson) return [];
    try {
      return JSON.parse(addonsJson);
    } catch {
      return [];
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Sipariş Takibi</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Sipariş durumu yükleniyor...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity onPress={fetchOrderStatus} style={styles.retryButton}>
                  <Text style={styles.retryButtonText}>Tekrar Dene</Text>
                </TouchableOpacity>
              </View>
            ) : orderStatus ? (
              <>
                {/* Sipariş Bilgileri */}
                <View style={styles.orderInfo}>
                  <Text style={styles.orderNumber}>Sipariş #{orderStatus.id}</Text>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(orderStatus.status) }
                  ]}>
                    <Text style={styles.statusText}>
                      {getStatusText(orderStatus.status)}
                    </Text>
                  </View>
                </View>

                {/* Sipariş Durumu Timeline */}
                <View style={styles.timeline}>
                  <View style={styles.timelineItem}>
                    <View style={[
                      styles.timelineIcon,
                      { backgroundColor: '#10b981' }
                    ]} />
                    <Text style={styles.timelineText}>Sipariş Oluşturuldu</Text>
                  </View>

                  <View style={styles.timelineItem}>
                    <View style={[
                      styles.timelineIcon,
                      { backgroundColor: orderStatus.status === 'completed' ? '#10b981' : '#e5e7eb' }
                    ]} />
                    <Text style={styles.timelineText}>Sipariş Tamamlandı</Text>
                  </View>

                  {orderStatus.status === 'cancelled' && (
                    <View style={styles.timelineItem}>
                      <View style={[
                        styles.timelineIcon,
                        { backgroundColor: '#ef4444' }
                      ]} />
                      <Text style={[styles.timelineText, { color: '#ef4444' }]}>Sipariş İptal Edildi</Text>
                    </View>
                  )}
                </View>

                {/* Sipariş Detayları */}
                <View style={styles.orderDetails}>
                  <Text style={styles.sectionTitle}>Sipariş Detayları</Text>
                  {orderStatus.items?.map((item, index) => {
                    const addons = parseAddons(item.addons);
                    return (
                      <View key={index} style={styles.orderItem}>
                        <View style={styles.orderItemInfo}>
                          <Text style={styles.orderItemTitle}>
                            {item.quantity}x {item.title || `Ürün #${item.item_id}`}
                          </Text>
                          {item.variant_title && (
                            <Text style={styles.orderItemVariant}>
                              Seçenek: {item.variant_title}
                            </Text>
                          )}
                          {addons.length > 0 && item.addon_titles && (
                            <Text style={styles.orderItemAddons}>
                              Ekstralar: {item.addon_titles.join(', ')}
                            </Text>
                          )}
                        </View>
                        <Text style={styles.orderItemPrice}>
                          {(item.price * item.quantity).toFixed(2)} TL
                        </Text>
                      </View>
                    );
                  })}

                  <View style={styles.totalContainer}>
                    <Text style={styles.totalText}>
                      Toplam: {calculateTotalAmount(orderStatus.items || []).toFixed(2)} TL
                    </Text>
                  </View>
                </View>

                {/* Yenile Butonu */}
                <TouchableOpacity onPress={fetchOrderStatus} style={styles.refreshButton}>
                  <Text style={styles.refreshButtonText}>Durumu Yenile</Text>
                </TouchableOpacity>
              </>
            ) : (
              <View style={styles.noOrderContainer}>
                <Text style={styles.noOrderText}>Sipariş bulunamadı</Text>
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    width: '95%',
    height: '85%',
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: colors.textSecondary,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 10,
    color: colors.textSecondary,
  },
  errorContainer: {
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 15,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  orderInfo: {
    alignItems: 'center',
    marginBottom: 30,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 10,
  },
  statusBadge: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  timeline: {
    marginBottom: 30,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  timelineIcon: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 15,
  },
  timelineText: {
    fontSize: 16,
    color: colors.text,
  },
  orderDetails: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 15,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  orderItemInfo: {
    flex: 1,
  },
  orderItemTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
  },
  orderItemVariant: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  orderItemAddons: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  orderItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
  },
  totalContainer: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 2,
    borderTopColor: colors.border,
  },
  totalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'right',
  },
  refreshButton: {
    backgroundColor: colors.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  noOrderContainer: {
    alignItems: 'center',
    padding: 40,
  },
  noOrderText: {
    color: colors.textSecondary,
    fontSize: 16,
  },
});

export default OrderTrackingModal;

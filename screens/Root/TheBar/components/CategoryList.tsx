import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { colors } from '../theme';
import { Category } from '../types';
import { white } from '../../../../constants/Color';

interface CategoryListProps {
  categories: Category[];
  onSelectCategory: (categoryId: number) => void;
  defaultSelectedId?: number | null;
}

const CategoryList: React.FC<CategoryListProps> = ({ 
  categories, 
  onSelectCategory,
  defaultSelectedId = null
}) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(defaultSelectedId);

  // Set first category as selected by default if categories are loaded and none is selected
  useEffect(() => {
    if (categories.length > 0 && selectedCategoryId === null) {
      const firstCategoryId = categories[0].id;
      setSelectedCategoryId(firstCategoryId);
      onSelectCategory(firstCategoryId);
    }
  }, [categories, selectedCategoryId, onSelectCategory]);

  // If there are no categories, show a message
  if (!categories || categories.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Kategoriler</Text>
        </View>
        <Text style={styles.emptyText}>Kategoriler yükleniyor...</Text>
      </View>
    );
  }

  const handleCategoryPress = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    onSelectCategory(categoryId);
  };

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <Text style={styles.title}>Menü</Text>
      </View>
      <FlatList
        data={categories}
        renderItem={({ item }) => (
          <CategoryItem
            category={item}
            isSelected={item.id === selectedCategoryId}
            onPress={() => handleCategoryPress(item.id)}
          />
        )}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.list}
        keyExtractor={(item) => item.id.toString()}
      />
    </View>
  );
};

interface CategoryItemProps {
  category: Category;
  isSelected: boolean;
  onPress: () => void;
}

const CategoryItem: React.FC<CategoryItemProps> = ({ category, isSelected, onPress }) => {
  return (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        isSelected ? styles.selectedCategoryItem : styles.unselectedCategoryItem
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[
        styles.categoryImageContainer,
        isSelected ? styles.selectedCategoryImageContainer : styles.unselectedCategoryImageContainer
      ]}>
        {category.cat_image ? (
          <Image
            style={styles.categoryImage}
            source={{ uri: `https://backendpos.hollystone.com.tr${category.cat_image}` }}
          />
        ) : (
          <View style={styles.placeholderImage} />
        )}
      </View>
      <Text style={[
        styles.categoryName,
        isSelected && styles.selectedCategoryName
      ]} numberOfLines={1} ellipsizeMode="tail">
        {category.title}
      </Text>
    </TouchableOpacity>
  );
};

export default CategoryList;

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontFamily: 'MADE TOMMY',
    color: colors.text,
  },
  list: {

  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontFamily: 'MADE TOMMY',
    marginLeft: 15,
    marginTop: 10,
  },
  categoryItem: {
    marginRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 8,
    width: 130,
    height: 36,
    borderWidth: 1,
  },
  unselectedCategoryItem: {
    backgroundColor: white,
    borderColor: '#e9ecef',
  },
  selectedCategoryItem: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryImageContainer: {
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
    overflow: 'hidden',
  },
  unselectedCategoryImageContainer: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectedCategoryImageContainer: {
    backgroundColor: '#fff',
  },
  categoryImage: {
    width: 26,
    height: 26,
    borderRadius: 13,
  },
  placeholderImage: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: colors.imagePlaceholder,
  },
  categoryName: {
    fontSize: 13,
    fontFamily: 'MADE TOMMY',

    color: colors.text,
    flex: 1,
  },
  selectedCategoryName: {
    color: '#fff',
    fontWeight: 'bold',
  },
});
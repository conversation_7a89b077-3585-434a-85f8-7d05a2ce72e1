import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  Image,
  StyleSheet,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
  TouchableOpacity
} from 'react-native';
import { colors } from '../theme';
import { black_t2 } from '../../../../constants/Color';

const { width: windowWidth } = Dimensions.get('window');

interface ImageSliderProps {
  slides: string[];
  autoScroll?: boolean;
  scrollInterval?: number;
}

interface SlideItem {
  id: number;
  image: string;
}

const ImageSlider: React.FC<ImageSliderProps> = ({ 
  slides, 
  autoScroll = true, 
  scrollInterval = 3000 
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList | null>(null);
  
  // Convert slides array to expected format
  const slideData: SlideItem[] = slides.map((slide, index) => ({
    id: index + 1,
    image: `https://backendpos.hollystone.com.tr${slide}`
  }));

  // Auto scroll effect
  useEffect(() => {
    if (!autoScroll || slideData.length <= 1) return;
    
    const interval = setInterval(() => {
      if (activeIndex === slideData.length - 1) {
        flatListRef.current?.scrollToIndex({
          index: 0,
          animated: true,
        });
      } else {
        flatListRef.current?.scrollToIndex({
          index: activeIndex + 1,
          animated: true,
        });
      }
    }, scrollInterval);
    
    return () => clearInterval(interval);
  }, [activeIndex, autoScroll, scrollInterval, slideData.length]);

  // Handle scroll events
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / windowWidth);
    setActiveIndex(index);
  };

  // Render each slide
  const renderItem = ({ item }: { item: SlideItem }) => (
    <View style={styles.slideContainer}>
      <Image
        source={{ uri: item.image }}
        style={styles.image}
        resizeMode="cover"
      />
    </View>
  );
  
  // If there are no slides, don't render anything
  if (!slides || slides.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={slideData}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyExtractor={(item) => item.id.toString()}
        getItemLayout={(_, index) => ({
          length: windowWidth,
          offset: windowWidth * index,
          index,
        })}
      />
      
      {/* Pagination dots */}
      <View style={styles.pagination}>
        {slideData.map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => {
              flatListRef.current?.scrollToIndex({
                index,
                animated: true,
              });
            }}
          >
            <View
              style={[
                styles.paginationDot,
                index === activeIndex && styles.paginationDotActive,
              ]}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 200,
    position: 'relative',
    marginHorizontal: -15, // Ana container padding'ini telafi et
  },
  slideContainer: {
    width: windowWidth,
    height: '100%',
    padding: 10,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  pagination: {
    position: 'absolute',
    bottom: 15,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: colors.primary,
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});

export default ImageSlider;
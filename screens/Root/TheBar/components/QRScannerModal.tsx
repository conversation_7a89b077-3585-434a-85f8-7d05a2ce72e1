import React, { useEffect, useState, useRef } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet, Dimensions, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Camera, useCameraDevice, useCodeScanner } from 'react-native-vision-camera';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { colors } from '../theme';

const { width, height } = Dimensions.get('window');

interface QRScannerModalProps {
  visible: boolean;
  onClose: () => void;
  onScanSuccess: (data: string) => void;
}

const QRScannerModal: React.FC<QRScannerModalProps> = ({ visible, onClose, onScanSuccess }) => {
  const [hasPermission, setHasPermission] = useState(false);
  const device = useCameraDevice('back');
  const camera = useRef<Camera>(null);

  // Check camera permission when modal becomes visible
  useEffect(() => {
    if (visible) {
      checkCameraPermission();
    }
  }, [visible]);

  // Permission check function
  const checkCameraPermission = async () => {
    try {
      // Choose the correct permission based on platform
      const cameraPermission = Platform.select({
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA,
        default: PERMISSIONS.ANDROID.CAMERA,
      });

      // Check current permission status
      const result = await check(cameraPermission);
      
      switch (result) {
        case RESULTS.GRANTED:
          setHasPermission(true);
          break;
        case RESULTS.DENIED:
          // Request permission if denied
          const requestResult = await request(cameraPermission);
          setHasPermission(requestResult === RESULTS.GRANTED);
          break;
        case RESULTS.BLOCKED:
        case RESULTS.UNAVAILABLE:
          setHasPermission(false);
          // You could show an alert here to direct users to settings
          break;
        default:
          setHasPermission(false);
          break;
      }
    } catch (error) {
      console.error('Error checking camera permission:', error);
      setHasPermission(false);
    }
  };

  // Handle QR code scanning
  const handleQRCodeScanned = (codes) => {
    if (codes && codes.length > 0 && codes[0].value) {
      onScanSuccess(codes[0].value);
    }
  };

  // Set up code scanner
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: handleQRCodeScanned
  });

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.qrContainer}>
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>QR Kod Tarayıcı</Text>
          </View>

          <View style={styles.cameraContainer}>
            <View style={styles.topContentContainer}>
              <Text style={styles.centerText}>
                Menüyü görüntülemek için masadaki QR kodu tarayın
              </Text>
            </View>

            {device && hasPermission ? (
              <View style={styles.cameraSectionContainer}>
                <Camera
                  ref={camera}
                  style={styles.cameraStyle}
                  device={device}
                  isActive={visible}
                  codeScanner={codeScanner}
                  enableZoomGesture={false}
                  photo={false}
                  video={false}
                  audio={false}
                />
                <View style={styles.markerContainer}>
                  <View style={styles.marker} />
                </View>
              </View>
            ) : (
              <View style={[styles.cameraStyle, { backgroundColor: '#888', justifyContent: 'center', alignItems: 'center' }]}>
                <Text style={{ color: 'white', textAlign: 'center' }}>
                  {hasPermission === false ? 'Kamera izni gerekiyor' : 'Kamera başlatılıyor...'}
                </Text>
              </View>
            )}

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={onClose}
              >
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

export default QRScannerModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrContainer: {
    width: width * 0.95,
    height: height * 0.8,
    backgroundColor: 'white',
    borderRadius: 15,
    overflow: 'hidden',
    justifyContent: 'space-between',
  },
  headerContainer: {
    backgroundColor: colors.primary,
    padding: 15,
    alignItems: 'center',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  headerText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  centerText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginVertical: 10,
  },
  buttonContainer: {
    padding: 15,
    alignItems: 'center',
    width: '100%',
    backgroundColor: 'white',
  },
  cancelButton: {
    backgroundColor: colors.secondary,
    paddingVertical: 10,
    paddingHorizontal: 30,
    borderRadius: 20,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Camera container styles
  cameraContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  topContentContainer: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    width: '100%',
  },
  cameraSectionContainer: {
    position: 'relative',
    width: width * 0.8,
    height: height * 0.4,
    alignSelf: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  cameraStyle: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
    overflow: 'hidden',
  },
  markerContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  marker: {
    borderColor: colors.primary,
    borderRadius: 10,
    borderWidth: 2,
    width: width * 0.6,
    height: width * 0.6,
  },
});
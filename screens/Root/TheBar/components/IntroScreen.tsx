import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Image } from 'react-native';
import LottieView from 'lottie-react-native';
import { colors } from '../theme';
import { BackIcon } from "../../../../components/Svgs";
import { white } from "../../../../constants/Color";

interface IntroScreenProps {
  onStartScan: () => void;
  navigation: any;
}

const { width, height } = Dimensions.get('window');


const IntroScreen: React.FC<IntroScreenProps> = ({ onStartScan, navigation }) => {
  return (
    <View style={styles.container}>
      {/* Geri Dönüş Butonu */}
      <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
        <BackIcon size={25} color={colors.primary} />
      </TouchableOpacity>

      <View style={styles.lottieContainer}>
        <LottieView
          source={require('../../../../assets/animations/qr-scan.json')}
          autoPlay
          loop
          style={{ width: 250, height: 250 }}
        />
      </View>

      <Text style={styles.title}>Menü</Text>
      <Text style={styles.description}>
        Menümüzü keşfetmek için masadaki QR kodu tarayın.
        Bu sayede tüm ürünlerimizi ve kampanyalarımızı görüntüleyebilirsiniz.
      </Text>

      <TouchableOpacity
        style={styles.scanButton}
        onPress={onStartScan}
      >
        <Text style={styles.scanButtonText}>QR Kodu Tara</Text>
      </TouchableOpacity>
    </View>
  );
};

export default IntroScreen;


const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.background,
  },
  greenFold: {
    height: 40,
    width: 200,
  },
  goBackButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  lottieContainer: {
    width: width * 0.7,
    height: width * 0.7,
    marginBottom: 30,
    alignItems: 'center',

  },
  title: {
    fontSize: 24,
    fontFamily: 'MADE TOMMY',
    textAlign: 'center',
    marginBottom: 20,
    color: colors.text,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  scanButton: {
    backgroundColor: colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
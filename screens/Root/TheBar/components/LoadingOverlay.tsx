import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import LottieView from 'lottie-react-native';
import { colors } from '../theme';

interface LoadingOverlayProps {
  message?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ message = 'Yükleniyor...' }) => {
  return (
    <View style={styles.container}>
      <LottieView
        source={require('../../../../assets/animations/loading.json')}
        autoPlay
        loop
        style={{ width: 120, height: 120 }}
      />
      <Text style={styles.loadingText}>{message}</Text>
    </View>
  );
};

export default LoadingOverlay;


const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    zIndex: 100,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: colors.primary,
    fontWeight: 'bold',
  },
});
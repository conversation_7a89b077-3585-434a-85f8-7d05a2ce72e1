import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Linking, Platform, StyleSheet, Image, ScrollView } from 'react-native';
import Toast from '../../../components/Toast';
import { SafeAreaView } from 'react-native-safe-area-context';
import { check, request, PERMISSIONS, RESULTS, Permission } from 'react-native-permissions';
import { colors } from './theme';
import QRScannerModal from './components/QRScannerModal';
import LoadingOverlay from './components/LoadingOverlay';
import ErrorMessage from './components/ErrorMessage';
import ImageSlider from './components/ImageSlider';
import CategoryList from './components/CategoryList';
import IntroScreen from './components/IntroScreen';
import { fetchMenuData } from './server/api';
import { Category, MenuItem, Campaign, StoreSettings } from './types';
import HeaderSix from '../../../components/HeaderSix';
import { kremrengi } from '../../../constants/Color';
import { CartProvider, useCart } from './CartContext';
import CartModal from './CartModal';
import CartButton from './CartButton';

const TheBarContent: React.FC<any> = ({ navigation }) => {
  const [showQRScanner, setShowQRScanner] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [menuLoaded, setMenuLoaded] = useState<boolean>(false);

  // API data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [slides, setSlides] = useState<string[]>([]);
  const [storeSettings, setStoreSettings] = useState<StoreSettings | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [showCartModal, setShowCartModal] = useState<boolean>(false);
  const [toastStatus, setToastStatus] = useState<boolean | false>(false);
  const [toastMessage, setToastMessage] = useState<string>('');
  const [toastType, setToastType] = useState<string>('success');
  const { addToCart, setQrcode, setTableId } = useCart(); // Cart context'i kullan




  // Handle QR scan result
  const handleScanSuccess = async (scannedUrl: string): Promise<void> => {
    try {

      // QR tarayıcıyı hemen kapat ve yükleme göstergesini göster
      setShowQRScanner(false);
      setIsLoading(true);
      setError(null);

      // Extract qrcode and tableId from URL
      let qrcode = '';
      let tableId = '';

      const qrcodeMatch = scannedUrl.match(/\/m\/([^?]+)/);
      if (qrcodeMatch && qrcodeMatch[1]) {
        qrcode = qrcodeMatch[1];
      }

      const tableIdMatch = scannedUrl.match(/[?&]table=([^&]+)/);
      if (tableIdMatch && tableIdMatch[1]) {
        tableId = tableIdMatch[1];
      }

      setQrcode(qrcode);
    

      // Fetch menu data
      if (qrcode) {
        try {
          const data = await fetchMenuData(qrcode, tableId);

          setCategories(data.categories || []);
          setMenuItems(data.menuItems || []);
          setCampaigns(data.campaigns || []);
          setStoreSettings(data.storeSettings || null);
          setTableId(data.storeTable.id);

          // Set slides if available
          if (data.storeSettings && data.storeSettings.slides) {
            setSlides(data.storeSettings.slides.split(','));
          }

          setIsLoading(false);
          setMenuLoaded(true);
          // Başarılı olduğunda alert göstermeye gerek yok
          // showAlert('QR kod başarıyla tarandı', 'success');
        } catch (error) {
          console.error('API error:', error);
          setError(error instanceof Error ? error.message : 'Bilinmeyen hata');
          setIsLoading(false);
          showAlert('Geçersiz QR Kod', 'error');
          // QR tarayıcıyı otomatik olarak tekrar açmıyoruz
        }
      } else {
        setIsLoading(false);
        showAlert('Geçersiz QR Kod', 'error');
        // QR tarayıcıyı otomatik olarak tekrar açmıyoruz
      }
    } catch (error) {
      console.error('QR processing error:', error);
      setError(error instanceof Error ? error.message : 'Bilinmeyen hata');
      setIsLoading(false);
      showAlert('QR kod işlenirken bir hata oluştu', 'error');
      // QR tarayıcıyı otomatik olarak tekrar açmıyoruz
    }
  };

  // Show toast notification
  const showAlert = (message: string, type: 'success' | 'error'): void => {
    // Boş mesajları gösterme
    if (!message || message.trim() === '') return;

    setToastMessage(message);
    setToastType(type);
    setToastStatus(true); // Trigger toast animation

    // Reset toast status after animation completes
    setTimeout(() => {
      setToastStatus(false);
      setToastMessage(''); // Mesajı temizle
    }, 3000);
  };

  // Check camera permission
  const checkCameraPermission = async (): Promise<void> => {
    try {
      const cameraPermission: Permission | undefined = Platform.select({
        ios: PERMISSIONS.IOS.CAMERA,
        android: PERMISSIONS.ANDROID.CAMERA,
        default: undefined,
      });

      if (!cameraPermission) {
        console.error('Camera permission not available for platform');
        return;
      }

      const status = await check(cameraPermission);

      if (status === RESULTS.GRANTED) {
        setShowQRScanner(true);
      } else if (status === RESULTS.DENIED) {
        const permissionResult = await request(cameraPermission);
        if (permissionResult === RESULTS.GRANTED) {
          setShowQRScanner(true);
        } else {
          showAlert('QR kodu tarayabilmek için kamera izni gerekli', 'error');
        }
      } else {
        showAlert('QR kodu tarayabilmek için kamera erişimine izin vermelisiniz.', 'error');
        // Ayarlara gitme seçeneği için kullanıcıya bilgi ver
        setTimeout(() => {
          Linking.openSettings();
        }, 2000);
      }
    } catch (error) {
      console.error('Permission check error:', error);
    }
  };

  const handleStartScanning = (): void => {
    checkCameraPermission();
  };

  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  const canAddToCart = storeSettings?.is_qr_order_enabled === 1;


  const filteredMenuItems = selectedCategoryId
    ? menuItems.filter(item => item.category_id === selectedCategoryId)
    : menuItems;


  return (
    <SafeAreaView style={styles.container}>
      {!menuLoaded && !isLoading && !showQRScanner ? (
        <IntroScreen onStartScan={handleStartScanning} navigation={navigation} />
      ) : (
        <View style={styles.contentContainer}>
          {/* Loading Overlay */}
          {isLoading && <LoadingOverlay message="Menü yükleniyor..." />}

          {/* Error Message */}
          {error && (
            <ErrorMessage
              error={error}
              onRetry={handleStartScanning}
            />
          )}

          {/* Menu Content */}
          {menuLoaded && (
            <>
              <HeaderSix navigation={navigation} />

              {/* Tüm içeriği tek bir ScrollView içine koyalım */}
              <ScrollView
                style={styles.mainScrollView}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.scrollViewContent}
              >
                {/* Slider */}
                {slides.length > 0 && <ImageSlider slides={slides} />}

                {/* Categories */}
                <CategoryList
                  categories={categories}
                  onSelectCategory={handleCategorySelect}
                />

                {/* Menu Items */}
                 {filteredMenuItems.length === 0 ? (
                   <Text style={styles.emptyText}>Bu kategoride ürün bulunmamaktadır.</Text>
                 ) : (
                   filteredMenuItems.map((item) => (
                     <MenuItemView
                       key={item.id}
                       item={item}
                       canAddToCart={canAddToCart}
                       onAddToCart={() => addToCart(item)}
                     />
                   ))
                 )}

                {/* Campaigns */}
                {campaigns.length > 0 && (
                  <>
                    <View style={styles.sectionTitleContainer}>
                      <Text style={styles.sectionTitle}>Kampanyalar</Text>
                    </View>

                    {campaigns.map((campaign) => (
                      <CampaignItemView key={campaign.id} campaign={campaign} />
                    ))}
                  </>
                )}

                {/* Extra bottom space */}
                <View style={styles.bottomSpace} />
              </ScrollView>
              {/* Sepet butonu - Alt kısımda sabit konumlu */}
              <CartButton onPress={() => setShowCartModal(true)} />

              {/* Sepet Modalı */}
              <CartModal
                visible={showCartModal}
                onClose={() => setShowCartModal(false)}
              />
            </>
          )}
        </View>
      )}

      {/* QR Scanner Modal */}
      <QRScannerModal
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        onScanSuccess={handleScanSuccess}
      />

      {/* Toast Notification - Sadece mesaj varsa göster */}
      {toastMessage && (
        <Toast
          type={toastType}
          subtitle={toastMessage}
          status={toastStatus === true}
          successColor={toastType === 'success' ? '#047857' : '#dc2626'}
        />
      )}
    </SafeAreaView>
  );
};

// Menü öğesi bileşeni - Doğrudan TheBar içinde kullanılacak
const MenuItemView: React.FC<{
  item: MenuItem,
  canAddToCart: boolean,
  onAddToCart: () => void
}> = ({ item, canAddToCart, onAddToCart }) => {  return (
    <View style={styles.menuItem}>
      {item.image ? (
        <Image
          style={styles.menuItemImage}
          source={{ uri: `https://backendpos.hollystone.com.tr${item.image}` }}
        />
      ) : (
        <View style={styles.menuItemImage} />
      )}

      <View style={styles.menuItemContent}>
        {/* Üst kısım: Başlık ve Fiyat */}
        <View style={styles.menuItemHeader}>
          <Text style={styles.menuItemTitle}>{item.title}</Text>
          <Text style={styles.menuItemPrice}>{item.price} TL</Text>
        </View>

        {/* Açıklama (varsa) */}
        {item.description ? (
          <Text style={styles.menuItemDescription}>{item.description}</Text>
        ) : null}

        {/* Alt kısım: Holly Puan ve Sepete Ekle */}
        {canAddToCart && (
          <View style={styles.menuItemFooter}>
            {/* API'den gelen veri yapısına göre hem sew_points hem de sew-points kontrol ediliyor */}
            {(item.sew_points || (item as any)['sew-points']) ? (
              <Text style={styles.hollyPointsText}>Holly Puan: {item.sew_points || (item as any)['sew-points']}</Text>
            ) : (
              <View />
            )}
            <TouchableOpacity
              style={styles.addToCartButton}
              onPress={onAddToCart}
            >
              <Text style={styles.addToCartButtonText}>Sepete Ekle</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

// Kampanya öğesi bileşeni - Doğrudan TheBar içinde kullanılacak
const CampaignItemView: React.FC<{ campaign: Campaign }> = ({ campaign }) => {
  return (
    <View style={styles.campaignItem}>
      {campaign.image_url && (
        <Image
          style={styles.campaignImage}
          source={{ uri: `https://backendpos.hollystone.com.tr${campaign.image_url}` }}
        />
      )}

      <Text style={styles.campaignTitle}>{campaign.campaign_name}</Text>

      {campaign.description && (
        <Text style={styles.campaignDescription}>{campaign.description}</Text>
      )}

      <View style={styles.campaignDates}>
        <Text style={styles.campaignDate}>
          <Text style={styles.campaignDateLabel}>Başlangıç:</Text> {campaign.start_date}
        </Text>
        <Text style={styles.campaignDate}>
          <Text style={styles.campaignDateLabel}>Bitiş:</Text> {campaign.end_date}
        </Text>
      </View>
    </View>
  );
};

const TheBar: React.FC = (props) => {
  return (
    <CartProvider>
      <TheBarContent {...props} />
    </CartProvider>
  );
};

export default TheBar;

// Güncellenmiş stiller
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: kremrengi,
  },
  contentContainer: {
    flex: 1,
  },
  mainScrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 10,
    paddingBottom: 50, // Alt kısımda büyük bir boşluk
  },
  sectionTitleContainer: {
    marginVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingBottom: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginVertical: 20,
    fontStyle: 'italic',
  },
  bottomSpace: {
    height: 100, // Ekstra alt boşluk
  },
  // Menü öğeleri için stiller
  menuItem: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    flexDirection: 'row',
  },
  menuItemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 15,
    backgroundColor: colors.imagePlaceholder,
  },
  menuItemContent: {
    flex: 1,
  },
  menuItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 5,
  },
  menuItemTitle: {
    fontSize: 16,
    fontFamily: 'MADE TOMMY',
    color: colors.text,
    flex: 1,
    marginRight: 10,
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
  },
  menuItemDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 5,
    lineHeight: 20,
  },
  categoryLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 8,
    backgroundColor: colors.categoryLabelBackground,
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
  },
  // Kampanya öğeleri için stiller
  campaignItem: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  campaignImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 10,
    backgroundColor: colors.imagePlaceholder,
  },
  campaignTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 5,
  },
  campaignDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 10,
    lineHeight: 20,
  },
  campaignDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 8,
  },
  campaignDate: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  campaignDateLabel: {
    fontWeight: 'bold',
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    paddingTop: 5,
    borderTopWidth: 0.5,
    borderTopColor: '#e0e0e0',
  },
  addToCartButton: {
    backgroundColor: colors.primary,
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    width: 100,
    alignItems: 'center',
  },
  addToCartButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  hollyPointsText: {
    fontSize: 14,
    color: '#00731C',
    fontWeight: 'bold',
  },
});
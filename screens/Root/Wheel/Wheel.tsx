import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Animated, StyleSheet, Image, ImageBackground } from 'react-native';
import Layout from '../../../constants/Layout';
import { black, white, green_t1, red_t6 } from '../../../constants/Color';
import { getImageURL, post } from '../../../networking/Server';
import { useNavigation } from "@react-navigation/native";
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';
import { MainStore } from '../../../stores/MainStore';
import { BackIcon } from '../../../components/Svgs';
import Svg, { Path } from "react-native-svg";
import WheelOfFortune from '../../../components/WheelComponent';
import { Observer } from "mobx-react-lite";
import { WheelStoreInstance as WheelStore } from "../../../stores/WheelStore";
import { SafeAreaView } from 'react-native-safe-area-context';

const Wheel: React.FC = () => {
    const navigation = useNavigation();
    const [winner, setWinner] = useState<number | null>(null);
    const [showWinnerModal, setShowWinnerModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [count, setCount] = useState(0);
    const [startCountdown, setStartCountdown] = useState(false);
    const wheelRef = useRef<any>(null);
    const scaleAnim = useRef(new Animated.Value(0)).current; // Animation for bounce effect

    useEffect(() => {
        WheelStore.fetchWheelInfo();
    }, []);

    useEffect(() => {
        if (startCountdown && count > 0) {
            // Grow and shrink animation for each count
            const animate = () => {
                Animated.sequence([
                    Animated.timing(scaleAnim, {
                        toValue: 1.5, // Grow to 1.5x size
                        duration: 500, // Half of the 1-second interval
                        useNativeDriver: true,
                    }),
                    Animated.timing(scaleAnim, {
                        toValue: 0.5, // Shrink to 0.5x size
                        duration: 500, // Remaining half of the 1-second interval
                        useNativeDriver: true,
                    }),
                ]).start();
            };

            animate(); // Start animation for the current number

            const timer = setInterval(() => {
                setCount((prev) => {
                    if (prev <= 1) {
                        Animated.timing(scaleAnim, {
                            toValue: 0, // Shrink to 0 when countdown ends
                            duration: 300,
                            useNativeDriver: true,
                        }).start(() => setStartCountdown(false));
                        clearInterval(timer);
                        return 0;
                    }
                    animate(); // Trigger animation for the next number
                    return prev - 1;
                });
            }, 1000); // 1-second interval for each number
            return () => clearInterval(timer);
        } else {
            scaleAnim.setValue(0); // Reset scale when countdown ends
        }
    }, [startCountdown, count]);

    const handleSpin = () => {
        setCount(3);
        setStartCountdown(true);

        setTimeout(() => {
            post('wheel/spin').then((res: any) => {
                if (res.type === 'success') {
                    WheelStore.lastImage = res.reward.image;
                    WheelStore.lastName = res.reward.name;

                    const winnerIndex = WheelStore.participants.findIndex(p => p.name === res.reward.name);
                    if (winnerIndex !== -1) {
                        setWinner(winnerIndex);
                        if (wheelRef.current) {
                            wheelRef.current.prepareWheel(winnerIndex);
                            wheelRef.current._onPress();
                        }
                        setTimeout(() => {
                            WheelStore.participants = [];
                        }, 6000);
                    }
                    WheelStore.fetchWheelInfo();

                }
            });
        }, 3000); // Wait for countdown to finish
    };

    const handleGetWinner = (value: any, index: number) => {
        setWinner(index);
        setShowWinnerModal(true);
    };

    const WinnerModal: React.FC = () => (
        <View style={styles.winnerModal}>
        <ImageBackground
            source={require('../../../assets/root/wheelwinback.png')}
            style={styles.winnerBackground}
            resizeMode='contain'
        >
            <View style={styles.winnerModalContent}>
                <View style={styles.winnerInfo}>
                    <Text style={styles.winnerTitle}>{MainStore.language.last_win}</Text>
                    {WheelStore.lastImage && (
                        <Image source={{ uri: getImageURL(WheelStore.lastImage) }} style={styles.winnerImage} />
                    )}
                    <Text style={styles.winnerName}>{WheelStore.lastName}</Text>
                    <Text style={styles.quoteText}>
                        Şanslı insan, doğru zamanda doğru yerde bulunandır.
                    </Text>
                    <Text style={styles.quoteAuthor}>Publilius Syrus</Text>
                </View>
    
                <View style={styles.countDownContainer}>
                    <Text style={styles.newPrizeText}>{MainStore.language.new_prize}</Text>
                    <View style={styles.countDownView}>
                        {['daywheel', 'hour', 'minute', 'second'].map((unit, index) => (
                            <ImageBackground
                                key={unit}
                                style={styles.countDownItem}
                                source={require('../../../assets/root/hWheelIt.png')}
                            >
                                <CountdownCircleTimer
                                    isPlaying
                                    initialRemainingTime={
                                        unit === 'daywheel' ? WheelStore.daywheel * 24 * 60 * 60 :
                                        unit === 'hour' ? WheelStore.hour * 60 * 60 :
                                        unit === 'minute' ? WheelStore.minute * 60 : 60
                                    }
                                    duration={
                                        unit === 'daywheel' ? WheelStore.daywheel * 24 * 60 * 60 :
                                        unit === 'hour' ? WheelStore.hour * 60 * 60 :
                                        unit === 'minute' ? WheelStore.minute * 60 : 60
                                    }
                                    colors={[green_t1, red_t6]}
                                    colorsTime={unit === 'daywheel' ? [WheelStore.daywheel * 24 * 60 * 60, (WheelStore.daywheel * 24 * 60 * 60) / 2] : [7, 3.5]}
                                    size={48}
                                    strokeWidth={4}
                                    onComplete={unit === 'second' ? () => ({ shouldRepeat: true, delay: 0 }) : undefined}
                                >
                                    {({ remainingTime }) => (
                                        <Text style={styles.countDownText}>
                                            {unit === 'daywheel' ? WheelStore.daywheel :
                                                unit === 'hour' ? (remainingTime / 60 / 60).toFixed(0) :
                                                    unit === 'minute' ? (remainingTime / 60).toFixed(0) : remainingTime}
                                        </Text>
                                    )}
                                </CountdownCircleTimer>
                                <Text style={styles.countDownLabel}>
                                    {unit === 'daywheel' ? 'Gün' : unit === 'hour' ? 'Saat' : unit === 'minute' ? 'Dakika' : 'Saniye'}
                                </Text>
                            </ImageBackground>
                        ))}
                    </View>
                </View>
            </View>
        </ImageBackground>
    </View>
    );

    const wheelOptions = {
        rewards: WheelStore.participants,
        knobSize: 50,
        borderWidth: 1,
        borderColor: 'transparent',
        innerRadius: 30,
        duration: 6000,
        backgroundColor: 'transparent',
        textAngle: 'horizontal',
        winner: winner,
        knobSource: require('../../../assets/knob.png'),
        onRef: (ref: any) => (wheelRef.current = ref),
    };

    return (
        <Observer>
            {() => (
                <ImageBackground source={require('../../../assets/carkarkaplan.png')} style={styles.container}>
                    <SafeAreaView style={styles.safeContainer}>
                    {loading ? (
                        <View style={styles.loadingContainer}>
                            <Image source={require('../../../assets/root/hediyecarkigif.gif')} style={styles.loadingGif} />
                        </View>
                    ) : (
                        <>
                            <View style={styles.header}>
                                <TouchableOpacity onPress={() => navigation.goBack()}>
                                    <BackIcon size={25} color={white} />
                                </TouchableOpacity>
                                <Image
                                    source={require('../../../assets/root/giftWheelLogo.png')}
                                    style={styles.giftWheelHeaderLogo}
                                    resizeMode='cover'
                                />
                            </View>

                            {WheelStore.participants.length > 0 ? (
                                    <WheelOfFortune options={wheelOptions} getWinner={handleGetWinner} />
                            ) : (
                                <WinnerModal />
                            )}

                            {count > 0 && (
                                <View style={styles.countares}>
                                    <Animated.Text
                                        style={[
                                            styles.countdownTextBig,
                                            { transform: [{ scale: scaleAnim }] },
                                        ]}
                                    >
                                        {count}
                                    </Animated.Text>
                                </View>
                            )}


                            {WheelStore.participants.length > 0 ? (

                            <TouchableOpacity onPress={handleSpin} style={styles.startButton}>
                               <Svg width='150' height='100' viewBox="0 0 210 200">
                                <Path fill="#0BDC05" d="M19.15,115c-1.01.45-2.03.57-3.07.38-1.04-.2-1.99-.58-2.86-1.14-.87-.56-1.64-1.25-2.31-2.06-.67-.81-1.17-1.63-1.47-2.44-.31-.81-.41-1.57-.29-2.27.11-.7.51-1.22,1.18-1.56,1.57-.73,2.65-1.67,3.24-2.82.59-1.15.91-2.39.97-3.7.06-1.32-.08-2.67-.42-4.04-.34-1.38-.65-2.65-.93-3.83-2.75-.79-5.11-2.33-7.07-4.63-2.36-2.86-4.04-6.82-5.05-11.87-.56-2.81-.9-5.89-1.01-9.26-.11-3.37-.01-6.89.29-10.56.31-3.67.84-7.41,1.6-11.19.76-3.79,1.7-7.53,2.82-11.24,1.12-3.7,2.41-7.18,3.87-10.44,1.46-3.25,3.03-6.2,4.71-8.84,3.87-5.95,8.08-9.9,12.63-11.87,3.65-1.52,7.15-1.96,10.52-1.35,3.03.62,5.64,2.02,7.83,**********,1.23,1.38,1.85,2.1,4.66-1.18,9.65-1.01,**********.23,1.18.63,1.52,**********.42,1.22.25,1.89-.22.67-.63,1.18-1.22,1.52-.59.34-1.22.42-1.89.25-2.25-.62-4.29-.97-6.14-1.05-1.85-.08-3.51.01-4.97.29.17.45.32.93.46,1.43.14.5.27,1.01.38,1.52.84,3.82.67,7.18-.5,10.1-.79,1.91-1.92,3.37-3.41,4.38-1.49,1.01-3.21,1.52-5.18,1.52-1.85,0-3.48-.39-4.88-1.18-1.46-.79-2.5-1.88-3.11-3.28-.84-1.8-1-3.83-.46-6.1.53-2.27,1.67-4.42,3.41-6.44,1.68-1.96,3.62-3.51,5.81-4.63-1.63-.73-3.17-.93-4.63-.59-1.46.34-2.83,1.01-4.12,2.02-1.29,1.01-2.48,2.3-3.58,3.87-1.09,1.57-2.1,3.23-3.03,4.97-.93,1.74-1.75,3.49-2.48,5.26-.73,1.77-1.38,3.38-1.94,4.84-1.07,2.81-2.01,5.74-2.82,8.8-.81,3.06-1.49,6.12-2.02,9.17-.53,3.06-.93,6.1-1.18,9.13-.25,3.03-.35,5.92-.29,8.67.17,5,.7,8.95,1.6,11.87.9,2.92,2.06,4.98,3.49,6.19,1.43,1.21,3.07,1.67,4.92,1.39,1.85-.28,3.79-1.08,5.81-2.4,2.02-1.32,4.07-3.09,6.14-5.3,2.08-2.22,4.07-4.64,5.98-7.28,1.63-2.24,3.07-4.46,4.33-6.65s2.34-4.14,3.24-5.85c.9-1.71,1.8-3.66,2.69-5.85.28-.67.74-1.15,1.39-1.43.64-.28,1.28-.28,1.89,0,.67.28,1.15.74,1.43,1.39.28.65.28,1.28,0,1.89-.06.11-.32.73-.8,1.85-.48,1.12-1.18,2.6-2.1,4.42-.93,1.82-2.05,3.87-3.37,6.14-1.32,2.27-2.85,4.59-4.59,6.94-2.02,2.86-4.11,5.43-6.27,7.7-2.16,2.27-4.33,4.22-6.52,5.85-2.08,1.46-4.24,2.72-6.48,3.79-2.25,1.06-4.63,1.82-7.15,2.27,1.18,1.79,2.22,3.82,3.11,6.06.9,2.24,1.4,4.47,1.52,6.69.11,2.22-.29,4.28-1.22,6.19-.93,1.91-2.6,3.39-5.01,4.46ZM44.4,22.32c.5-1.18.72-2.62.63-4.33-.08-1.71-.46-3.44-1.14-5.18-1.23.62-2.23,1.26-2.99,1.94-.76.67-1.33,1.23-1.73,1.68-1.24,1.46-1.99,2.83-2.27,4.12-.28,1.29-.25,2.27.08,2.95.51,1.07,1.65,1.6,3.45,1.6s3.17-.93,3.96-2.78Z"/>
                                <Path fill="#0BDC05" d="M91.46,49.43c.62.28,1.04.76,1.26,1.43.22.67.2,1.32-.08,1.94-.84,1.8-1.84,3.77-2.99,5.93-1.15,2.16-2.41,4.33-3.79,6.52-1.38,2.19-2.85,4.31-4.42,6.36-1.57,2.05-3.21,3.87-4.92,5.47-1.71,1.6-3.46,2.88-5.26,3.83-1.8.95-3.62,1.43-5.47,1.43-.62,0-1.35-.11-2.19-.34-.84-.23-1.7-.67-2.57-1.35-.87-.67-1.71-1.64-2.53-2.9-.81-1.26-1.47-2.96-1.98-5.09-.67-2.75-1-5.86-.97-9.34.03-3.48.44-6.96,1.22-10.44.22-1.12.56-2.41,1.01-3.87.45-1.46,1.02-2.9,1.73-4.33.7-1.43,1.56-2.78,2.57-4.04,1.01-1.26,2.19-2.26,3.54-2.99,2.81-1.51,5.5-2.02,8.08-1.51,1.29.22,2.44.69,3.45,1.39,1.01.7,1.79,1.56,2.36,2.57,1.29,2.52,1.12,5.28-.5,8.25-1.63,2.92-4.54,5.47-8.75,7.66-.17.17-.37.25-.59.25l-.34,2.27c-.22,1.85-.38,3.68-.46,5.47-.08,1.8-.06,3.41.08,4.84.14,1.43.41,2.58.8,3.45.39.87.98,1.28,1.77,1.22.62-.05,1.39-.5,2.31-1.35s1.92-1.92,2.99-3.24c1.07-1.32,2.15-2.81,3.24-4.46,1.09-1.65,2.15-3.32,3.16-5.01,1.01-1.68,1.94-3.3,2.78-4.84.84-1.54,1.54-2.88,2.1-4,.28-.62.76-1.04,1.43-1.26.67-.22,1.32-.2,1.94.08ZM71.42,49.26c.73-.56,1.39-1.19,1.98-1.89.59-.7,1.05-1.35,1.39-1.94.34-.59.55-1.11.63-1.56.08-.45.01-.76-.21-.93-.28-.17-.58-.07-.88.29-.31.37-.65.88-1.01,1.56-.37.67-.72,1.42-1.05,2.23-.34.81-.62,1.56-.84,2.23Z"/>
                                <Path fill="#0BDC05" d="M94.06,89.58c-1.96-2.3-3.23-5.72-3.79-10.27-.5-3.59-.64-7.66-.42-12.21.06-1.96.17-3.87.34-5.72.17-1.85.34-3.49.51-4.92.17-1.43.33-2.58.47-3.45.14-.87.21-1.33.21-1.39.28-.95.83-1.74,1.65-2.36.82-.62,1.74-1.05,2.75-1.3,1.02-.25,2.07-.32,3.18-.21,1.1.11,2.09.41,2.96.88.88.48,1.57,1.15,2.07,2.02.5.87.7,1.92.59,3.16-.11.9-.32,2.55-.63,4.97-.31,2.41-.52,5.64-.63,9.68-.17,4.49.05,8.36.67,11.62.33,1.85.77,3.09,1.3,3.7.53.62,1.12.83,1.76.63.64-.2,1.31-.71,2.01-1.56s1.4-1.85,2.1-3.03c.7-1.18,1.35-2.43,1.97-3.75.62-1.32,1.17-2.51,1.68-3.58.95-2.19,1.82-4.63,2.6-7.32.78-2.69,1.48-5.3,2.1-7.83-2.4-.56-4.47-1.26-6.21-2.1-1.96-1.01-3.72-2.33-5.28-3.96-1.57-1.63-2.82-3.45-3.77-5.47-.84-1.96-1.37-3.96-1.6-5.98-.22-2.02-.11-3.98.34-5.89.67-2.92,2.07-5.27,4.19-7.07,2.35-1.91,5.2-2.86,8.56-2.86,1.84,0,3.58.53,5.2,1.6,1.4,1.07,2.52,2.47,3.36,4.21,1.45,3.09,2.18,7.13,2.18,12.12,0,3.03-.25,6.23-.76,9.6,1.4.45,2.76.67,4.08.67s2.61-.13,3.87-.38c1.26-.25,2.51-.59,3.75-1.01,1.23-.42,2.5-.88,3.79-1.39l.08-.08c.62-.28,1.25-.28,1.89,0,.64.28,1.11.73,1.39,1.35.28.62.29,1.25.04,1.89-.25.65-.69,1.11-1.3,1.39-.45.22-1.61.69-3.49,1.39-1.88.7-4.17,1.3-6.86,1.81-1.46.28-2.89.48-4.29.59-1.4.11-2.78.14-4.12.08-.62,2.75-1.36,5.6-2.23,8.54s-1.87,5.65-2.99,8.12c-1.12,2.69-2.34,5.09-3.66,7.2-1.32,2.1-2.68,3.91-4.08,5.43-1.18,1.35-2.55,2.48-4.12,3.41-1.57.93-3.16,1.56-4.76,1.89-1.6.34-3.16.29-4.67-.13-1.51-.42-2.83-1.33-3.96-2.74ZM115.86,37.64c0,1.8.41,3.72,1.22,5.77.81,2.05,2.12,3.86,3.91,5.43.11-1.23.21-2.41.29-3.54.08-1.12.13-2.24.13-3.37,0-.73-.04-1.71-.13-2.95-.08-1.23-.22-2.45-.42-3.66-.2-1.21-.45-2.26-.76-3.16-.31-.9-.69-1.35-1.14-1.35-.51,0-.94.21-1.3.63-.37.42-.67.97-.93,1.64-.25.67-.46,1.42-.63,2.23-.17.81-.25,1.59-.25,2.31Z"/>
                                <Path fill="#0BDC05" d="M143.89,89.32c-1.29-1.97-2.06-4.83-2.31-8.59-.25-3.76.01-8.53.8-14.31.28-2.02.59-3.91.93-5.68s.65-3.3.93-4.59c.28-1.29.52-2.33.72-3.11.2-.79.29-1.21.29-1.26.39-1.23,1.02-2.22,1.89-2.95.87-.73,1.85-1.23,2.95-1.52,1.09-.28,2.2-.39,3.32-.34,1.12.06,2.13.25,3.03.59.9.34,1.61.76,2.15,1.26.53.5.72,1.07.55,1.68-.51,1.96-.93,3.73-1.26,5.3-.34,1.57-.69,3.32-1.05,5.26-.37,1.94-.69,3.86-.97,5.77-.56,3.76-.9,6.75-1.01,8.96-.11,2.22,0,3.55.34,4,.34.45.88-.06,1.64-1.52.76-1.46,1.75-4.01,2.99-7.66.79-2.36,1.5-4.7,2.15-7.03.64-2.33,1.19-4.4,1.64-6.23.45-1.82.9-3.88,1.35-6.19.11-.67.46-1.21,1.05-1.6.59-.39,1.22-.5,1.89-.34.73.11,1.28.45,1.64,1.01.36.56.49,1.21.38,1.94,0,.11-.13.76-.38,1.94-.25,1.18-.6,2.69-1.05,4.54-.45,1.85-1,3.96-1.64,6.31-.65,2.36-1.39,4.74-2.23,7.15-1.96,5.95-3.99,10.55-6.06,13.8-.84,1.35-1.88,2.58-3.11,3.7-1.24,1.12-2.53,1.94-3.87,2.44s-2.69.57-4.04.21c-1.35-.37-2.55-1.36-3.62-2.99ZM159.47,36.97c0,1.29-.58,2.41-1.73,3.37-1.15.95-2.54,1.43-4.17,1.43s-3.03-.48-4.21-1.43c-1.18-.95-1.77-2.08-1.77-3.37s.59-2.4,1.77-3.32,2.58-1.39,4.21-1.39,3.02.46,4.17,1.39c1.15.93,1.73,2.03,1.73,3.32Z"/>
                                <Path fill="#0BDC05" d="M163.84,40.5c0-1.85.14-3.65.42-5.39.28-1.74.75-3.28,1.39-4.63.65-1.35,1.47-2.43,2.48-3.24,1.01-.81,2.27-1.22,3.79-1.22s2.69.29,3.7.88c1.01.59,1.81,1.38,2.4,2.36.59.98,1,2.08,1.22,3.28.22,1.21.31,2.43.25,3.66-.11,1.8-.41,3.56-.88,5.3-.48,1.74-1.02,3.42-1.64,5.05.73.34,1.61.48,2.65.42,1.04-.06,2.1-.21,3.2-.46,1.09-.25,2.12-.59,3.07-1.01.95-.42,1.71-.85,2.27-1.3.11-.06.21-.11.29-.17.08-.06.18-.11.29-.17.95-.9,2.16-1.36,3.62-1.39,1.46-.03,2.83.17,4.12.59,1.29.42,2.36,1.01,3.2,1.77.84.76,1.15,1.47.93,2.15-.06-.17-.29.51-.72,2.02s-.93,3.47-1.51,5.85c-.59,2.39-1.18,5.01-1.77,7.87-.59,2.86-1.09,5.51-1.52,7.96-.42,2.44-.7,4.49-.84,6.14-.14,1.65-.04,2.48.29,2.48.5,0,1.14-.59,1.89-1.77.76-1.18,1.53-2.66,2.32-4.46.79-1.79,1.58-3.79,2.4-5.98s1.57-4.32,2.27-6.4c.7-2.08,1.32-3.94,1.85-5.6.53-1.65.91-2.85,1.14-3.58.22-.62.63-1.11,1.22-1.47.59-.36,1.22-.46,1.89-.29.67.17,1.19.56,1.56,1.18.36.62.43,1.26.21,1.94-.34,1.12-.87,2.88-1.6,5.26-.73,2.39-1.61,5.01-2.65,7.87-1.04,2.86-2.25,5.82-3.62,8.88-1.38,3.06-2.89,5.85-4.54,8.38-1.66,2.52-3.45,4.59-5.39,6.19-1.94,1.6-4,2.4-6.19,2.4-2.02,0-3.54-.74-4.54-2.23-1.01-1.49-1.64-3.42-1.89-5.81-.25-2.39-.24-5.08.04-8.08.28-3,.71-6.03,1.3-9.09.59-3.06,1.23-5.99,1.94-8.8.7-2.8,1.33-5.22,1.89-7.24-.73.23-1.47.42-2.23.59-.76.17-1.53.34-2.31.5-2.13.45-4.25.63-6.36.55-2.1-.08-4-.55-5.68-1.39s-3.04-2.1-4.08-3.79c-1.04-1.68-1.56-3.87-1.56-6.57ZM173.35,41.43c.45-1.4.81-2.97,1.09-4.71.28-1.74.05-3.25-.67-4.55-.9,1.35-1.29,2.88-1.18,4.59.11,1.71.36,3.27.76,4.67Z"/>
                                <Path fill="#0BDC05" d="M225.71,73.16c0,1.29-.58,2.41-1.73,3.37-1.15.96-2.54,1.43-4.17,1.43s-3.03-.48-4.21-1.43c-1.18-.95-1.77-2.08-1.77-3.37s.59-2.4,1.77-3.32,2.58-1.39,4.21-1.39,3.02.46,4.17,1.39,1.73,2.03,1.73,3.32ZM216.95,15.25c.06-1.35.43-2.47,1.14-3.37.7-.9,1.56-1.58,2.57-2.06,1.01-.48,2.1-.74,3.28-.8,1.18-.06,2.26.06,3.24.34.98.28,1.81.73,2.48,1.35.67.62.98,1.35.93,2.19-.17,4.71-.34,8.92-.51,12.63-.17,3.7-.38,7.28-.63,10.73s-.53,6.97-.84,10.56c-.31,3.59-.63,7.6-.97,12.04-.06.84-.45,1.51-1.18,2.02-.73.51-1.61.84-2.65,1.01-1.04.17-2.13.17-3.28,0-1.15-.17-2.22-.5-3.2-1.01-.98-.5-1.78-1.18-2.4-2.02-.62-.84-.87-1.82-.76-2.95.39-4.54.74-8.75,1.05-12.63.31-3.87.58-7.46.8-10.77.22-3.31.41-6.4.55-9.26.14-2.86.27-5.53.38-8Z"/>
                               </Svg>
                            </TouchableOpacity>

) : (
    < ></>
)}
                        </>
                    )}
                     </SafeAreaView>
                </ImageBackground>
            )}
        </Observer>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: black,
    },
    safeContainer:{
        flex: 1,
    },
    winnerModalContent:{
        alignContent: 'center',
        top: Layout.screen.height / 3.6,
    },
    header: {
        flexDirection: 'row',
        width: '100%',
        padding: 20,
        alignItems: 'center',
    },
    carkview: {
        flex: 1,
        backgroundColor: white,

    },
    countdownTextBig: {
        fontSize: 120,
        fontWeight: 'bold',
        textAlign: 'center',
        color: '#0BDC05',
    },
    countares: {
        position: 'absolute',
        bottom: '45%',
        alignContent: 'center',
        alignItems: 'center',
        alignSelf: 'center'
    },
    giftWheelHeaderLogo: {
        height: 30,
        marginLeft: 20,
        width: 125,
        top: 5,
    },
    countDownView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 8,
        width: Layout.screen.width / 1.6,
    },
    winnerImage: {
        width: 50,
        height: 50,
    },
    startButton: {
        padding: 10,
        borderRadius: 5,
        width: '50%',
        left: '25%',
        right:'25%',
        height: 75,
        marginBottom: 100,
        alignItems: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: Layout.screen.width,
    },
    loadingGif: {
        width: 250,
        height: 55,
    },
    countDownText: {
        fontSize: 22,
        fontWeight: 'bold',
        color: white,
    },
    winnerModal: {
        width: '100%',
        zIndex: 10,
    },
    winnerBackground: {
        alignSelf: 'center',
        marginTop: -30,
        height: Layout.screen.height / 1.1,
        alignItems: 'center',
    },
    winnerTitle: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
    },
    winnerName: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        marginTop: 10,
        width: Layout.screen.width / 1.6,
        letterSpacing: 1.1,
        textAlign: 'center',
    },
    quoteText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        marginTop: 10,
        width: Layout.screen.width / 1.6,
        letterSpacing: 1.1,
        textAlign: 'center',
    },
    quoteAuthor: {
        color: white,
        fontSize: 13,
        width: Layout.screen.width / 1.8,
        textAlign: 'right',
        fontWeight: 'bold',
        marginTop: 15,
    },
    countDownContainer: {
        width: Layout.screen.width / 1.2,
        paddingVertical: 30,
        alignItems: 'center',
        top: 50
    },
    newPrizeText: {
        color: white,
        fontSize: 13,
        fontWeight: 'bold',
        letterSpacing: 2.3,
    },
    countDownItem: {
        height: 56.88,
        width: 56.34,
        alignItems: 'center',
        justifyContent: 'center',
    },
    countDownLabel: {
        position: 'absolute',
        bottom: -22,
        fontFamily: 'MADE TOMMY',
        color: white,
    },
    winnerInfo: {
        alignItems: 'center', // İçerideki tüm elemanları ortala
        justifyContent: 'center',
    },
    
});

export default Wheel;
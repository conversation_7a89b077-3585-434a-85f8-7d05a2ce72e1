import React from "react";
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { Spinner } from "native-base";
import { SafeAreaView } from "react-native-safe-area-context";
import { black, blue_t4, black_t4, blue_t1, brown_t1, brown_t2, green_t1, white, yellow_t1, yellow_t2 } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import { MainStore } from "../../../stores/MainStore";
import { get, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { screens } from "../../../navigation";
const dayjs = require("dayjs");

const Notifications: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation: any = useNavigation<screens>();

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    const [notifications, setNotifications] = React.useState([]);

    const getNotification = () => {
        setLoading(true);
        get("notifications").then((res: any) => {
            if (res.type == "success") {
                const sortedNotifications = res.notifications.sort((a: any, b: any) =>
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                );
                setNotifications(sortedNotifications);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        });
    }

    React.useEffect(() => {
        getNotification();
    }, [])

    return (
        <SafeAreaView style={styles.main}>
            {/* HEADER */}
            <HeaderFour
                navigation={navigation}
                title={MainStore.language.notifications}
            />

            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>

            <ScrollView>
                {
                    loading ?
                        <Spinner color={blue_t4} size={18} mt={70} />
                        :
                        notifications.length < 1 ?
                            <Text style={{ fontFamily: 'MADE TOMMY', fontSize: 18, textAlign: 'center', color: "black" }}>Henüz bir bildirim almadınız :(</Text>
                            :
                            <>
                                {
                                    notifications.map((item: any, index: any) => {
                                        return (
                                            <TouchableOpacity
                                                onPress={() => {
                                                    if (item.type == "announcement") {
                                                        navigation.navigate("Root")
                                                    } else if (item.type == "hollychat") {
                                                        navigation.navigate("HollyChat");
                                                    } else if (item.type == "hollysnap") {
                                                        navigation.navigate("HollySnap");
                                                    } else if (item.type == "hollyshop" && item.innerType == "order") {
                                                        navigation.navigate("MyOrders");
                                                    } else {
                                                        navigation.navigate("HollyShopDetail", { productId: item.target });
                                                    }
                                                }}
                                                key={index}
                                                style={
                                                    [
                                                        styles.notifyView,
                                                        {
                                                            marginTop: 10,
                                                            backgroundColor:
                                                                item.type == "announcement" ? green_t1 :
                                                                    item.type == "hollyshop" ? brown_t2 :
                                                                        item.type == "hollysnap" ? yellow_t2 :
                                                                            item.type == "hollychat" ? blue_t1 :
                                                                                black

                                                        }
                                                    ]
                                                }
                                            >
                                                <Text style={styles.date}>{dayjs(item.createdAt).format("DD.MM.YYYY hh:mm")}</Text>
                                                <View style={styles.notifyAltView}>
                                                    <View style={styles.notifyWhiteArea}>
                                                        <Image
                                                            style={styles.notifyNIcon}
                                                            resizeMode="contain"
                                                            source={
                                                                item.type == "announcement" ? require('../../../assets/notifications/nGreen.png') :
                                                                    item.type == "hollyshop" ? require('../../../assets/notifications/nBrown.png') :
                                                                        item.type == "hollysnap" ? require('../../../assets/notifications/nYellow.png') :
                                                                            item.type == "hollychat" ? require('../../../assets/notifications/nBlue.png') :
                                                                                require('../../../assets/notifications/nBlack.png')

                                                            }
                                                        />
                                                    </View>
                                                    <View style={styles.notifyTextsView}>
                                                        <Text style={styles.notifyTitle}>{item.title}</Text>
                                                        <Text style={styles.notifyDesc}>{item.content}</Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        )
                                    })
                                }
                                
                            </>
                }
            </ScrollView>
        </SafeAreaView>
    )
}
export default Notifications;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        backgroundColor: '#efefef',
        flexGrow: 1
    },
    toastView: {
        top: -100,
        alignSelf: 'center',
        zIndex: 20
    },
    notifyView: {
        backgroundColor: blue_t1,
        borderRadius: 16,
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        paddingHorizontal: 10,
        paddingVertical: 15
    },
    date: {
        fontSize: 12,
        color: white,
        position: 'absolute',
        top: 5,
        right: 10
    },
    notifyAltView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    notifyWhiteArea: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center'
    },
    notifyNIcon: {
        width: 24,
        height: 38
    },
    notifyTextsView: {
        marginLeft: 10,
    },
    notifyTitle: {
        fontSize: 14,
        color: white,
        fontWeight: 'bold'
    },
    notifyDesc: {
        fontSize: 14,
        width: Layout.screen.width / 1.4,
        color: white,
        marginTop: 3
    },
});
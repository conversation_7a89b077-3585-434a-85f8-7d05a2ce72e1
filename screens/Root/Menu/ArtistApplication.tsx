import React from "react";
import { StyleSheet, ImageBackground, View, Text, ScrollView } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import { gray_t4, gray_t7, green_t1, kremren<PERSON>, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import MenuProfileHeader from "../../../components/MenuProfileHeader";
import SettingsFirstInput from "../../../components/SettingsFirstInput";
import SettingsSecondInput from "../../../components/SettingsSecondInput";
import { <PERSON><PERSON>, Spinner } from "native-base";
import { shadow } from "../../../constants/Shadow";
import SettingsSelect from "../../../components/SettingsSelect";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { LinkIcon, WriteIcon } from "../../../components/Svgs";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";


const ArtistApplication: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [buttonLoading, setButtonLoading] = React.useState(false);

    // -- PROFILE INFO -- //
    const [nameLastName, setNameLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [dateOfBirth, setDateOfBirth] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [city, setCity] = React.useState("");

    // -- APPLICATION -- //
    const [description, setDescription] = React.useState("");
    const [videos, setVideos] = React.useState("");
    const [verify, setVerify] = React.useState(false);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const getProfile = () => {
        get('users/profile').then((res: any) => {
            if (res.type == "success") {
                setNameLastName(`${res.user.firstName} ${res.user.lastName}`);
                setEmail(res.user.email);
                setDateOfBirth(res.user.dateOfBirth);
                setPhoneNumber(res.user.phoneNumber);
                setCity(res.user.city);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getProfile();
    }, []);

    const postApplication = () => {
        if (!verify) {
            startToast("Kimlik bilgilerinizi onaylayın!", "error");
            return;
        }
        setButtonLoading(true);
        const videosLength = videos.length;
        const descriptionLength = description.length;
        const isvideosValid = videosLength > 1 && videosLength < 1000;
        const isdescriptionValid = descriptionLength > 1 && descriptionLength < 1000;
        if (isvideosValid && isdescriptionValid) {
            post('apply-performer', {
                videos,
                description
            }).then((res: any) => {
                if (res.type == "success") {
                    startToast("Başvurunuz gönderildi", "success");
                    setTimeout(() => {
                        navigation.pop();
                    }, 2000);
                } else {
                    startToast(res.error, "error");
                    setButtonLoading(false);
                }
            })
        } else {
            let errorMessage = "";
            if (!isvideosValid) {
                errorMessage += "Video linki 8 ile 1000 karakter arasında olmalıdır. ";
            }
            if (!isdescriptionValid) {
                errorMessage += "Açıklama 25 ile 1000 karakter arasında olmalıdır.";
            }
            startToast(errorMessage, "error");
            setButtonLoading(false);
        }
    }

    const LoadingSkeleton = () => {
        return (
            
            <ScrollView 
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.skeletonContainer}

            >
                <View style={[styles.form2, styles.skeletonForm]}>

                {/* Profile Header Skeleton */}
                <SkeletonPlaceholder 
                    backgroundColor="#F5F5F5" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item 
                        flexDirection="row" 
                        padding={20}
                        backgroundColor={white}
                        borderRadius={15}
                        marginHorizontal={15}
                        height={160}
                    >
                        {/* Profile Info */}
                        <SkeletonPlaceholder.Item marginLeft={15} flex={1}>
                            <SkeletonPlaceholder.Item
                                width="80%"
                                height={12}
                                borderRadius={6}
                            />
                            <SkeletonPlaceholder.Item
                                width="60%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                            <SkeletonPlaceholder.Item
                                width="40%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                </View>
    
                {/* Form Skeleton */}
                <View style={[styles.form, styles.skeletonForm]}>
                    <SkeletonPlaceholder 
                        backgroundColor="#F5F5F5" 
                        highlightColor="#FFFFFF"
                        speed={1200}
                    >
                        {/* Subject Input */}
                        <SkeletonPlaceholder.Item>
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={90}
                                borderRadius={15}
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={90}
                                borderRadius={15}
                                marginTop={30}
                            />
    
                            {/* Submit Button */}
                            <SkeletonPlaceholder.Item
                                width={180}
                                height={45}
                                borderRadius={15}
                                alignSelf="center"
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder>
                </View>
            </ScrollView>
        );
    };


    return (
        <View
            style={styles.main}
        >
            <SafeAreaView style={styles.safeAreaView}>
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>
                {/* HEADER */}
                <HeaderFour
                    headerStatus={header}
                    navigation={navigation}
                    title={"SANATÇI BAŞVURU"}
                />
                {
                    loading ? <LoadingSkeleton /> : (
                        <ScrollView
                            onScroll={handleScroll}
                            showsVerticalScrollIndicator={false}
                        >
                            {/* PROFILE HEADER */}
                            <MenuProfileHeader
                                nameLastName={nameLastName}
                                email={email}
                                dateOfBirth={dateOfBirth}
                                phoneNumber={phoneNumber}
                                city={city}
                                setVerify={setVerify}
                            />

                            {/* FORM */}
                            <View style={[styles.form, shadow, { marginBottom: 150 }]}>

                                {/* YOU ARE YOUR GROUP ( MUSIC ) */}
                                <Text style={[styles.titles, { marginBottom: 25 }]}>Kendinizden ya da grubunuzdan bahsediniz</Text>

                                <SettingsSecondInput
                                    icon={<WriteIcon size={5} color={'transparent'} />}
                                    number={100}
                                    setText={setDescription}
                                />

                                {/* LINK */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 25 }]}>Size ait video linkleriniz</Text>

                                <SettingsSecondInput
                                    icon={<LinkIcon size={5} color={'transparent'} />}
                                    number={100}
                                    setText={setVideos}
                                    placeholder="https://youtu.be/_Hqfjb3y6Fs,https://youtu.be/a-YdL5WsuSM"
                                />

                                {/* BUTTON */}
                                {buttonLoading ? <Spinner color={gray_t4} size={18} mt={5} /> : (
                                    <Button
                                        onPress={() => {
                                            postApplication();
                                        }}
                                        style={styles.button}
                                    >
                                        <Text style={styles.buttonText}>gönder</Text>
                                    </Button>
                                )}
                            </View>
                        </ScrollView>
                    )
                }

            </SafeAreaView>
        </View>
    )
}
export default ArtistApplication;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi,
     },
    safeAreaView: { flexGrow: 1
     },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    skeletonContainer: {
        paddingTop: 50, // Added top padding
    },
    skeletonForm: {
        backgroundColor: white, // Changed form background to white
        marginTop: 25,
    },
    form2: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 40
    },
    
    form: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        paddingBottom: 50,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20
    },
    titles: {
        marginLeft: 10,
        fontWeight: 'bold',
        marginBottom: 15
    },
    button: {
        position: 'absolute',
        alignSelf: 'center',
        bottom: -20,
        backgroundColor: green_t1,
        width: 180,
        height: 45,
        borderRadius: 15
    },
    buttonText: {
        fontSize: 18,
        color: white,
        fontFamily: 'MADE TOMMY'
    }
});
import React from "react";
import { StyleSheet, ImageBackground, View, Text, ScrollView, TouchableOpacity, Image } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import { gray_t4, gray_t7, green_t1, kremrengi, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import MenuProfileHeader from "../../../components/MenuProfileHeader";
import SettingsFirstInput from "../../../components/SettingsFirstInput";
import SettingsSecondInput from "../../../components/SettingsSecondInput";
import { <PERSON><PERSON>, Spin<PERSON> } from "native-base";
import { shadow } from "../../../constants/Shadow";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import SettingsSelect from "../../../components/SettingsSelect";
import { launchImageLibrary } from 'react-native-image-picker'
import { WriteIcon } from "../../../components/Svgs";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import UploadPhotoIcon from "./CustomUploadIcon";
import CustomUploadIcon from "./CustomUploadIcon";

const Franchise: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingFranchises, setLoadingFranchises] = React.useState(true);
    const [buttonLoading, setButtonLoading] = React.useState(false);

    // -- PROFILE INFO -- //
    const [nameLastName, setNameLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [dateOfBirth, setDateOfBirth] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [city, setCity] = React.useState("");

    // -- FRANCHISE -- //
    const [franchises, setFranchises] = React.useState([]);
    const [franchise, setFranchise] = React.useState("");
    const [address, setAddress] = React.useState("");
    const [landPhone, setLandPhone] = React.useState("");
    const [currentJob, setCurrentJob] = React.useState("");
    const [experience, setExperience] = React.useState("");
    const [q1, setQ1] = React.useState("");
    const [q2, setQ2] = React.useState("");
    const [property, setProperty] = React.useState("");
    const [areaSize, setAreaSize] = React.useState(0);
    const [floorCount, setFloorCount] = React.useState(0);
    const [roofHeight, setRoofHeight] = React.useState(0);
    const [buildingAddress, setBuildingAddress] = React.useState("");
    const [investmentAmount, setInvestmentAmount] = React.useState("");
    const [imageUris, setImageUris] = React.useState<any[]>([]); // Birden fazla fotoğraf için dizi
    const [imageType, setImageType] = React.useState("");
    const [imageName, setImageName] = React.useState("");
    const [verify, setVerify] = React.useState(false);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    // -- CHOOSE IMAGE FUNCTION -- //
    const setImages = () => {
        launchImageLibrary({
            mediaType: "photo",
            maxHeight: 600,
            maxWidth: 600,
            quality: 1,
            selectionLimit: 10 // 📌 Maksimum 10 resim seçilebilir
        }, (callback: any) => {
            if (callback.didCancel) {
                startToast("Lütfen fotoğraf seçiniz.", "error");
            } else {
                const selectedImages = callback?.assets?.map((img: any) => ({
                    uri: img.uri,
                    type: img.type,
                    name: img.fileName
                }));
    
                setImageUris(prevImages => [...prevImages, ...selectedImages]); // 📌 Mevcut resimlere ekle
            }
        });
    };
    

    // -- GET FRANCHISES -- //
    const getFranchises = () => {
        get('franchises').then((res: any) => {
            if (res.type == "success") {
                setFranchises(res.franchises);
                setLoadingFranchises(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    // -- GET PROFILE -- //
    const getProfile = () => {
        get('users/profile').then((res: any) => {
            if (res.type == "success") {
                setNameLastName(`${res.user.firstName} ${res.user.lastName}`);
                setEmail(res.user.email);
                setDateOfBirth(res.user.dateOfBirth);
                setPhoneNumber(res.user.phoneNumber);
                setCity(res.user.city);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    const sendFranchise = () => {
        if (!verify) {
            startToast("Kimlik bilgilerinizi onaylayın!", "error");
            return;
        }
        if (!franchise ||
            !address ||
            !currentJob ||
            !experience ||
            !q1 ||
            !q2 ||
            !property ||
            areaSize == 0 ||
            floorCount == 0 ||
            roofHeight == 0 ||
            !investmentAmount) {
            startToast("Tüm alanları doldurun!", "error");
            return;
        }
        setButtonLoading(true);
        post("apply-franchise", {
            franchise,
            address,
            landPhoneNumber: landPhone,
            currentJob,
            experience,
            q1,
            q2,
            property,
            areaSize,
            floorCount,
            roofHeight,
            buildingAddress,
            investmentAmount
        }).then((res: any) => {
            if (res.type == "success") {
                startToast("Fotoğraflar yükleniyor. Lütfen bekleyin!", "success");
    
                const formData = new FormData();
                imageUris.forEach((image, index) => {
                    formData.append(`image${index}`, {
                        uri: image.uri,
                        type: image.type,
                        name: image.name
                    });
                });
    
                formData.append('fileName', `franchise/${res.id}`);
    
                post("functions/upload", formData).then((resp: any) => {
                    if (resp.type == "success") {
                        startToast("Fotoğraflar başarılı bir şekilde yüklendi!", "success");
                        setTimeout(() => {
                            navigation.pop();
                        }, 2000);
                    } else {
                        startToast(resp.error, "error");
                        setButtonLoading(false);
                    }
                });
            } else {
                startToast(res.error, "error");
                setButtonLoading(false);
            }
        });
    };
    const LoadingSkeleton = () => {
        return (
            
            <ScrollView 
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.skeletonContainer}

            >
                <View style={[styles.form2, styles.skeletonForm]}>

                {/* Profile Header Skeleton */}
                <SkeletonPlaceholder 
                    backgroundColor="#F5F5F5" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item 
                        flexDirection="row" 
                        padding={20}
                        backgroundColor={white}
                        borderRadius={15}
                        marginHorizontal={15}
                        height={160}
                    >
                        {/* Profile Info */}
                        <SkeletonPlaceholder.Item marginLeft={15} flex={1}>
                            <SkeletonPlaceholder.Item
                                width="80%"
                                height={12}
                                borderRadius={6}
                            />
                            <SkeletonPlaceholder.Item
                                width="60%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                            <SkeletonPlaceholder.Item
                                width="40%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                </View>
    
                {/* Form Skeleton */}
                <View style={[styles.form, styles.skeletonForm]}>
                    <SkeletonPlaceholder 
                        backgroundColor="#F5F5F5" 
                        highlightColor="#FFFFFF"
                        speed={1200}
                    >
                        {/* Subject Input */}
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={130}
                                borderRadius={15}
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={30}
                                borderRadius={15}
                                marginTop={30}
                            />
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={30}
                                borderRadius={15}
                                marginTop={30}
                            />
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={30}
                                borderRadius={15}
                                marginTop={30}
                            />
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={30}
                                borderRadius={15}
                                marginTop={30}
                            />
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={30}
                                borderRadius={15}
                                marginTop={30}
                            />
    
                            {/* Submit Button */}
                            <SkeletonPlaceholder.Item
                                width={180}
                                height={45}
                                borderRadius={15}
                                alignSelf="center"
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder>
                </View>
            </ScrollView>
        );
    };


    React.useEffect(() => {
        getProfile();
        getFranchises();
    }, []);

    return (
        <View
            style={styles.main}
        >
            <SafeAreaView style={styles.safeAreaView}>
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>
                {/* HEADER */}
                <HeaderFour
                    headerStatus={header}
                    navigation={navigation}
                    title={"FRANCHISE"}
                />

                {
                    loading || loadingFranchises ? <LoadingSkeleton /> : (
                        <ScrollView onScroll={handleScroll} showsVerticalScrollIndicator={false}>
                            {/* PROFILE HEADER */}
                            <MenuProfileHeader
                                nameLastName={nameLastName}
                                email={email}
                                dateOfBirth={dateOfBirth}
                                phoneNumber={phoneNumber}
                                city={city}
                                setVerify={setVerify}
                            />

                            {/* FORM */}
                            <View style={[styles.form, shadow]}>

                                {/* SELECT BOX - STATUS OF FRANCHISE */}
                                <Text style={styles.titles}>Başvurulan franchise</Text>

                                <SettingsSelect
                                    variables={franchises}
                                    setSelected={setFranchise}
                                />

                                {/* ADDRESS */}
                                <Text style={[styles.titles, { marginBottom: 25, marginTop: 20 }]}>Adresiniz</Text>

                                <SettingsSecondInput
                                    icon={<WriteIcon size={5} color={'transparent'} />}
                                    number={100}
                                    setText={setAddress}
                                />

                                {/* CONST PHONE */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Sabit numaranız (Varsa)</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setLandPhone}
                                />

                                {/* BUSINESS */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Şu An Yaptığınız İş</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setCurrentJob}
                                />

                                {/* EXPE. */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Daha önceki tecrübeleriniz</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setExperience}
                                />

                                {/* EXPE. FOR BUSINESS */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Geçmiş veya mevcut franchise sahipliğiniz var mı? Varsa bilgi verebilir misiniz?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setQ1}
                                />

                                {/* IDEA? */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>HOLLY STONE markasını ne kadar tanıyorsunuz ve neden bizi seçtiniz?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setQ2}
                                />

                                {/* INVEST? */}
                                <Text style={[styles.titles, { marginTop: 20 }]}>Yatırım yapmayı planladığınız mülkün durumu nedir?</Text>
                                <SettingsSelect
                                    variables={[{ key: "Kendi Mülküm", title: "Kendi Mülküm" }, { key: "Kiralık", title: "Kiralık" }]}
                                    setSelected={setProperty}
                                />

                                {/* m2 */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Kaç m² kapalı alana sahip?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setAreaSize}
                                />

                                {/* FLOOR */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Mekan kaç katlı?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setFloorCount}
                                />

                                {/* HEIGHT OF PLACE */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Tavan yüksekliği nedir?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setRoofHeight}
                                />

                                {/* ADDRESS OF PLACE */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Yeri belli ise mekanın adresi nedir?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setBuildingAddress}
                                />

                                {/* INVESTMENT FOR THIS BUSINESS ? */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Planlanan yatırım miktarı ne kadar?</Text>
                                <SettingsFirstInput
                                    placeHolder=""
                                    setText={setInvestmentAmount}
                                />

                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 10 }]}>Mekanın görsellerini paylaşınız</Text>

                                <View style={styles.imageUploadContainer}>
    {/* Fotoğraf Seçme Butonu */}
    <TouchableOpacity onPress={setImages} style={styles.uploadBox}>
        <CustomUploadIcon size={40} color="#888" /> 
        <Text style={styles.uploadText}>Fotoğraf Ekle</Text>
    </TouchableOpacity>

    {/* Seçilen Fotoğrafları Göster */}
    <View style={styles.imageContainer}>
        {imageUris.map((image, index) => (
            <View key={index} style={styles.imageWrapper}>
                <Image source={{ uri: image.uri }} style={styles.image} />
                <TouchableOpacity
                    onPress={() => setImageUris(prev => prev.filter((_, i) => i !== index))}
                    style={styles.deleteButton}
                >
                    <Text style={styles.deleteButtonText}>X</Text>
                </TouchableOpacity>
            </View>
        ))}
    </View>
</View>

                                {/* BUTTON */}
                                {buttonLoading ? <Spinner color={gray_t4} size={18} mt={5} /> : (
                                    <Button onPress={() => {
                                        sendFranchise();
                                    }} style={styles.button}>
                                        <Text style={styles.buttonText}>gönder</Text>
                                    </Button>
                                )}
                            </View>
                        </ScrollView>
                    )
                }

            </SafeAreaView>
        </View>
    )
}
export default Franchise;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi
 },
 imageUploadContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 10,
},
uploadBox: {
    width: 100, // 📌 Daha küçük kare boyutları
    height: 100,
    borderWidth: 2,
    borderColor: "#bbb",
    borderStyle: "dashed",
    borderRadius: 15,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    marginBottom: 10,
},
uploadText: {
    color: "#555",
    fontSize: 12, // 📌 Daha küçük font
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 5
},
imageContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    width: "100%"
},
imageWrapper: {
    width: 100, // 📌 Seçilen fotoğraflar için boyut
    height: 100,
    borderWidth: 2,
    borderColor: "#bbb",
    borderStyle: "dashed",
    borderRadius: 15,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
},
image: {
    width: "100%",
    height: "100%",
    borderRadius: 15,
},
deleteButton: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "red",
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
},
deleteButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
},
    safeAreaView: { flexGrow: 1 },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    skeletonContainer: {
        paddingTop: 50, // Added top padding
    },
    skeletonForm: {
        backgroundColor: white, // Changed form background to white
        marginTop: 25,
    },
    form2: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 40
    },
    form: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        paddingBottom: 50,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20,
        marginBottom: 100
    },
    titles: {
        marginLeft: 10,
        fontWeight: 'bold',
        marginBottom: 15
    },
    button: {
        position: 'absolute',
        alignSelf: 'center',
        bottom: -20,
        backgroundColor: green_t1,
        width: 180,
        height: 45,
        borderRadius: 15
    },
    buttonText: {
        fontSize: 18,
        color: white,
        fontFamily: 'MADE TOMMY'
    }
});
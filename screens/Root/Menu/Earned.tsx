import React, { useEffect } from "react";
import { ImageBackground, StyleSheet, View, Text } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import EarnedList from "../../../components/EarnedList";
import { green_t1, kremrengi } from "../../../constants/Color";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { ScrollView } from "react-native-gesture-handler";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { HollyPointsStoreInstance } from "../../../stores/HollyPointsStore"; // Store importu


const Earned: React.FC = () => {

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    }

    const [Kazandiklarim, setKazandiklarim]: any = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- GET USER INFO -- //
    const getUserData = () => {
        try {
            get("/users/kazandiklarim").then((res: any) => {
                if (res.type == "success") {
                    setKazandiklarim(res.data.kazandiklarim);
                    setLoading(false);
                } else {
                    startToast(res.error, "error");
                    setTimeout(() => {
                        setLoading(false);
                        navigation.pop();
                    }, 1500);
                }
            });
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 1500);
        }
    }

    useEffect(() => {
        getUserData();
    }, []);

    const handleTransfer = async (prizeId: number, prizeType: string, closeModal: () => void) => {
        // Include prizeType (shaman or wheel) in the request
        const res: any = await post("holly-points/transferHollyPoints", { prizeId, prizeType });
        if (res.type === "success") {
            startToast("Holly Points başarıyla hesabınıza aktarıldı.", "success");
            closeModal();
            await HollyPointsStoreInstance.fetchHollyPoints(); // Store güncelle
            getUserData(); // Veriyi yeniden çek
        } else {
            startToast("Aktarım başarısız. Lütfen tekrar deneyin.", "error");
        }
    };

    return (
        <View style={styles.main}>
            <View style={styles.toastView}>
            <Toast
                                        type={typeToast}
                                        subtitle={subtitleToast}
                                        status={statusToast}
                                        successColor={green_t1}
                                    />
            </View>


                <HeaderFour
                    navigation={navigation}
                    title="KAZANDIKLARIM" headerStatus={true} />

                    <SafeAreaView>
                    <ScrollView>
                        <View style={styles.mainn}>
                            {loading ? (
                                <SkeletonPlaceholder>
                                    <SkeletonPlaceholder.Item marginTop={20}>
                                        <SkeletonPlaceholder.Item  borderBottomLeftRadius={25} borderBottomRightRadius={25}  borderTopEndRadius={25} borderTopStartRadius={25} height={170} borderRadius={4} marginLeft={25} marginRight={25} />
                                    </SkeletonPlaceholder.Item>
                                    <SkeletonPlaceholder.Item marginTop={20}>
                                    <SkeletonPlaceholder.Item  borderBottomLeftRadius={25} borderBottomRightRadius={25}  borderTopEndRadius={25} borderTopStartRadius={25} height={170} borderRadius={4} marginLeft={25} marginRight={25} />
                                    </SkeletonPlaceholder.Item>
                                    <SkeletonPlaceholder.Item marginTop={20}>
                                    <SkeletonPlaceholder.Item  borderBottomLeftRadius={25} borderBottomRightRadius={25}  borderTopEndRadius={25} borderTopStartRadius={25} height={170} borderRadius={4} marginLeft={25} marginRight={25} />
                                    </SkeletonPlaceholder.Item>
                                    <SkeletonPlaceholder.Item marginTop={20}>
                                    <SkeletonPlaceholder.Item  borderBottomLeftRadius={25} borderBottomRightRadius={25}  borderTopEndRadius={25} borderTopStartRadius={25} height={170} borderRadius={4} marginLeft={25} marginRight={25} />
                                    </SkeletonPlaceholder.Item>

                                </SkeletonPlaceholder>

                            ) : (
                                Kazandiklarim && Kazandiklarim.length > 0 ? (
                                    <EarnedList kazandiklarim={Kazandiklarim} onTransfer={handleTransfer} />
                                ) : (
                                    <Text style={styles.noRewardsText}>Üzgünüz. Henüz ödül kazanmadınız.</Text>
                                )
                            )}
                        </View>
                    </ScrollView>
            </SafeAreaView>
        </View>
    );
}
export default Earned;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi
    },
    toastView:{
        position: 'absolute',
        top: 10,
        zIndex:99999,
        alignContent: 'center',
        alignItems: 'center',
        alignSelf: 'center'

    },
    mainn: {
        marginTop: 70,
    },
    noRewardsText: {
        textAlign: 'center',
        marginVertical: 20,
        fontSize: 16,
        color: 'gray',
    },
});

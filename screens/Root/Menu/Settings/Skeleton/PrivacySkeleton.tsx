import React from "react";
import { StyleSheet, View } from "react-native";
import { Skeleton } from "native-base";
import Layout from "../../../../../constants/Layout";
import { kremrengi, white } from "../../../../../constants/Color";

const PrivacySkeleton = () => {
    return (
        <View style={styles.main}>
            {/* Skeleton Header */}
            <Skeleton.Text
                lines={50}
                alignSelf="center"
                w={Layout.screen.width / 1.1}
                mb={1}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
        paddingVertical: 20,
        paddingHorizontal: 10,
        top: 80,
    },
});

export default PrivacySkeleton;

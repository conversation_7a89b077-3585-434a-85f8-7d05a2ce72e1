import React from "react";
import { StyleSheet, View } from "react-native";
import { Skeleton } from "native-base";
import Layout from "../../../../../constants/Layout";
import { kremrengi, white } from "../../../../../constants/Color";

const CreditCardsSkeleton = () => {
    return (
        <View style={styles.main}>
            {/* Skeleton Header */}
            <Skeleton.Text
                lines={1}
                alignSelf="center"
                w={Layout.screen.width / 2}
                mb={5}
            />
            <Skeleton.Text
                lines={1}
                alignSelf="center"
                w={Layout.screen.width / 2}
                mb={5}
            />

            {/* Skeleton Credit Cards */}
            

            {/* Skeleton Add Card Button */}
            <View style={[styles.skeletonButton, styles.shadow, styles.alignLeftButton]}>
                <Skeleton.Text lines={1} w="50%" alignSelf="center" />
            </View>

            {/* Skeleton Info Section */}
            <View style={[styles.skeletonInfo, styles.shadow]}>
                <Skeleton.Text lines={3} w="90%" alignSelf="center" />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
        paddingVertical: 20,
        top: 80,
        paddingHorizontal: 10,
    },
    alignLeftButton: {
        alignSelf: "flex-end",
        marginRight: 40,
    },
    skeletonCard: {
        height: 70,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginBottom: 15,
        paddingHorizontal: 10,
    },
    skeletonButton: {
        height: 30,
        width: 80,
        backgroundColor: white,
        borderRadius: 10,
        justifyContent: "center",
        alignSelf: "center",
        alignItems: "center",
        marginTop: 20,
    },
    skeletonInfo: {
        height: 100,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginTop: 20,
    },
    shadow: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
    },
});

export default CreditCardsSkeleton;

import React from "react";
import { StyleSheet, View } from "react-native";
import { Skeleton } from "native-base";
import Layout from "../../../../../constants/Layout";
import { gray_t4, kremrengi, white } from "../../../../../constants/Color";

const AddAddressSkeleton = () => {
    return (
        <View style={styles.main}>
            {/* Skeleton Header */}
            <Skeleton.Text
                lines={1}
                alignSelf="center"
                w={Layout.screen.width / 2}
                mb={5}
            />

            {/* Skeleton Input Fields */}
            {[...Array(8)].map((_, index) => (
                <View key={index} style={[styles.skeletonInput, styles.shadow]}>
                    <Skeleton h={5} w="90%" alignSelf="center" />
                </View>
            ))}

            {/* Skeleton Button */}
            <View style={[styles.skeletonButton, styles.shadow]}>
                <Skeleton.Text lines={1} w="40%" alignSelf="center" />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
        paddingVertical: 20,
        paddingHorizontal: 10,
    },
    skeletonInput: {
        height: 50,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginBottom: 15,
    },
    skeletonButton: {
        height: 50,
        width: Layout.screen.width / 1.5,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginTop: 20,
    },
    shadow: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
    },
});

export default AddAddressSkeleton;

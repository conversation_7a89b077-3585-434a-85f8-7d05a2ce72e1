import React from "react";
import { StyleSheet, View } from "react-native";
import { Skeleton } from "native-base";
import Layout from "../../../../../constants/Layout";
import { kremrengi, white } from "../../../../../constants/Color";

const ChangeCitySkeleton = () => {
    return (
        <View style={styles.main}>
            {/* Skeleton Header */}
            <Skeleton.Text
                lines={1}
                alignSelf="flex-start"
                w={Layout.screen.width / 2}
                mb={2}
            />

            {/* Skeleton Current City */}
            <View style={[styles.skeletonCity, styles.shadow]}>
                <Skeleton.Text lines={1} alignSelf="center" w="100%" mb={2} />
            </View>
            
            <Skeleton.Text
                lines={1}
                alignSelf="flex-start"
                w={Layout.screen.width / 2}
                mb={2}
            />

            {/* Skeleton Available Cities */}
            {[...Array(5)].map((_, index) => (
                <View key={index} style={[styles.skeletonCity, styles.shadow]}>
                    <Skeleton.Text lines={1} w="100%" mb={2} alignSelf="center" />
                </View>
            ))}

            {/* Skeleton Button */}
            <View style={[styles.skeletonButton, styles.shadow]}>
                <Skeleton.Text lines={1} w="50%" alignSelf="center" />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
        paddingVertical: 20,
        paddingHorizontal: 10,
    },
    skeletonCity: {
        height: 40,
        width: '100%',
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginBottom: 15,
        paddingHorizontal: 10,
    },
    skeletonButton: {
        height: 50,
        width: Layout.screen.width / 2,
        backgroundColor: white,
        borderRadius: 10,
        justifyContent: "center",
        alignSelf: "center",
        marginTop: 20,
    },
    shadow: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
    },
});

export default ChangeCitySkeleton;

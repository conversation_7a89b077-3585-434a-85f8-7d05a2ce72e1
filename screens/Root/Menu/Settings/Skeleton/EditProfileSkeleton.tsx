import React from "react";
import { StyleSheet, View, Text } from "react-native";
import { Skeleton } from "native-base";
import Layout from "../../../../../constants/Layout";
import { gray_t4, kremrengi, white } from "../../../../../constants/Color";

const EditProfileSkeleton = () => {
    return (
        <View style={styles.main}>
            <Skeleton.Text
                lines={1}
                alignSelf="center"
                w={Layout.screen.width / 1.5}
                mb={5}
            />

            {[...Array(4)].map((_, index) => (
                <View key={index} style={[styles.skeletonItem, styles.shadow]}>
                    <Skeleton h={5} w="90%" rounded="full" alignSelf="center" />
                </View>
            ))}

            <View style={[styles.skeletonButton, styles.shadow]}>
                <Skeleton.Text lines={1} w="50%" alignSelf="center" />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
        paddingVertical: 20,
        top: 80,
        paddingHorizontal: 10,
    },
    skeletonItem: {
        height: 40,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginBottom: 15,
    },
    skeletonButton: {
        height: 50,
        width: Layout.screen.width / 1.5,
        backgroundColor: white,
        alignSelf: "center",
        borderRadius: 10,
        justifyContent: "center",
        marginTop: 320,
    },
    shadow: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
    },
});

export default EditProfileSkeleton;

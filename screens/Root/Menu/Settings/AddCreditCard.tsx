import React from "react";
import { StyleSheet, View, ImageBackground, Image, Text, TouchableOpacity, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import SettingsBackground from "../../../../components/SettingsBackground";
import Layout from "../../../../constants/Layout";
import { black, black_t3, brown_t2, gray_t12, gray_t4, green_t1, green_t4, kremrengi, red_t2, white, yellow_t1 } from "../../../../constants/Color";
import { shadow } from "../../../../constants/Shadow";
//@ts-ignore
import FlipCard from 'react-native-flip-card'
import { Input, Spinner } from "native-base";
import { post } from "../../../../networking/Server";
import Toast from "../../../../components/Toast";
import WebView from "react-native-webview";

const AddCreditCard: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- CARD INFORMATION -- //
    const [flip, setFlip] = React.useState(false);
    const [name, setName] = React.useState("");
    const [no, setNo] = React.useState("");
    const [month, setMonth] = React.useState("");
    const [year, setYear] = React.useState("");
    const [ccv, setCcv] = React.useState("");
    const [loading, setLoading] = React.useState(false);

    const [paytrLink, setPaytrLink] = React.useState("");
    const webview: any = React.useRef(null);

    const addCard = () => {
        setLoading(true);
        try {
            post("payment/store-card", {
                cc_owner: name,
                card_number: no,
                expiry_month: month,
                expiry_year: year,
                cvv: ccv
            }).then((res: any) => {
                if (res.type == "success") {
                    setLoading(false);
                    setPaytrLink(res.paymentLink);
                    startToast("Ödemeye aktarılıyorsunuz...", "success");
                } else {
                    setLoading(false);
                    startToast(res.error, "error");
                }
            });
        } catch (e) {
            setLoading(false);
            startToast("Bir şeyler ters gitti", "error");
        }
    }


    // -- WEBVIEW PAYTR -- //
    const handleWebViewNavigationStateChange = (params: { url: any; }) => {
        const { url } = params;
        if (url?.includes('success')) {
            //webview.current.stopLoading();
            //webview.current = null;
            setPaytrLink("");
            startToast("Kartınız başarıyla kayıt edilmiştir. Bekleyiniz..", "success");
            setTimeout(() => {
                navigation.pop();
            }, 1500);
        } else if (url?.includes('fail')) {
            startToast("İşlem reddedildi.", "error")
            setPaytrLink("");
        }
    }

    if (paytrLink) {
        return (
            <WebView
                ref={webview}
                style={styles.containerPayTr}
                source={{ uri: paytrLink }}
                onNavigationStateChange={handleWebViewNavigationStateChange}
            />
        )
    }

    return (
        <View
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>

            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={"AYARLARIM"} headerStatus={false}                />
                {
                    loading ?
                        <Spinner color={gray_t4} size={18} mt={5} />
                        :
                        <SettingsBackground
                            onPress={() => {
                                addCard();
                            }}
                            buttonTitle="Kaydet"
                            title="KART EKLE"
                            children={
                                <>
                                    <View style={styles.flipView}>
                                        <FlipCard
                                            flip={flip}
                                            flipHorizontal={true}
                                            flipVertical={false}
                                        >
                                            {/* Face Side */}
                                            <ImageBackground
                                                source={require('../../../../assets/menu/creditFront.png')}
                                                style={styles.flipFrontImage}
                                            >
                                                <Image
                                                    source={require('../../../../assets/exp/masterCard.png')}
                                                    style={styles.masterCard}
                                                />
                                                <View style={styles.cardInfoView}>
                                                    <Text style={styles.cardInfoName}>{name}</Text>
                                                    <Text style={styles.cardInfoNo}>{no.substring(0, 4)} {no.substring(4, 8)} {no.substring(8, 12)} {no.substring(12, 16)}</Text>
                                                </View>
                                                <Text style={styles.cardInfoMonth}>{month}/{year}</Text>
                                            </ImageBackground>
                                            {/* Back Side */}
                                            <ImageBackground
                                                source={require('../../../../assets/menu/creditBack.png')}
                                                style={styles.flipBackImage}
                                            >
                                                <Text style={styles.flipBackCVV}>{ccv}</Text>
                                            </ImageBackground>
                                        </FlipCard>
                                    </View>

                                    {/* INPUTS */}
                                    <View style={[styles.inputView, { marginTop: Platform.OS == "ios" ? 30 : 70 }]}>
                                        <Text style={styles.inputTitle}>kart numarası</Text>
                                        <Input
                                            backgroundColor={white}
                                            placeholder="KART NO"
                                            height={42}
                                            onChangeText={(text) => {
                                                if (text.length > 16) return;
                                                setNo(text);
                                                setFlip(false);
                                            }}
                                            value={no}
                                            alignSelf={'center'}
                                            borderRadius={10}
                                            fontWeight={'bold'}
                                            color={black}
                                        />
                                    </View>
                                    <View
                                        style={
                                            [
                                                styles.inputView,
                                                {
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between'
                                                }
                                            ]
                                        }
                                    >
                                        <View
                                            style={{
                                                width: Layout.screen.width / 2,
                                            }}
                                        >
                                            <Text style={styles.inputTitle}>kart sahibi adı soyadı</Text>
                                            <Input
                                                backgroundColor={white}
                                                placeholder="ISIM SOYISIM"
                                                height={42}
                                                onChangeText={(text) => {
                                                    setName(text);
                                                    setFlip(false);
                                                }}
                                                alignSelf={'center'}
                                                borderRadius={10}
                                                fontWeight={'bold'}
                                                color={black}
                                            />
                                        </View>
                                        <View
                                            style={{
                                                width: Layout.screen.width / 3.4
                                            }}
                                        >
                                            <Text style={styles.inputTitle}>son kullanım</Text>
                                            <Input
                                                backgroundColor={white}
                                                placeholder="17/08"
                                                height={42}
                                                onChangeText={(text) => {
                                                    setMonth(text.substring(0, 2));
                                                    setYear(text.substring(2, 4));
                                                    setFlip(false);
                                                }}
                                                value={month + year}
                                                alignSelf={'center'}
                                                borderRadius={10}
                                                fontWeight={'bold'}
                                                color={black}
                                            />
                                        </View>
                                    </View>
                                    <View style={styles.inputView}>
                                        <View
                                            style={{
                                                width: Layout.screen.width / 2,
                                            }}
                                        >
                                            <Text style={styles.inputTitle}>güvenlik kodu (CVV)</Text>
                                            <Input
                                                backgroundColor={white}
                                                placeholder="454"
                                                height={42}
                                                onChangeText={(text) => {
                                                    if (text.length > 3) return;
                                                    setCcv(text);
                                                    setFlip(true);
                                                    setFlip(false);
                                                }}
                                                value={ccv}
                                                alignSelf={'center'}
                                                borderRadius={10}
                                                fontWeight={'bold'}
                                                color={black}
                                            />
                                        </View>
                                    </View>
                                </>
                            }
                        />
                }
            </SafeAreaView>
        </View>
    )
}
export default AddCreditCard;

// -- STYLES -- //
const styles = StyleSheet.create({
    containerPayTr: { flex: 1 },
    main: { flex: 1,
    backgroundColor: kremrengi },
    toastView: {
        position: 'absolute',
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    safeAreaView: { flexGrow: 1 },
    flipView: { height: 168 },
    flipFrontImage: {
        width: Layout.screen.width / 1.2,
        height: (Layout.screen.width / 1.2) / 1.8,
        alignSelf: 'center',
        justifyContent: 'flex-end'
    },
    masterCard: {
        width: 28,
        height: 17,
        position: 'absolute',
        right: 15,
        top: 20
    },
    cardInfoView: {
        marginBottom: 30,
        marginLeft: 25,
    },
    cardInfoName: {
        color: black,
        fontSize: 13,
        letterSpacing: 1.3,
        fontFamily: 'College'
    },
    cardInfoNo: {
        fontFamily: 'College',
        color: black,
        fontSize: 16,
        letterSpacing: 1.2,
        marginTop: 15
    },
    cardInfoMonth: {
        fontFamily: 'College',
        right: 15,
        bottom: 10,
        color: black,
        fontSize: 13,
        letterSpacing: 1.2,
        position: 'absolute'
    },
    flipBackImage: {
        width: Layout.screen.width / 1.2,
        height: (Layout.screen.width / 1.2) / 1.8,
        alignSelf: 'center'
    },
    flipBackCVV: {
        fontFamily: 'College',
        right: 40,
        bottom: 10,
        color: black,
        fontSize: 13,
        letterSpacing: 1.2,
        position: 'absolute'
    },
    infoView: {
        backgroundColor: gray_t12,
        margin: 20,
        padding: 15,
        borderRadius: 10
    },
    infoText: { color: black_t3 },
    inputView: {
        width: Layout.screen.width / 1.23,
        alignSelf: 'center',
        marginTop: 15
    },
    inputTitle: {
        fontSize: 12,
        color: black_t3,
        marginLeft: 10,
        marginBottom: 2
    },
});
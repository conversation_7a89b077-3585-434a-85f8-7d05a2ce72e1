import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { screens } from "../../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import SettingsBackground from "../../../../components/SettingsBackground";
import Layout from "../../../../constants/Layout";
import { shadow } from "../../../../constants/Shadow";
import { black_t3, green_t1, kremrengi, white, yellow_t2 } from "../../../../constants/Color";
import { MainStore } from "../../../../stores/MainStore";
import tr from '../../../../localization/tr.json';
import en from '../../../../localization/en.json';
import { goPage } from "../../../../functions/goPage";

const ChangeLanguage: React.FC = () => {
    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    return (
        <View
            style={styles.main}
        >
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={"AYARLARIM"} headerStatus={false}                />
                <SettingsBackground
                    onPress={() => {

                    }}
                    buttonTitle={MainStore.language.update}
                    title="DİL DEĞİŞTİR"
                    children={<>
                        <View style={styles.form}>
                            {/* SELECTED CITY */}
                            <Text style={styles.title}>Uygulama dil ayarı:</Text>
                            <View style={[styles.cityView, shadow]}>
                                <Text style={styles.cityText}>{
                                    MainStore.language?.language == "tr" ? "TÜRKÇE" : "İNGİLİZCE"
                                }</Text>

                            </View>
                            <Text style={styles.titleTwo}>Select different language:</Text>

                            {/* NONE SELECTED CITY */}
                            <TouchableOpacity
                                onPress={() => {
                                    if (MainStore.language?.language == "tr")
                                        MainStore.setLanguage("en");
                                    else
                                        MainStore.setLanguage("tr");

                                    goPage(navigation, "Initial", {}, false)
                                }}
                                style={[styles.cityView, shadow]}
                            >
                                <Text style={[styles.cityText, { color: black_t3 }]}>{
                                    MainStore.language?.language == "tr" ? "İNGİLİZCE" : "TÜRKÇE"
                                }</Text>
                            </TouchableOpacity>
                        </View>
                    </>}
                />
            </SafeAreaView>
        </View>

    )
}
export default ChangeLanguage;

// -- STYLES -- // 
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi },
    safeAreaView: { flexGrow: 1 },
    form: { marginHorizontal: 20 },
    title: {
        fontSize: 16,
        color: black_t3
    },
    cityView: {
        width: '100%',
        backgroundColor: white,
        padding: 10,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 15
    },
    cityText: {
        fontSize: 16,
        color: green_t1,
        fontWeight: 'bold'
    },
    titleTwo: {
        fontSize: 16,
        color: black_t3,
        marginTop: 15
    },
});
// Create a separate component for the city selector
import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Input } from 'native-base';
import { black, black_t3, green_t1, white } from '../../../../constants/Color';

interface City {
  id: number;
  key: number;
  title: string;
}

interface CitySelectorProps {
  onCitySelected: (city: { id: number, title: string }) => void;
  getCities: () => Promise<City[]>;
  label?: string;
}

const CitySelector: React.FC<CitySelectorProps> = ({ onCitySelected, getCities, label = "Şehir" }) => {
  const [loading, setLoading] = useState(false);
  const [cities, setCities] = useState<City[]>([]);
  const [filteredCities, setFilteredCities] = useState<City[]>([]);
  const [cityText, setCityText] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCity, setSelectedCity] = useState<{ id: number, title: string } | null>(null);

  useEffect(() => {
    fetchCities();
  }, []);

  const fetchCities = async () => {
    setLoading(true);
    try {
      const citiesData = await getCities();
      setCities(citiesData);
      setLoading(false);
    } catch (error) {
      console.error("Failed to fetch cities:", error);
      setLoading(false);
    }
  };

  const handleCityInput = (text: string) => {
    setCityText(text);
    if (text.length > 0) {
      const filtered = cities.filter((cityItem: City) =>
        cityItem.title.toLocaleLowerCase('tr-TR').includes(text.toLocaleLowerCase('tr-TR'))
      );
      setFilteredCities(filtered);
      setShowSuggestions(true);
    } else {
      setFilteredCities([]);
      setShowSuggestions(false);
    }
  };

  const selectCity = (city: City) => {
    setSelectedCity({ id: city.id, title: city.title });
    setCityText(city.title);
    setShowSuggestions(false);
    onCitySelected({ id: city.id, title: city.title });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.inputContainer}>
        <Input
          value={cityText}
          onChangeText={handleCityInput}
          placeholder="Şehir ara..."
          placeholderTextColor="#999"
          bg={white}
          borderRadius={12}
          height={52}
          px={4}
          fontSize={16}
          color={black}
          borderWidth={1}
          borderColor={selectedCity ? green_t1 : "#E5E5E5"}
          _focus={{
            borderColor: green_t1,
            bg: white,
          }}
          onFocus={() => {
            if (cityText.length > 0) {
              setShowSuggestions(true);
            }
          }}
        />
        {loading && (
          <View style={styles.loadingIndicator}>
            <ActivityIndicator color={green_t1} />
          </View>
        )}
      </View>

      {showSuggestions && filteredCities.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <FlatList
            data={filteredCities}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => selectCity(item)}>
                <Text style={styles.suggestionItem}>{item.title}</Text>
              </TouchableOpacity>
            )}
            keyExtractor={(item) => item.id.toString()}
            style={styles.suggestionList}
            keyboardShouldPersistTaps="handled"
            maxToRenderPerBatch={10}
            windowSize={5}
          />
        </View>
      )}

      {showSuggestions && filteredCities.length === 0 && cityText.length > 0 && (
        <View style={styles.noResultsContainer}>
          <Text style={styles.noResultsText}>Sonuç bulunamadı</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: black_t3,
    marginBottom: 8,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  inputContainer: {
    position: 'relative',
  },
  loadingIndicator: {
    position: 'absolute',
    right: 16,
    top: 16,
  },
  suggestionsContainer: {
    marginTop: 4,
    backgroundColor: white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    maxHeight: 200,
    zIndex: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  suggestionList: {
    padding: 8,
  },
  suggestionItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: black,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  noResultsContainer: {
    marginTop: 4,
    backgroundColor: white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    alignItems: 'center',
  },
  noResultsText: {
    color: black_t3,
    fontSize: 14,
  }
});

export default CitySelector;
import React, { useState, useEffect } from "react";
import { StyleSheet, View, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import { kremrengi } from "../../../../constants/Color";
import { WebView } from "react-native-webview";

const DeleteAccount: React.FC = () => {
  const navigation = useNavigation<screens>();
  const [loading, setLoading] = useState(true);

  return (
    <View style={styles.main}>
      <SafeAreaView style={styles.safeAreaView}>
        <HeaderFour navigation={navigation} title={"Hesap Silme"} headerStatus={false} />

        {/* WebView */}
        <WebView
          source={{ uri: "https://api.hollystone.com.tr/api/delete-account" }}
          onLoad={() => setLoading(false)}
          onError={() => setLoading(false)}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={styles.spinnerView}>
              <ActivityIndicator size="large" color="#0000ff" />
            </View>
          )}
        />
      </SafeAreaView>
    </View>
  );
};

export default DeleteAccount;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: kremrengi,
  },
  safeAreaView: {
    flex: 1,
  },
  spinnerView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
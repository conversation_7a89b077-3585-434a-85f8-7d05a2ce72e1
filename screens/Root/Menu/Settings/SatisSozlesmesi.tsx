import React, { useState, useEffect } from "react";
import { StyleSheet, View, Text, ScrollView, Dimensions  } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import { black_t3, gray_t4, green_t1, green_t2, krem<PERSON><PERSON>, white } from "../../../../constants/Color";
import { get } from "../../../../networking/Server";
import Toast from "../../../../components/Toast";
import { Spinner } from "native-base";

import RenderHtml from 'react-native-render-html';
import PrivacySkeleton from "./Skeleton/PrivacySkeleton";


const SatisSozlesmesi: React.FC = () => {
  // -- NAVIGATION -- //
  const navigation = useNavigation<screens>();

  const [loading, setLoading] = useState(true);

  // -- Toast -- //
  const [statusToast, setStatusToast]: any = React.useState(null);
  const [typeToast, setTypeToast] = useState("");
  const [subtitleToast, setSubTitleToast] = useState("");

  const startToast = (message: string, type: string) => {
    setStatusToast(true);
    setSubTitleToast(message);
    setTypeToast(type);
    setTimeout(() => {
      setStatusToast(false);
    }, 1500);
  };

  // -- Fetch Agreements -- //
  const [agreement, setAgreement] = useState<any>(null);

  const getContracts = () => {
    try {
      get("/agreements").then((res: any) => {
        if (res.type === "success") {
          const foundAgreement = res.data.find((a: any) => a.id === 3);
          if (foundAgreement) {
            setAgreement(foundAgreement);
          }
        }
        setLoading(false);
      });
    } catch (e) {
      startToast("Bir şeyler ters gitti.", "error");
      setLoading(false);
    }
  };

  // -- Fetch Agreements on Mount -- //
  useEffect(() => {
    getContracts();
  }, []);

  return (
    <View style={styles.main}>
      {/* TOAST */}
      <View style={styles.toastView}>
        <Toast
          type={typeToast}
          subtitle={subtitleToast}
          status={statusToast}
          successColor={green_t1}
        />
      </View>

      <SafeAreaView style={styles.safeAreaView}>
        {/* HEADER */}
        <HeaderFour navigation={navigation} title={"SÖZLEŞME"} headerStatus={false} />

        {/* Agreement Details or Loading Indicator */}
        {loading ? (
          <View style={styles.spinnerView}>
          <PrivacySkeleton />
          </View>
        ) : (
          agreement ? (
            <ScrollView contentContainerStyle={styles.scrollViewContent}>
              <RenderHtml
                contentWidth={Dimensions.get('window').width}
                source={{ html: agreement.text }}
              />
            </ScrollView>
          ) : (
            <Text style={styles.noAgreementText}>Sözleşme bulunamadı.</Text>
          )
        )}
      </SafeAreaView>
    </View>
  );
};

export default SatisSozlesmesi;

// -- STYLES -- //
const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: kremrengi,
  },
  toastView: {
    position: "absolute",
    top: 0,
    alignSelf: "center",
    zIndex: 20,
  },
  safeAreaView: {
    flex: 1,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    textAlign: "center",
    color: gray_t4,
  },
  scrollViewContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 50,
  },
  agreementTitle: {
    marginTop: 20,
    fontSize: 18,
    textAlign: 'center',
    color: black_t3,
  },
  agreementContent: {
    marginTop: 10,
    fontSize: 16,
    textAlign: 'left',
    color: gray_t4,
  },
  noAgreementText: {
    marginTop: 20,
    fontSize: 16,
    textAlign: "center",
    color: gray_t4,
  },
  spinnerView: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    flex: 1
  },
});

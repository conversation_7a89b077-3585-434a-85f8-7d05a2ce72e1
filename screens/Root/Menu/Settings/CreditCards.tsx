import React from "react";
import { StyleSheet, View, ImageBackground, Image, Text, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import SettingsBackground from "../../../../components/SettingsBackground";
import Layout from "../../../../constants/Layout";
import { black_t3, brown_t2, gray_t12, gray_t4, green_t1, green_t4, kremrengi, red_t2, white, yellow_t1 } from "../../../../constants/Color";
import { shadow } from "../../../../constants/Shadow";
import { get, post } from "../../../../networking/Server";
import Toast from "../../../../components/Toast";
import { Spinner } from "native-base";
import { MainStore } from "../../../../stores/MainStore";
import CreditCardsSkeleton from "./Skeleton/CreditCardsSkeleton";

const CreditCards: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MODAL -- //
    const [modal, setModal] = React.useState(false);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- CARDS -- //
    const [loading, setLoading] = React.useState(true);
    const [cards, setCards] = React.useState([]);
    const [selectedCard, setSelectedCard]: any = React.useState("");

    // -- GET CARDS -- //
    const getCards = () => {
        setLoading(true);
        try {
            get("payment/card-list").then((res: any) => {
                if (res.type == "success") {
                    setCards(res.list);
                    setLoading(false);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    navigation.pop();
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            navigation.pop();
        }
    }

    const deleteCard = () => {
        setLoading(true);
        try {
            post("payment/delete-card", {
                ctoken: selectedCard.ctoken
            }).then((res: any) => {
                setLoading(false);
                if (res.type == "success") {
                    getCards();
                } else {
                    startToast(res.error, "error");
                }
            })
        } catch (e) {
            setLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    useFocusEffect(
        React.useCallback(() => {
            getCards();
        }, [])
    );

    return (
        <View
            style={styles.main}
        >
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            {
                modal ?
                    <View style={styles.modalView}>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseTopTouch}
                        />
                        <View style={styles.modalWhiteView}>
                            <Text style={styles.modalTitle}>Seçili olan kartınız:</Text>

                            {/* SELECTED CREDIT CARD */}
                            <TouchableOpacity style={[styles.touchCredit, { borderColor: gray_t12, marginTop: 20 }, shadow]}>
                                <Text style={styles.touchCardName}>{selectedCard.c_name}</Text>
                                <Text style={styles.touchCardNo}>**** **** **** {selectedCard.last_4}</Text>
                                <Image
                                    source={selectedCard?.schema == "MASTERCARD" ?
                                        require('../../../../assets/exp/masterCard.png')
                                        :
                                        selectedCard?.schema == "VISA" ?
                                            require('../../../../assets/exp/visa.png')
                                            :
                                            require('../../../../assets/exp/cardIcon.png')}
                                    style={styles.masterCardIcon}
                                />
                            </TouchableOpacity>
                            <Text style={styles.modalDesc}>SİLMEK İSTEDİĞİNİZE EMİN MİSİNİZ?</Text>
                            <View style={styles.modalButtonsView}>
                                {/* CANCEL */}
                                <TouchableOpacity
                                    onPress={() => {
                                        setModal(false);
                                    }}
                                    style={styles.modalButtonCancel}
                                >
                                    <Text style={styles.modalCancelText}>İptal</Text>
                                </TouchableOpacity>

                                {/* DELETE */}
                                <TouchableOpacity
                                    onPress={() => {
                                        setModal(false);
                                        deleteCard();
                                    }}
                                    style={styles.modalDeleteButton}
                                >
                                    <Text style={styles.modalDeleteText}>Sil</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseBottomTouch}
                        />
                    </View>
                    :
                    <></>
            }
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={MainStore.language.settings_my} headerStatus={false}                />
                {
                    loading ?
                        <CreditCardsSkeleton />
                        :
                        <SettingsBackground
                            onPress={() => {
                                setModal(true);
                            }}
                            buttonTitle={MainStore.language.delete}
                            title={MainStore.language?.credit_cards?.toUpperCase()}
                            children={
                                <>
                                    {/* CREDIT CARDS */}
                                    {
                                        cards?.length < 1 ?
                                            <Text style={styles.noContext}>{MainStore.language.no_credit_card}</Text>
                                            :
                                            cards?.map((item: any, index: React.Key) => {
                                                return (
                                                    <TouchableOpacity
                                                        onPress={() => {
                                                            setSelectedCard(item)
                                                        }}
                                                        key={index}
                                                        style={
                                                            [
                                                                styles.touchCredit,
                                                                {
                                                                    borderColor: item == selectedCard ? yellow_t1 : gray_t12,
                                                                    marginTop: index == 0 ? 0 : 20,
                                                                    borderWidth: item == selectedCard ? 1 : 0
                                                                }
                                                                ,
                                                                shadow
                                                            ]
                                                        }
                                                    >
                                                        <Text style={styles.touchCardName}>{item.c_name}</Text>
                                                        <Text style={styles.touchCardNo}>**** **** **** {item.last_4}</Text>
                                                        <Image
                                                            source={item?.schema == "MASTERCARD" ?
                                                                require('../../../../assets/exp/masterCard.png')
                                                                :
                                                                item?.schema == "VISA" ?
                                                                    require('../../../../assets/exp/visa.png')
                                                                    :
                                                                    require('../../../../assets/exp/cardIcon.png')}
                                                            style={styles.masterCardIcon}
                                                        />
                                                    </TouchableOpacity>
                                                )
                                            })
                                    }

                                    {/* ADD CARD */}
                                    <TouchableOpacity
                                        onPress={() => {
                                            navigation.navigate("AddCreditCard")
                                        }}
                                        style={styles.addCreditCardButton}
                                    >
                                        <Text style={styles.addCreditCardTitle}>{MainStore.language.add_card_two}</Text>
                                    </TouchableOpacity>

                                    {/* INFO */}
                                    <View style={[styles.infoView, shadow]}>
                                        <Text style={styles.infoText}>{MainStore.language.info_credit_card}</Text>
                                    </View>
                                </>
                            }
                        />
                }
            </SafeAreaView>
        </View>
    )
}
export default CreditCards;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi
 },
    toastView: {
        position: 'absolute',
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    modalView: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalWhiteView: {
        borderRadius: 10,
        backgroundColor: white,
        padding: 15,
        width: Layout.screen.width / 1.2
    },
    modalTitle: {
        fontSize: 14,
        color: black_t3
    },
    modalDesc: {
        color: red_t2,
        fontWeight: 'bold',
        textAlign: 'center',
        marginVertical: 20
    },
    modalButtonsView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 25
    },
    modalButtonCancel: {
        height: 30,
        borderRadius: 15,
        width: 107,
        backgroundColor: yellow_t1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalDeleteButton: {
        height: 30,
        width: 107,
        borderRadius: 15,
        backgroundColor: green_t4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCancelText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    modalDeleteText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    safeAreaView: { flexGrow: 1 },
    noContext: {
        textAlign: 'center',
        fontWeight: 'bold',
        color: black_t3,
        fontSize: 12
    },
    infoView: {
        backgroundColor: gray_t12,
        margin: 20,
        padding: 15,
        borderRadius: 10
    },
    infoText: { color: black_t3 },
    touchCredit: {
        borderWidth: 1,
        borderColor: brown_t2,
        marginHorizontal: 20,
        height: 70,
        justifyContent: 'center',
        borderRadius: 10,
        paddingHorizontal: 15,
        backgroundColor: white
    },
    touchCardName: {
        fontSize: 10,
        color: black_t3
    },
    touchCardNo: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 10,
        color: black_t3
    },
    masterCardIcon: {
        width: 28,
        height: 17,
        position: 'absolute',
        right: 15,
        bottom: 10
    },
    addCreditCardButton: {
        backgroundColor: yellow_t1,
        alignSelf: 'flex-end',
        marginRight: 20,
        marginTop: 20,
        paddingVertical: 8,
        paddingHorizontal: 20,
        borderRadius: 15,
    },
    addCreditCardTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
});
import React from "react";
import { Image, ImageBackground, StyleSheet, Text, View } from "react-native";
import Layout from "../../../../constants/Layout";
import { black, black_t3, gray_t10, gray_t11, gray_t12, gray_t14, gray_t4, green_t1, k<PERSON><PERSON><PERSON>, white } from "../../../../constants/Color";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import SettingsBackground from "../../../../components/SettingsBackground";
import { shadow } from "../../../../constants/Shadow";
import { Input } from "native-base";
import Toast from "../../../../components/Toast";
import { post } from "../../../../networking/Server";
import { MainStore } from "../../../../stores/MainStore";
import { PasswordLock } from "../../../../components/Svgs";


const ProfileUpdatePassword: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- PASSWORD -- //
    const [currentPassword, setCurrentPassword] = React.useState("");
    const [newPassword, setNewPassword] = React.useState("");
    const [repeatPassword, setRepeatPassword] = React.useState("");

    // -- UPDATE USER INFO -- //
    const handleUpdate = () => {
        const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,64}$/;

        if (
            passwordRegex.test(currentPassword) &&
            passwordRegex.test(newPassword) &&
            passwordRegex.test(repeatPassword)
        ) {
            if (newPassword === repeatPassword) {
                try {
                    post("/users/update-password", { currentPassword, newPassword }).then((res: any) => {
                        if (res.type === "success") {
                            startToast("Şifre güncellendi", "success");
                            setTimeout(() => {
                                navigation.pop();
                            }, 2000);
                        } else {
                            startToast(res.error, "error");
                        }
                    });
                } catch (e) {
                    startToast("Bir şeyler ters gitti", "error");
                }
            } else {
                startToast("yeni şifre ile şifre tekrarı eşleşmiyor.", "error");
            }
        } else {
            startToast("Şifre kurallara uymuyor.", "error");
        }
    }

    return (
        <View
            style={styles.main}
        >
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={"AYARLARIM"} headerStatus={false}                />
                <SettingsBackground
                    onPress={() => handleUpdate()}
                    buttonTitle={MainStore.language.update}
                    title="ŞİFRE DEĞİŞTİR"
                    children={<>
                        {/* OLD PASSWORD */}
                        <View style={[styles.inputView, shadow]}>
                            <Input
                                width={Layout.screen.width / 1.4}
                                ml={1}
                                borderWidth={0}
                                backgroundColor={'transparent'}
                                placeholder="Mevcut Şifre"
                                value={currentPassword}
                                onChange={(event) => setCurrentPassword(event.nativeEvent.text)}
                                color={gray_t4}
                                fontSize={16}
                                fontWeight={'bold'}
                            />
                            {/*
                            <Image
                                source={require('../../../../assets/menu/password.png')}
                                style={styles.inputIcon}
                                resizeMode="contain"
                            />
                            */}
                            <View style={{ marginRight: 10 }}>
                                <PasswordLock
                                    size={4}
                                    color={"#999999"}
                                />
                            </View>

                        </View>

                        {/* NEW PASSWORD */}
                        <View style={[styles.inputView, shadow, { marginTop: 15 }]}>
                            <Input
                                width={Layout.screen.width / 1.4}
                                ml={1}
                                borderWidth={0}
                                backgroundColor={'transparent'}
                                placeholder="Yeni Şifre"
                                value={newPassword}
                                onChange={(event) => setNewPassword(event.nativeEvent.text)}
                                color={gray_t4}
                                fontSize={16}
                                fontWeight={'bold'}
                            />
                            <View style={{ marginRight: 10 }}>
                                <PasswordLock
                                    size={4}
                                    color={"#999999"}
                                />
                            </View>
                        </View>

                        {/* AGAIN NEW PASSWORD */}
                        <View style={[styles.inputView, shadow, { marginTop: 15 }]}>
                            <Input
                                width={Layout.screen.width / 1.4}
                                ml={1}
                                borderWidth={0}
                                backgroundColor={'transparent'}
                                placeholder="Tekrar Yeni Şifre"
                                value={repeatPassword}
                                onChange={(event) => setRepeatPassword(event.nativeEvent.text)}
                                color={gray_t4}
                                fontSize={16}
                                fontWeight={'bold'}
                            />
                            <View style={{ marginRight: 10 }}>
                                <PasswordLock
                                    size={4}
                                    color={"#999999"}
                                />
                            </View>
                        </View>

                        {/* INFO */}
                        <View style={[styles.infoView, shadow]}>
                            <Text style={{
                                color: black_t3,
                            }}>Şifreniz <Text style={{ color: black }}>en az 8 karakter</Text> ve <Text style={{ color: black }}>en fazla 64</Text> karakter olmalı, <Text style={{ color: black }}>harf ve rakam</Text> içermelidir.</Text>
                        </View>
                    </>}
                />
            </SafeAreaView>
        </View>
    )
}
export default ProfileUpdatePassword;

// -- STYLES -- //
const styles = StyleSheet.create({
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
        alignSelf: 'center'
    },
    main: { flex: 1,
    backgroundColor: kremrengi
 },
    safeAreaView: { flexGrow: 1 },
    inputView: {
        height: 40,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: 'center',
        borderRadius: 10,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center'
    },
    inputIcon: {
        height: 20,
        width: 20,
        marginRight: 10
    },
    updatePasswordView: {
        height: 40,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: 'center',
        borderRadius: 10,
        flexDirection: 'row',
        alignItems: 'center'
    },
    updatePasswordIcon: {
        height: 25,
        width: 25,
        marginLeft: 10
    },
    updatePassword: {
        marginLeft: 10,
        fontSize: 16,
        fontWeight: 'bold',
        color: gray_t4
    },
    infoView: {
        backgroundColor: gray_t12,
        margin: 20,
        padding: 15,
        borderRadius: 10
    },
});
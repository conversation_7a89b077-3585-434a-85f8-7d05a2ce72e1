import React from "react";
import { StyleSheet, View, ScrollView, Text, FlatList, KeyboardAvoidingView, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import SettingsBackground from "../../../../components/SettingsBackground";
import { black, black_t3, green_t1, kremrengi, white } from "../../../../constants/Color";
import { Input, TextArea } from "native-base";
import { get, post } from "../../../../networking/Server";
import Toast from "../../../../components/Toast";
import AddAddressSkeleton from "./Skeleton/AddAddressSkeleton";
import CitySelector from "./CitySelector"; // Import the new component


// Define the correct type for city objects based on API response
interface City {
    id: number;
    key: number;
    title: string;
}

const AddAddress: React.FC = () => {
    const navigation = useNavigation<screens>();

    // Toast states
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => setStatusToast(false), 1500);
    }

    // Address states
    const [loading, setLoading] = React.useState(false);
    const [title, setTitle] = React.useState("");
    const [identity, setIdentity] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [phone, setPhone] = React.useState("90"); // Başlangıçta "90" ile başlat
    const [city, setCity] = React.useState("");
    const [district, setDistrict] = React.useState("");
    const [postCode, setPostCode] = React.useState("");
    const [fullAddress, setFullAddress] = React.useState("");
    const [cities, setCities] = React.useState<City[]>([]);
    const [selectedCity, setSelectedCity] = React.useState<{ id: number, title: string } | null>(null);
    const [filteredCities, setFilteredCities] = React.useState<City[]>([]);
    const [showSuggestions, setShowSuggestions] = React.useState(false);

    // API calls
    const addUserAddress = () => {
        if (!title) return startToast("Adres adı zorunludur.", "error");
        if (!identity || identity.length !== 11) return startToast("Kimlik numarası 11 hane olmalıdır.", "error");
        if (!email || !/\S+@\S+\.\S+/.test(email)) return startToast("Geçerli bir e-posta girin.", "error");
        if (!phone || phone.length < 10) return startToast("Telefon numarası geçersiz.", "error");
        if (!fullAddress) return startToast("Açık adres zorunludur.", "error");
        if (!selectedCity) return startToast("Lütfen bir şehir seçin.", "error");
        if (!postCode) return startToast("Posta kodu zorunludur.", "error"); // Posta kodu kontrolü eklendi

        setLoading(true);
        post("users/add-address", {
            title,
            cityId: selectedCity.id,
            district,
            postalCode: postCode,
            fullAddress,
            identityNumber: identity,
            email,
            phoneNumber: phone
        }).then((res: any) => {
            setLoading(false);
            if (res.type === "success") {
                startToast("Yeni adres kaydı başarılı!", "success");
                setTimeout(() => navigation.pop(), 1500);
            } else {
                startToast(res.error, "error");
            }
        }).catch(() => {
            setLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
        });
    };


    const getCities = (): Promise<City[]> => {
        return new Promise((resolve, reject) => {
            get("/cities").then((res: any) => {
                if (res.type === "success") {
                    resolve(res.cities);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    reject(new Error(res.error || "Failed to fetch cities"));
                }
            }).catch((error) => {
                startToast("Bir şeyler ters gitti.", "error");
                reject(error);
            });
        });
    };

    // Handle city input and filtering with Turkish locale
    const handleCityInput = (text: string) => {
        setCity(text);
        if (text.length > 0) {
            const filtered = cities.filter((cityItem: City) =>
                cityItem.title.toLocaleLowerCase('tr-TR').includes(text.toLocaleLowerCase('tr-TR'))
            );
            setFilteredCities(filtered);
            setShowSuggestions(true);
        } else {
            setFilteredCities(cities);
            setShowSuggestions(false);
        }
    };


    // Handle city selection
    const selectCity = (selected: City) => {
        setSelectedCity(selected); // Seçilen şehri ID ve title ile sakla
        setCity(selected.title); // Input alanında şehrin adını göster
        setShowSuggestions(false);
    };

    const handleCitySelected = (selectedCity: { id: number, title: string }) => {
        setSelectedCity(selectedCity);
    };


    // Handle phone number input to ensure it starts with "90"
    const handlePhoneInput = (text: string) => {
        // Eğer giriş "90" ile başlamıyorsa, "90" ekle
        if (!text.startsWith("90")) {
            text = "90" + text.replace(/^0+/, ''); // Başındaki sıfırları temizle
        }
        // Sadece rakamları kabul et ve maksimum 12 karakter (90 + 10 hane)
        const cleaned = text.replace(/[^0-9]/g, '').slice(0, 12);
        setPhone(cleaned);
    }

    React.useEffect(() => {
        getCities();
    }, []);

    return (
        <View style={styles.main}>
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>

            <SafeAreaView style={styles.safeAreaView}>
                <HeaderFour navigation={navigation} title={"AYARLARIM"} headerStatus={false} />
                {loading ? (
                    <AddAddressSkeleton />
                ) : (
                    <SettingsBackground
                        onPress={addUserAddress}
                        buttonTitle="Kaydet"
                        title="ADRES EKLE"
                        children={
                            <KeyboardAvoidingView
                                behavior={Platform.OS === "ios" ? "padding" : "height"}
                                style={{ flex: 1 }}
                                keyboardVerticalOffset={100}
                            >
                                <ScrollView
                                    showsVerticalScrollIndicator={false}
                                    contentContainerStyle={{ flexGrow: 1 }}
                                >
                                    <View style={styles.form}>
                                        {/* ADDRESS NAME */}
                                        <Text style={styles.label}>Adres Adı</Text>
                                    <Input
                                        onChangeText={setTitle}
                                        placeholder="Örn: Ev Adresim"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* IDENTITY */}
                                    <Text style={styles.label}>Kimlik Numarası</Text>
                                    <Input
                                        keyboardType="number-pad"
                                        onChangeText={setIdentity}
                                        placeholder="Kimlik No"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* EMAIL */}
                                    <Text style={styles.label}>Email</Text>
                                    <Input
                                        keyboardType="email-address"
                                        onChangeText={setEmail}
                                        placeholder="<EMAIL>"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* PHONE */}
                                    <Text style={styles.label}>Telefon No</Text>
                                    <Input
                                        keyboardType="phone-pad"
                                        value={phone}
                                        onChangeText={handlePhoneInput}
                                        placeholder="90 5XX XXX XX XX"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* CITY */}
                                    <CitySelector
                        onCitySelected={handleCitySelected}
                        getCities={getCities}
                    />

                                    {/* DISTRICT */}
                                    <Text style={styles.label}>İlçe</Text>
                                    <Input
                                        onChangeText={setDistrict}
                                        placeholder="İlçe adı"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* POST CODE */}
                                    <Text style={styles.label}>Posta Kodu</Text>
                                    <Input
                                        keyboardType="number-pad"
                                        onChangeText={setPostCode}
                                        placeholder="Örn: 34000"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={52}
                                        px={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        mb={4}
                                    />

                                    {/* FULL ADDRESS */}
                                    <Text style={styles.label}>Açık Adres</Text>
                                    <TextArea
                                        onChangeText={setFullAddress}
                                        placeholder="Detaylı adres bilgisi"
                                        placeholderTextColor="#999"
                                        bg={white}
                                        borderRadius={12}
                                        height={100}
                                        p={4}
                                        fontSize={16}
                                        color={black}
                                        borderWidth={1}
                                        borderColor="#E5E5E5"
                                        _focus={{
                                            borderColor: green_t1,
                                            bg: white,
                                        }}
                                        autoCompleteType={undefined}
                                        textAlignVertical="top"
                                    />
                                    </View>
                                </ScrollView>
                            </KeyboardAvoidingView>
                        }
                    />
                )}
            </SafeAreaView>
        </View>
    );
};

export default AddAddress;

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    toastView: {
        position: 'absolute',
        top: 0,
        alignSelf: 'center',
        zIndex: 20,
    },
    safeAreaView: {
        flexGrow: 1,
    },
    form: {
        marginHorizontal: 20,
        paddingVertical: 20,
    },
    label: {
        fontSize: 14,
        color: black_t3,
        marginBottom: 8,
        fontWeight: '600',
        textTransform: 'capitalize',
    },
    suggestionsContainer: {
        position: 'absolute',
        top: 60,
        left: 0,
        right: 0,
        backgroundColor: white,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        maxHeight: 150,
        zIndex: 10,
    },
    suggestionList: {
        padding: 8,
    },
    suggestionItem: {
        paddingVertical: 10,
        paddingHorizontal: 16,
        fontSize: 16,
        color: black,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
});
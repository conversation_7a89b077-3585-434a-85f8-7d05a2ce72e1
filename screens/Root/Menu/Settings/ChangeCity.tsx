import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { screens } from "../../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import SettingsBackground from "../../../../components/SettingsBackground";
import { shadow } from "../../../../constants/Shadow";
import { black_t3, gray_t1, green_t1, kremrengi, white, yellow_t2 } from "../../../../constants/Color";
import { MainStore } from "../../../../stores/MainStore";
import { get, post } from "../../../../networking/Server";
import ChangeCitySkeleton from "./Skeleton/ChangeCitySkeleton";
import Toast from "../../../../components/Toast";
import Layout from "../../../../constants/Layout";

const ChangeCity: React.FC = () => {
    const navigation = useNavigation<screens>();
    const [items, setItems] = React.useState<any[]>([]);
    const [loading, setLoading] = React.useState(true);
    const [selectedCity, setSelectedCity] = React.useState<any>({});

    // Toast state'leri
    const [statusToast, setStatusToast] = React.useState<boolean | null>(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    React.useEffect(() => {
        getCities();
    }, []);

    const getCities = () => {
        get("/vendors").then((res: any) => {
            if (res.type === "success") {
                setLoading(false);
                setItems(res.vendors);
            } else {
                navigation.pop();
            }
        });
    };

    const handleCityUpdate = async () => {
        if (!selectedCity || selectedCity.active === 2) {
            startToast("Seçtiğiniz şehir aktif değil!", "error");
            return;
        }
        try {
            post("/users/update-city", { cityId: selectedCity.id }).then((res: any) => {
                if (res.type === "success") {
                    MainStore.setCity(selectedCity);
                    startToast("Şehir başarıyla güncellendi", "success");
                    navigation.navigate("Initial");
                } else {
                    startToast(res.error, "error");
                }
                setLoading(false);
            });
        } catch (e) {
            startToast("Bir şeyler ters gitti", "error");
            setLoading(false);
        }
    };

    return (
        <View style={styles.main}>
            <SafeAreaView style={styles.safeAreaView}>
                {/* Toast Bileşeni */}
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>

                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={"AYARLARIM"}
                    headerStatus={false}
                />
                <SettingsBackground
                    onPress={handleCityUpdate}
                    buttonTitle={MainStore.language.update}
                    title="ŞEHİR DEĞİŞTİR"
                    children={
                        <>
                            {loading ? (
                                <ChangeCitySkeleton />
                            ) : (
                                <View style={styles.form}>
                                    {/* SELECTED CITY */}
                                    <Text style={styles.title}>Bulunduğunuz Şehir</Text>
                                    <View style={[styles.cityView, shadow]}>
                                        <Text style={styles.cityText}>{MainStore.city.name}</Text>
                                        <Image
                                            source={require('../../../../assets/menu/locationGreen.png')}
                                            style={styles.location}
                                        />
                                    </View>

                                    <Text style={styles.titleTwo}>Şehir değiştirmek için seçiniz:</Text>

                                    <ScrollView
                                        contentContainerStyle={styles.scrollContent}
                                        showsVerticalScrollIndicator={false}
                                    >
                                        {items.map((item: any, index: number) => {
                                            if (MainStore.city.name === item.name) {
                                                return null;
                                            }

                                            const isInactive = item.active === 2;

                                            return (
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (isInactive) {
                                                            startToast("Bu şehir aktif değil!", "error");
                                                            return;
                                                        }
                                                        setSelectedCity(item);
                                                    }}
                                                    key={index}
                                                    style={[
                                                        styles.cityView,
                                                        shadow,
                                                        { marginBottom: 10 }, // Consistent spacing
                                                        isInactive && styles.inactiveCity,
                                                    ]}
                                                >
                                                    <Text
                                                        style={[
                                                            styles.cityText,
                                                            {
                                                                color: isInactive
                                                                    ? gray_t1
                                                                    : selectedCity.name === item.name
                                                                    ? yellow_t2
                                                                    : black_t3,
                                                            },
                                                        ]}
                                                    >
                                                        {item.name}
                                                        {isInactive ? " (Aktif değil)" : ""}
                                                    </Text>
                                                    <Image
                                                        source={require('../../../../assets/menu/locationGreen.png')}
                                                        style={styles.location}
                                                    />
                                                </TouchableOpacity>
                                            );
                                        })}
                                    </ScrollView>
                                </View>
                            )}
                        </>
                    }
                />
            </SafeAreaView>
        </View>
    );
};

export default ChangeCity;

const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    safeAreaView: {
        flexGrow: 1,
    },
    form: {
        marginHorizontal: 20,
        flex: 1, // Ensure form takes available space
    },
    title: {
        fontSize: 16,
        color: black_t3,
    },
    cityView: {
        width: '100%',
        backgroundColor: white,
        padding: 15, // Increased padding for better touch area
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 15,
    },
    cityText: {
        fontSize: 16,
        color: green_t1,
        fontWeight: 'bold',
    },
    location: {
        position: 'absolute',
        right: 15,
        width: 12,
        height: 19,
    },
    titleTwo: {
        fontSize: 16,
        color: black_t3,
        marginTop: 15,
        marginBottom: 10, // Added spacing before ScrollView
    },
    toastView: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
    },
    inactiveCity: {
        opacity: 0.7,
    },
    scrollContent: {
        paddingBottom: Layout.screen.height * 0.15, // Dynamic padding to ensure scrolling fits screen
        paddingHorizontal: 5, // Consistent horizontal padding
    },
});
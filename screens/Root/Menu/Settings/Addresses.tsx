import React from "react";
import { Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import SettingsBackground from "../../../../components/SettingsBackground";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import Toast from "../../../../components/Toast";
import { black_t3, gray_t1, gray_t12, gray_t4, green_t1, green_t2, green_t4, kremrengi, red_t2, white, yellow_t1 } from "../../../../constants/Color";
import { shadow } from "../../../../constants/Shadow";
import { <PERSON><PERSON>, Spinner } from "native-base";
import Layout from "../../../../constants/Layout";
import { get, post } from "../../../../networking/Server";
import AddressesSkeleton from "./Skeleton/AddressesSkeleton";

const Addresses: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MODAL -- //
    const [modal, setModal] = React.useState(false);

    // -- USER ADDRESSES -- //
    const [userAddresses, setUserAddresses] = React.useState([]);
    const [selectedAddress, setSelectedAddress]: any = React.useState({});
    const [loading, setLoading] = React.useState(true);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- GET ADDRESS -- //
    const getAddress = () => {
        setLoading(true);
        try {
            get("/users/addresses").then((res: any) => {
                if (res.type == "success") {
                    setUserAddresses(res.userAddresses);
                } else {
                    setTimeout(() => {
                        navigation.pop();
                    }, 1500);
                    startToast("Bir şeyler ters gitti.", "error");
                }
                setLoading(false);
            })
        } catch (e) {
            setTimeout(() => {
                navigation.pop();
            }, 1500);
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- DELETE ADDRESS -- //
    const deleteAddress = () => {
        setLoading(true);
        try {
            post("/users/delete-address", {
                addressId: selectedAddress.id
            }).then((res: any) => {
                if (res.type == "success") {
                    getAddress();
                    startToast("Adres kayıtlı adreslerinizden kaldırıldı.", "success");
                } else {
                    setLoading(false);
                    startToast("Bir şeyler ters gitti.", "error");
                }
            })
        } catch (e) {
            setLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    useFocusEffect(
        React.useCallback(() => {
            getAddress();
        }, [])
    );

    return (
        <View
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t2}
                />
            </View>
            {
                modal ?
                    <View style={styles.modalView}>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseTopTouch}
                        />
                        <View style={styles.modalWhiteView}>
                            <Text style={styles.modalTitle}>Seçili olan adresiniz:</Text>

                            {/* SELECTED CREDIT CARD */}
                            <View style={[styles.modalItemView, shadow]}>
                                <Text style={styles.formName}>{selectedAddress.title}</Text>
                                <Image
                                    source={require('../../../../assets/menu/home.png')}
                                    style={styles.formIcon}
                                />
                            </View>
                            <Text style={styles.modalDesc}>SİLMEK İSTEDİĞİNİZE EMİN MİSİNİZ?</Text>
                            <View style={styles.modalButtonsView}>
                                {/* CANCEL */}
                                <TouchableOpacity
                                    onPress={() => {
                                        setModal(false);
                                    }}
                                    style={styles.modalButtonCancel}
                                >
                                    <Text style={styles.modalCancelText}>İptal</Text>
                                </TouchableOpacity>

                                {/* DELETE */}
                                <TouchableOpacity
                                    onPress={() => {
                                        deleteAddress();
                                        setModal(false);
                                    }}
                                    style={styles.modalDeleteButton}
                                >
                                    <Text style={styles.modalDeleteText}>Sil</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseBottomTouch}
                        />
                    </View>
                    :
                    <></>
            }
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={"AYARLARIM"} headerStatus={false}                />
                {
                    loading ? <AddressesSkeleton />
                        : (
                            <SettingsBackground
                                onPress={() => {
                                    if (selectedAddress.id)
                                        setModal(true);
                                    else
                                        startToast("Adres seçilmedi", "error");
                                }}
                                buttonTitle="Sil"
                                title="KAYITLI ADRESLERİM"
                                children={<>
                                    <View style={styles.form}>
                                        {
                                            userAddresses.length < 1 ?
                                                <Text style={styles.noContext}>Kayıtlı adresiniz bulunmamaktadır.</Text>
                                                :
                                                userAddresses.map((item: any, index: number) => {
                                                    return (
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                setSelectedAddress(item);
                                                            }}
                                                            key={index}
                                                            style={[styles.item, shadow, { borderWidth: selectedAddress == item ? 1 : 0, borderColor: selectedAddress == item ? green_t1 : gray_t1, marginTop: index != 0 ? 15 : 0 }]}
                                                        >
                                                            <Text style={styles.formName}>{item.title}</Text>
                                                            <Image
                                                                source={require('../../../../assets/menu/home.png')}
                                                                style={styles.formIcon}
                                                            />
                                                        </TouchableOpacity>
                                                    )
                                                })
                                        }

                                        {/* ADD ADDRESS BUTTON */}
                                        <Button
                                            onPress={() => {
                                                navigation.navigate("AddAddress");
                                            }}
                                            style={styles.button}
                                        >
                                            <Text style={styles.buttonText}>Adres Ekle</Text>
                                        </Button>

                                        {/* INFO */}
                                        <View style={[styles.infoView, shadow]}>
                                            <Text style={styles.infoText}>Sil butonuna bastığınızda seçili olan adres silinecektir.</Text>
                                        </View>
                                    </View>
                                </>}
                            />
                        )
                }
            </SafeAreaView>
        </View>
    )
}
export default Addresses;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi
 },
    modalView: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute'
    },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalWhiteView: {
        borderRadius: 10,
        backgroundColor: white,
        padding: 15,
        width: Layout.screen.width / 1.2
    },
    modalTitle: {
        fontSize: 14,
        color: black_t3
    },
    modalItemView: {
        width: 200,
        alignSelf: 'center',
        marginTop: 15,
        backgroundColor: white,
        height: 46,
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row',
        paddingHorizontal: 15,
        justifyContent: 'space-between',
    },
    modalDesc: {
        color: red_t2,
        fontWeight: 'bold',
        textAlign: 'center',
        marginVertical: 20
    },
    modalButtonsView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 25
    },
    modalButtonCancel: {
        height: 30,
        borderRadius: 15,
        width: 107,
        backgroundColor: black_t3,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalDeleteButton: {
        height: 30,
        width: 107,
        borderRadius: 15,
        backgroundColor: green_t4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCancelText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    modalDeleteText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    safeAreaView: { flexGrow: 1 },
    noContext: {
        textAlign: 'center',
        fontWeight: 'bold',
        color: black_t3,
        fontSize: 12
    },
    form: {
        marginHorizontal: 20,
        height: Layout.screen.height / 1.5
    },
    item: {
        width: '100%',
        backgroundColor: white,
        height: 46,
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row',
        paddingHorizontal: 15,
        justifyContent: 'space-between'
    },
    formName: {
        color: black_t3,
        fontSize: 14
    },
    formIcon: {
        height: 24,
        width: 24
    },
    button: {
        marginTop: 20,
        backgroundColor: gray_t4,
        alignSelf: 'flex-end',
        borderRadius: 20
    },
    buttonText: {
        color: white,
        fontWeight: 'bold'
    },
    infoView: {
        backgroundColor: gray_t12,
        position: 'absolute',
        width: '100%',
        alignSelf: 'center',
        bottom: 10,
        margin: 20,
        padding: 15,
        borderRadius: 10
    },
    infoText: { color: black_t3 },
});
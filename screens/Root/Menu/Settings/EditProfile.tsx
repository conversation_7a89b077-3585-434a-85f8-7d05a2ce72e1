import React from "react";
import { StyleSheet, View, ImageBackground, ScrollView, Image, Text, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import SettingsBackground from "../../../../components/SettingsBackground";
import Layout from "../../../../constants/Layout";
import { black, gray_t4, green_t1, kremrengi, white } from "../../../../constants/Color";
import { shadow } from "../../../../constants/Shadow";
import { Input, Spinner } from "native-base";
import Toast from "../../../../components/Toast";
import { get, post } from "../../../../networking/Server";
import { MainStore } from "../../../../stores/MainStore";
import { PasswordLock } from "../../../../components/Svgs";
import EditProfileSkeleton from "./Skeleton/EditProfileSkeleton";

const EditProfile: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);

    // -- USER INFO -- //
    const [firstName, setFirstName] = React.useState("");
    const [lastName, setLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");

    // -- GET USER INFO -- //
    const getUserData = () => {
        try {
            get("/users/profile").then((res: any) => {
                if (res.type == "success") {
                    setFirstName(res.user.firstName);
                    setLastName(res.user.lastName);
                    setEmail(res.user.email);
                    setPhoneNumber(res.user.phoneNumber);
                } else {
                    startToast(res.error, "error")
                }
                setLoading(false);
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti", "error")
        }
    }

    // -- UPDATE USER INFO -- //
    const handleUpdate = () => {
        if (
            firstName.length > 0 &&
            lastName.length > 0 &&
            email.length > 0
        ) {
            setLoading(true);
            try {
                post("/users/profile", { firstName, lastName, email }).then((res: any) => {
                    if (res.type == "success") {
                        startToast("Bilgiler güncellendi", "success")
                    } else {
                        startToast(res.error, "error")
                    }
                    setLoading(false);
                })
            } catch (e) {
                startToast("Bir şeyler ters gitti", "error")
                setLoading(false);
            }
        } else {
            startToast("Tüm alanaları doldurun.", "error")
            setLoading(false);
        }
    }

    React.useEffect(() => {
        getUserData();
    }, []);

    return (
        <View
            style={styles.main}
        >
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderFour
                    navigation={navigation}
                    title={MainStore.language.settings_my} headerStatus={false}                />
                {
                    loading ?  <EditProfileSkeleton /> : (
                        <SettingsBackground
                            onPress={() => handleUpdate()}
                            buttonTitle={MainStore.language.update}
                            title={MainStore.language?.edit_profile?.toUpperCase()}
                            children={<>
                                {/* FIRSTNAME */}
                                <View style={[styles.inputView, shadow]}>
                                    <Input
                                        width={Layout.screen.width / 1.4}
                                        ml={1}
                                        borderWidth={0}
                                        backgroundColor={'transparent'}
                                        placeholder={MainStore.language.name}
                                        value={firstName}
                                        onChange={(event) => setFirstName(event.nativeEvent.text)}
                                        color={gray_t4}
                                        fontSize={16}
                                        fontWeight={'bold'}
                                    />
                                    <Image
                                        source={require('../../../../assets/menu/edit.png')}
                                        style={styles.inputIcon}
                                        resizeMode="contain"
                                    />
                                </View>

                                {/* LASTNAME */}
                                <View style={[styles.inputView, shadow, { marginTop: 15 }]}>
                                    <Input
                                        width={Layout.screen.width / 1.4}
                                        ml={1}
                                        borderWidth={0}
                                        backgroundColor={'transparent'}
                                        placeholder={MainStore.language.surname}
                                        value={lastName}
                                        onChange={(event) => setLastName(event.nativeEvent.text)}
                                        color={gray_t4}
                                        fontSize={16}
                                        fontWeight={'bold'}
                                    />
                                    <Image
                                        source={require('../../../../assets/menu/edit.png')}
                                        style={styles.inputIcon}
                                        resizeMode="contain"
                                    />
                                </View>

                                {/* EMAIL */}
                                <View style={[styles.inputView, shadow, { marginTop: 15 }]}>
                                    <Input
                                        width={Layout.screen.width / 1.4}
                                        ml={1}
                                        borderWidth={0}
                                        backgroundColor={'transparent'}
                                        placeholder="Email"
                                        value={email}
                                        onChange={(event) => setEmail(event.nativeEvent.text)}
                                        color={gray_t4}
                                        fontSize={16}
                                        fontWeight={'bold'}
                                    />
                                    <Image
                                        source={require('../../../../assets/menu/edit.png')}
                                        style={styles.inputIcon}
                                        resizeMode="contain"
                                    />
                                </View>

                                {/* PHONE */}
                                <View style={[styles.inputView, shadow, { marginTop: 15 }]}>
                                    <Input
                                        width={Layout.screen.width / 1.4}
                                        ml={1}
                                        isDisabled={true}
                                        borderWidth={0}
                                        backgroundColor={'transparent'}
                                        placeholder="+90 999 999 99 99"
                                        value={phoneNumber}
                                        color={gray_t4}
                                        fontSize={16}
                                        fontWeight={'bold'}
                                    />
                                </View>

                                {/* UPDATE PASSWORD */}
                                <TouchableOpacity
                                    onPress={() => {
                                        navigation.navigate("ProfileUpdatePassword");
                                    }}
                                    style={[styles.updatePasswordView, shadow, { marginTop: 15 }]}
                                >
                                    {/*
                                    <Image
                                        source={require('../../../../assets/menu/password.png')}
                                        style={styles.updatePasswordIcon}
                                        resizeMode="contain"
                                    />
                                    */}
                                    <View style={{ marginLeft: 10 }}>
                                        <PasswordLock
                                            size={5}
                                        />
                                    </View>
                                    <Text style={styles.updatePassword}>{MainStore.language.password}</Text>
                                </TouchableOpacity>
                            </>}
                        />
                    )
                }

            </SafeAreaView>
        </View>
    )
}
export default EditProfile;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi
 },
    toastView: {
        position: 'absolute',
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    safeAreaView: { flexGrow: 1 },
    inputView: {
        height: 40,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: 'center',
        borderRadius: 10,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center'
    },
    inputIcon: {
        height: 20,
        width: 20,
        marginRight: 10
    },
    updatePasswordView: {
        height: 40,
        width: Layout.screen.width / 1.25,
        backgroundColor: white,
        alignSelf: 'center',
        borderRadius: 10,
        flexDirection: 'row',
        alignItems: 'center'
    },
    updatePasswordIcon: {
        height: 25,
        width: 25,
        marginLeft: 10
    },
    updatePassword: {
        marginLeft: 10,
        fontSize: 16,
        fontWeight: 'bold',
        color: gray_t4
    }
});
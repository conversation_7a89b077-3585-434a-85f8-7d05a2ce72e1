import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import HeaderFour from "../../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../../navigation";
import { black, black_t3, gray_t4, gray_t7, green_t1, green_t4, white } from "../../../../constants/Color";
import Layout from "../../../../constants/Layout";
import { SafeAreaView } from "react-native-safe-area-context";
import { shadow } from "../../../../constants/Shadow";
import Switch from "../../../../components/Switch";
import { But<PERSON>, Spinner } from "native-base";
import Toast from "../../../../components/Toast";
import { goPage } from "../../../../functions/goPage";
import { launchImageLibrary } from 'react-native-image-picker'
import { MainStore } from "../../../../stores/MainStore";
import { get, getImageURL, post } from "../../../../networking/Server";
import { Calendar, Contract, CreditCard, Language, Location, Lock, LogOut, MailIcon, PhoneIcon, Profile, Refund } from "../../../../components/Svgs";

import SkeletonPlaceholder from "react-native-skeleton-placeholder";

const LoadingSkeleton = () => {
    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            {/* Profile Card Skeleton */}
            <SkeletonPlaceholder 
                backgroundColor="#E8E8E8" 
                highlightColor="#FFFFFF"
                speed={1200}
            >
                <SkeletonPlaceholder.Item alignItems="center">
                    {/* Profile Section */}
                    <SkeletonPlaceholder.Item
                        width={Layout.screen.width / 1.3}
                        height={111}
                        borderRadius={8}
                        marginTop={70}
                        flexDirection="row"
                        backgroundColor={white}
                    >
                        {/* Profile Image */}
                        <SkeletonPlaceholder.Item
                            width={111}
                            height={111}
                            borderRadius={55.5}
                            position="absolute"
                            left={-20}
                        />
                        
                        {/* Profile Info */}
                        <SkeletonPlaceholder.Item 
                            marginLeft={80} 
                            marginTop={15}
                            width={Layout.screen.width / 2}
                        >
                            <SkeletonPlaceholder.Item
                                width={120}
                                height={14}
                                borderRadius={4}
                                left={30}

                            />
                            <SkeletonPlaceholder.Item
                                width={150}
                                height={10}
                                borderRadius={4}
                                marginTop={10}
                                left={30}

                            />
                            <SkeletonPlaceholder.Item
                                width={100}
                                height={10}
                                borderRadius={4}
                                marginTop={5}
                                left={30}

                            />
                            <SkeletonPlaceholder.Item
                                width={130}
                                height={12}
                                borderRadius={4}
                                marginTop={5}
                                left={30}

                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>

            {/* Settings Sections Skeleton */}
            <View style={styles.settingsContainer}>
                {/* Profile Section */}
                <SkeletonPlaceholder 
                    backgroundColor="#E8E8E8" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item>
                        {/* Section Title */}
                        <SkeletonPlaceholder.Item
                            width={100}
                            height={16}
                            borderRadius={4}
                            marginBottom={10}
                        />
                        
                        {/* Menu Items */}
                        {[1, 2, 3, 4, 5].map((_, index) => (
                            <SkeletonPlaceholder.Item
                                key={index}
                                width="100%"
                                height={50}
                                borderRadius={10}
                                marginVertical={5}
                            />
                        ))}
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>

                {/* Contracts Section */}
                <SkeletonPlaceholder 
                    backgroundColor="#E8E8E8" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item marginTop={20}>
                        <SkeletonPlaceholder.Item
                            width={100}
                            height={16}
                            borderRadius={4}
                            marginBottom={10}
                        />
                        {[1, 2, 3].map((_, index) => (
                            <SkeletonPlaceholder.Item
                                key={index}
                                width="100%"
                                height={50}
                                borderRadius={10}
                                marginVertical={5}
                            />
                        ))}
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>

                {/* Preferences Section */}
                <SkeletonPlaceholder 
                    backgroundColor="#E8E8E8" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item marginTop={20} marginBottom={30}>
                        <SkeletonPlaceholder.Item
                            width={100}
                            height={16}
                            borderRadius={4}
                            marginBottom={10}
                        />
                        {[1, 2, 3, 4].map((_, index) => (
                            <SkeletonPlaceholder.Item
                                key={index}
                                width="100%"
                                height={50}
                                borderRadius={10}
                                marginVertical={5}
                            />
                        ))}
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
            </View>
        </ScrollView>
    );
};

const Settings: React.FC = () => {

    const profileList = [
        {
            id: 1,
            title: MainStore.language.edit_profile,
            child: <View style={{
                left: 15,
                position: 'absolute',
            }}>
                <Profile size={5} color={black} />
            </View>,
            nav: "EditProfile"
        },
        {
            id: 2,
            title: MainStore.language.credit_cards,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <CreditCard size={75} color={black} />
            </View>,
            nav: "CreditCards",
        },
        {
            id: 3,
            title: MainStore.language.saved_addressess,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Location size={75} color={black} />
            </View>,
            nav: "Addresses"
        },
        {
            id: 4,
            title: MainStore.language.orders,
            child: <View style={{
                left: 10,
                position: 'absolute'
            }}>
                <Calendar size={30} color={"#efeaea"} />
            </View>,
            nav: "MyOrders"
        },
        {
            id: 5,
            title: MainStore.language.change_city_two,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Location size={75} color={black} />
            </View>,
            nav: "ChangeCity"
        },
        {
            id: 6,
            title: 'Hesap Silme',
            child: <View style={{
                left: 15,
                position: 'absolute',
            }}>
                <Profile size={5} color={black} />
            </View>,
            nav: "DeleteAccount"
        }
    ];

    const contractList = [
        {
            id: 1,
            title: MainStore.language.distance_selling_contract,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Contract size={75} color={black} />
            </View>,
            nav: "SatisSozlesmesi"
        },
        {
            id: 2,
            title: MainStore.language.cancellation_and_refund_conditions,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Refund size={75} color={black} />
            </View>,
            nav: "IptalveIade"
        },
        {
            id: 3,
            title: MainStore.language.privacy_and_security_policy,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Lock size={75} color={black} />
            </View>,
             nav: "GizlilikveGuvenlik"
        }
    ];

    const preferences = [
        {
            id: 1,
            title: MainStore.language.change_language,
            child: <View style={{
                left: -12,
                position: 'absolute',
            }}>
                <Language size={75} color={black} />
            </View>,
            nav: "ChangeLanguage"
        },
        {
            id: 2,
            title: MainStore.language.log_out,
            child: <View style={{
                left: -5,
                position: 'absolute'
            }}>
                <LogOut size={65} color={gray_t4} />
            </View>,
            nav: "Exit"
        },
    ]



    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- EXIT MODAL -- //
    const [exitModal, setExitModal] = React.useState(false);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);

    // -- USER INFO -- //
    const [firstName, setFirstName] = React.useState("");
    const [lastName, setLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [referralCode, setReferralCode] = React.useState("");

    

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const [imageUri, setImageUri] = React.useState("");
    const [imageType, setImageType] = React.useState("");
    const [imageName, setImageName] = React.useState("");
    const [image, setImage] = React.useState("");
    

    const setImages = () => {
        launchImageLibrary({
            mediaType: "photo",
            maxHeight: 600,
            maxWidth: 600,
            quality: 1,
        }, (callback: any) => {
            if (!callback.didCancel && callback.assets.length > 0) {
                const uri = callback.assets[0].uri;
                const type = callback.assets[0].type;
                const fileName = callback.assets[0].fileName;
                
                setImageUri(uri);
                setImageType(type);
                setImageName(fileName);    
                // Dosya özelliklerini handleUpdate fonksiyonuna iletip post isteği yap
                handleUpdate(uri, type, fileName); 
            } else {
                startToast("Lütfen fotoğraf seçiniz.", "error");
            }
        });
    }

    const handleUpdate = (uri: string, type: string, fileName: any) => {
        setLoading(true);
        startToast("Fotoğraf yükleniyor. Lütfen bekleyin!", "success");
    
        const formData = new FormData();
        formData.append('image', {
            uri: uri,
            type: type,
            name: fileName
        });
    
        post("users/profile-pic", formData).then((resp: any) => {
            if (resp.type == "success") {
                startToast("Fotoğraf başarılı bir şekilde yüklendi!", "success");
                setTimeout(() => {
                    navigation.pop();
                }, 2000)
            } else {
                startToast(resp.error, "error");
            }
            setLoading(false);
        }).catch((error: any) => {
            startToast("Bir şeyler ters gitti", "error");
            setLoading(false);
        });
    }
    

    // -- GET USER INFO -- //
    const getUserData = () => {
        try {
            get("/users/profile").then((res: any) => {
                if (res.type == "success") {
                    setFirstName(res.user.firstName);
                    setLastName(res.user.lastName);
                    setEmail(res.user.email);
                    setPhoneNumber(res.user.phoneNumber);
                    setReferralCode(res.user.referralCode);
                    setImage(res.user.image);
                } else {
                    startToast(res.error, "error")
                }
                setLoading(false);
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti", "error")
        }
    }

    React.useEffect(() => {
        getUserData();
    }, []);

    // -- SETTING ITEM A -- //
    const SettingItemOne: React.FC<{ child: any, title: string, onPress: any }> = ({
        child,
        title,
        onPress
    }) => {
        return (
            <TouchableOpacity
                onPress={onPress}
                style={[styles.settingItemOne, shadow]}
            >
                {child}
                <Text style={styles.settingItemOneTitle}>{title}</Text>
            </TouchableOpacity>
        )
    }

    const SettingItemTwo: React.FC<{ title: string }> = ({
        title
    }) => {
        return (
            <View style={[styles.settingItemTwoView, shadow]}>
                <Text style={styles.settingItemTwoTitle}>{title}</Text>
                <View style={styles.settingItemTitleRightView}>
                    <Switch />
                </View>
            </View>
        )
    }

    return (
        <View
            style={styles.main}
        >
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            {
                exitModal ?
                    <View style={styles.exitModal}>
                        <View style={styles.exitWhiteArea}>

                            {/* TITLE */}
                            <Text style={styles.exitText}>ÇIKIŞ YAPMAK İSTEDİĞİNDEN EMİN MİSİN?</Text>

                            {/* BUTTONS */}
                            <View style={styles.exitButtonsView}>
                                <Button
                                    onPress={() => {
                                        setExitModal(false);
                                    }}
                                    style={styles.exitButtonsLeft}
                                >
                                    <Text style={styles.exitButtonsLeftText}>Uygulamada Kal</Text>
                                </Button>
                                <Button
                                    onPress={() => {
                                        MainStore.setToken("");
                                        goPage(navigation, "Initial", {}, false);
                                    }}
                                    style={styles.exitButtonsRight}
                                >
                                    <Text style={styles.exitButtonsRightText}>Çıkış Yap</Text>
                                </Button>
                            </View>
                        </View>
                    </View>
                    :
                    <></>
            }
            {/* HEADER */}
            <HeaderFour
                headerStatus={header}
                navigation={navigation}
                title={MainStore.language.settings_my}
            />

            <SafeAreaView style={styles.safeAreaView}>
                <ScrollView onScroll={handleScroll} showsVerticalScrollIndicator={false}>
                    {
                        loading ? <LoadingSkeleton />
                            : (
                                <>
                                    {/* PROFILE CARD */}
                                    <View style={[styles.profileHeaderView, shadow]}>
                                        <TouchableOpacity onPress={setImages}  >
                                            <Image
                                                source={{ uri: getImageURL(image) }}
                                                style={styles.profileImg}
                                            />
                                            <View style={styles.profileChangeImgView}>
                                                <Image
                                                    source={require('../../../../assets/menu/settingsCamera.png')}
                                                    style={styles.profileChangeImgIcon}
                                                    resizeMode="contain"
                                                />
                                            </View>
                                        </TouchableOpacity>
                                        <View style={styles.profileDescView}>
                                            {/* NAME SURNAME */}
                                            <Text style={styles.profileNameSurname}>{`${firstName} ${lastName}`}</Text>

                                            {/* MAIL */}
                                            <View style={styles.profileMailView}>
                                                {/* <Image
                                                    source={require('../../../../assets/menu/mail.png')}
                                                    style={styles.profileMailIcon}
                                                    resizeMode="contain"
                                                /> */}
                                                <MailIcon size={3} />
                                                <Text style={styles.profileMail}>
                                                    {email}
                                                </Text>
                                            </View>

                                            {/* PHONE */}
                                            <View style={styles.phoneView}>
                                                {/* <Image
                                                    source={require('../../../../assets/menu/phone.png')}
                                                    style={styles.phoneIcon}
                                                    resizeMode="contain"
                                                /> */}
                                                <PhoneIcon size={3} />
                                                <Text style={styles.phone}>
                                                    {phoneNumber}
                                                </Text>
                                            </View>

                                            {/* REFERANS ID AREA */}
                                            <View style={styles.referansView}>
                                                <Text style={styles.referansIdTitle}>referans id: </Text>
                                                <Text style={styles.referansId}>{referralCode}</Text>
                                                <TouchableOpacity style={styles.copyTouch}>
                                                    <Image
                                                        source={require('../../../../assets/menu/copy.png')}
                                                        style={styles.copyIcon}
                                                    />
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    </View>

                                    {/* PROFILE SETTINGS */}
                                    <View style={styles.settingItemOneMapView}>
                                        <Text style={styles.settingItemOneMapTitle}>{MainStore.language.profile}</Text>
                                        {
                                            profileList.map((item, index) => {
                                                return (
                                                    <SettingItemOne
                                                        onPress={() => {
                                                            //@ts-ignore
                                                            navigation.navigate(item.nav)
                                                        }}
                                                        key={index}
                                                        child={item.child}
                                                        title={item.title}
                                                    />
                                                )
                                            })
                                        }
                                    </View>

                                    {/* CONTRACTS */}
                                    <View style={styles.settingItemOneMapView}>
                                        <Text style={styles.settingItemOneMapTitle}>{MainStore.language.contracts}</Text>
                                        {
                                            contractList.map((item, index) => {
                                                return (
                                                    <SettingItemOne
                                                        onPress={() => {
                                                            //@ts-ignore
                                                            navigation.navigate(item.nav)
                                                        }}
                                                        key={index}
                                                        child={item.child}
                                                        title={item.title}
                                                    />
                                                )
                                            })
                                        }
                                    </View>

                                    {/* PREFERENCES */}
                                    <View style={[styles.settingItemOneMapView, { marginBottom: 30 }]}>
                                        <Text style={styles.settingItemOneMapTitle}>{MainStore.language.preferences}</Text>

                                        {/* NOTIFICATION ACCESS */}
                                        <SettingItemTwo
                                            title={MainStore.language.app_notify}
                                        />

                                        {/* PHONE NUMBER MESSAGE */}
                                        <SettingItemTwo
                                            title={MainStore.language.sms_notify}
                                        />
                                        {/* LANGUAGE & LOGOUT */}
                                        {
                                            preferences.map((item, index) => {
                                                return (
                                                    <SettingItemOne
                                                        key={index}
                                                        onPress={() => {
                                                            if (item.nav == "Exit") {
                                                                setExitModal(true);
                                                                return
                                                            }
                                                            //@ts-ignore
                                                            navigation.navigate(item.nav)
                                                        }}
                                                        child={item.child}
                                                        title={item.title}
                                                    />
                                                )
                                            })
                                        }
                                    </View>
                                </>
                            )
                    }
                </ScrollView>
            </SafeAreaView>
        </View >
    )
}
export default Settings;

// -- STYLES -- //
const styles = StyleSheet.create({
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
        alignSelf: 'center'
    },
    settingItemOne: {
        width: '100%',
        height: 50,
        borderRadius: 10,
        backgroundColor: gray_t7,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        paddingHorizontal: 15,
        marginVertical: 5
    },
    settingItemOneTitle: {
        fontSize: 14,
        marginLeft: 35,
        color: black
    },
    settingItemTwoView: {
        width: '100%',
        height: 50,
        borderRadius: 10,
        backgroundColor: gray_t7,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        alignSelf: 'center',
        paddingHorizontal: 15,
        marginVertical: 5
    },
    settingItemTwoTitle: {
        fontSize: 14,
        marginLeft: 5,
        color: black
    },
    settingItemTitleRightView: { marginRight: 5 },
    main: { flex: 1 },
    exitModal: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    exitWhiteArea: {
        alignItems: 'center',
        backgroundColor: white,
        borderRadius: 15,
        width: Layout.screen.width / 1.15,
        paddingVertical: 20,
    },
    exitText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: black_t3,
        marginHorizontal: 60,
        textAlign: 'center'
    },
    exitButtonsView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20
    },
    exitButtonsLeft: {
        backgroundColor: green_t4,
        borderRadius: 20,
        width: 120,
        marginRight: 10
    },
    exitButtonsLeftText: {
        color: white,
        fontWeight: 'bold',
        fontSize: 10
    },
    exitButtonsRight: {
        backgroundColor: gray_t4,
        borderRadius: 20,
        width: 120
    },
    exitButtonsRightText: {
        color: white,
        fontWeight: 'bold'
    },
    safeAreaView: { flexGrow: 1 },
    profileHeaderView: {
        height: 111,
        backgroundColor: gray_t7,
        width: Layout.screen.width / 1.3,
        alignSelf: 'center',
        borderRadius: 8,
        marginLeft: 40,
        marginTop: 70,
        flexDirection: 'row'
    },
    settingsContainer: {
        width: Layout.screen.width / 1.15,
        alignSelf: 'center',
        marginTop: 20,
    },
    profileChangeImgView: {
        width: 24,
        height: 24,
        borderRadius: 12,
        backgroundColor: white,
        position: 'absolute',
        right: 35,
        bottom: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    profileChangeImgIcon: {
        width: 13,
        height: 11
    },
    profileImg: {
        height: 111,
        width: 111,
        left: -40,
        borderRadius: 55.5,
        borderWidth: 1.5,
        borderColor: white
    },
    profileDescView: {
        left: Layout.screen.width > 400 ?
            -20
            :
            -25,
        width: Layout.screen.width / 2,
        marginVertical: 15,
        justifyContent: 'flex-end'
    },
    profileNameSurname: {
        color: black_t3,
        fontWeight: 'bold',
        fontSize: 14
    },
    profileMailView: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 10
    },
    profileMailIcon: {
        width: 10,
        height: 7
    },
    profileMail: {
        color: black_t3,
        fontSize: 10,
        marginLeft: 5
    },
    phoneView: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 5
    },
    phoneIcon: {
        width: 8,
        height: 10
    },
    phone: {
        color: black_t3,
        fontSize: 10,
        marginLeft: 5
    },
    referansView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5
    },
    referansIdTitle: {
        color: black,
        fontSize: 12,
        fontWeight: 'bold'
    },
    referansId: {
        fontSize: 12,
        color: black
    },
    copyTouch: { marginLeft: 3 },
    copyIcon: {
        width: 20,
        height: 20
    },
    settingItemOneMapView: {
        alignSelf: 'center',
        width: Layout.screen.width / 1.15,
        marginVertical: 20,
    },
    settingItemOneMapTitle: {
        marginBottom: 10,
        fontWeight: 'bold',
        color: black_t3,
        letterSpacing: 2
    }
});

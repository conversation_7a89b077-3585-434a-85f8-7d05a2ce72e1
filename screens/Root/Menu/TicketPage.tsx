import React, { useState } from 'react';
import { SafeAreaView, View, Text, Image, StyleSheet, TouchableOpacity, Modal, TextInput, Alert, Linking } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { getImageURL } from '../../../networking/Server';
import QRCode from 'react-native-qrcode-svg';
import { black, black_t2, brown_t1, green_t1, red_t1, white, yellow_t1 } from '../../../constants/Color';
import { useNavigation } from '@react-navigation/native';
import { BackIcon } from '../../../components/Svgs';
import MapView, { Marker} from 'react-native-maps';
import { ScrollView } from 'react-native-gesture-handler';
import { post } from '../../../networking/Server';

const dayjs = require('dayjs');

type TicketPageParams = {
  TicketPage: {
    eventTitle: string;
    date: string;
    image: string;
    qrCodeUri: string;
    giftStatus: string,
    payment: string,
    status: string,
    ticketId: string,
    city: string,
    lat: string,
    long: string,
    gatedate: string,
    refundable: number,
  };
};

const TicketPage: React.FC = () => {
  const route = useRoute<RouteProp<TicketPageParams, 'TicketPage'>>();
  const { eventTitle, date, image, qrCodeUri, giftStatus, payment, status, ticketId, city, gatedate, lat, long, refundable } = route.params;
  const navigation = useNavigation();
  const [giftModalVisible, setGiftModalVisible] = useState(false);
  const [giftRef, setGiftRef] = useState('');
  const latitude = parseFloat(lat);
  const longitude = parseFloat(long)
  const [refundLoading, setRefundLoading] = useState(false);
  const [referralValid, setReferralValid] = useState<boolean|null>(null);
  const [validatingReferral, setValidatingReferral] = useState(false);

  React.useEffect(() => {
    if (!giftRef) {
      setReferralValid(null);
      return;
    }
    const timer = setTimeout(async () => {
      setValidatingReferral(true);
      try {
        const { type } = await post('/users/check-referral', { referralCode: giftRef });
        setReferralValid(type === 'success');
      } catch {
        setReferralValid(false);
      } finally {
        setValidatingReferral(false);
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [giftRef]);

  const handleSendGift = async () => {
    try {
      const { type, message, error } = await post('/users/send-ticket-gift', { ticketId, referralCode: giftRef });
      if (type === 'success') {
        Alert.alert('Başarılı', message);
        setGiftModalVisible(false);
      } else {
        Alert.alert('Hata', error);
      }
    } catch {
      Alert.alert('Hata', 'Sunucuya bağlanılamadı.');
    }
  };

  const handleRefundRequest = async () => {
    if (!refundable || status === 'iade-bekliyor') return;
  
    try {
      setRefundLoading(true);
      const { type, message, error } = await post('/users/refund-ticket', { ticketId });
  
      if (type === 'success') {
        Alert.alert('Başarılı', message || 'İade talebiniz alındı.');
            navigation.goBack
      } else {
        Alert.alert('Hata', error || 'İade talebi başarısız oldu.');
      }
    } catch {
      Alert.alert('Hata', 'Sunucuya bağlanılamadı.');
    } finally {
      setRefundLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
        <View style={styles.headerRow}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backIconTouch}>
            <BackIcon size={25} color={black} />
          </TouchableOpacity>
          <Image
            source={require('../../../assets/root/hollyticket.png')}
            style={styles.hollyTicketLogo}
            resizeMode="contain"
          />
        </View>
        <ScrollView showsVerticalScrollIndicator={false}>
            <Image source={{ uri: getImageURL(image) }} style={styles.eventImage} />
            <Text style={styles.title}>{eventTitle}</Text>
            <View style={styles.separator} />
            
            <View style={styles.timeArea} >
                <View>
                    <Text style={styles.dateTitle}>Tarih</Text>
                    <Text style={styles.date}>{dayjs(date)?.format("DD MMMM dddd")}</Text>
                </View>
                <View>
                    <Text style={styles.dateTitle}>Kapı Açılış</Text>
                    <Text style={styles.date}>{dayjs(gatedate)?.format("HH:mm")}</Text>
                </View>
            </View>
            <View style={styles.infoArea} >
                <View>
                    <Text style={styles.dateTitle}>Şehir</Text>
                    <Text style={styles.date}>{city}</Text>
                </View>
                <View>
                    <Text style={styles.dateTitle}>Sahne       </Text>
                    <Text style={styles.date}>{dayjs(date)?.format("HH:mm")}</Text>
                </View>
            </View>
    
            <View style={styles.separator} />
    
            <View style={styles.infoView}>
                <View style={styles.qrCodeArea} >
                    <QRCode value={qrCodeUri} size={130} />
                </View>
                <View style={styles.infoTextAre}>
                    <Text style={styles.subTitle} >Kare kodu personele okutunuz.</Text>
                    <Text style={styles.dateTitle} >Bilet Numaranız:</Text>
                    <Text style={styles.dateTitle} >{qrCodeUri}</Text>
                </View>
            </View>
    
            <View
              style={[
                styles.buttonRow,
                refundable === 1 && { justifyContent: 'space-between' }
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (giftStatus === 'sent' || status === 'iade-bekliyor') && styles.disabledButton
                ]}
                onPress={() => setGiftModalVisible(true)}
                disabled={giftStatus === 'sent' || giftStatus === 'iade-bekliyor'}
              >
                <Text style={styles.buttonText}>Hediye Gönder</Text>
              </TouchableOpacity>

            
              {refundable === 1 && (
                <TouchableOpacity
                  style={[
                    styles.cancelButton,
                    (status === 'iade-bekliyor' || refundLoading) && styles.disabledButton
                  ]}
                  onPress={handleRefundRequest}
                  disabled={status === 'iade-bekliyor' || refundLoading}
                >
                  <Text style={styles.buttonText}>
                    {refundLoading ? 'İşleniyor...' : 'İade Talebi'}
                  </Text>
                </TouchableOpacity>
              )}


            </View>
            {status === 'iade-bekliyor' && (
              <Text style={styles.processingText}>İade talebiniz işleniyor.</Text>
            )}

            <View style={styles.separator} />

            <View>
                <MapView
                  style={styles.map}
                  mapType="terrain"
                  initialRegion={{
                    latitude,
                    longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  }}
                >
                  <Marker coordinate={{ latitude, longitude }} />
                </MapView>
                <View style={styles.mapOverlay}>
                  <Text style={styles.overlayCity}>HollyStone Performance Hall {city}</Text>
                  <TouchableOpacity
                    style={styles.overlayButton}
                    onPress={() =>
                      Linking.openURL(
                        `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
                      )
                    }
                  >
                    <Text style={styles.overlayButtonText}>Yol Tarifi Al</Text>
                  </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
        <Modal visible={giftModalVisible} transparent animationType="slide">
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <TouchableOpacity 
                style={styles.modalCloseIcon} 
                onPress={() => setGiftModalVisible(false)}
              >
                <Text style={styles.modalCloseText}>✕</Text>
              </TouchableOpacity>
        
              <Text style={styles.modalTitle}>Hediye Gönder</Text>
        
              <TextInput
                placeholder="Arkadaşınızın referans numarasını girin"
                value={giftRef}
                onChangeText={setGiftRef}
                style={[
                  styles.input,
                  referralValid === false && { borderColor: red_t1 },
                  referralValid === true && { borderColor: green_t1 }
                ]}
              />
        
              {referralValid === false && giftRef.length > 0 && (
                <Text style={styles.errorText}>Bu referans kodu ile kullanıcı bulunamadı.</Text>
              )}
        
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, !referralValid && styles.disabledButton]}
                  onPress={handleSendGift}
                  disabled={!referralValid}
                >
                  <Text style={styles.modalButtonText}>
                    {validatingReferral ? 'Kontrol ediliyor…' : 'Gönder'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f2',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  modalCloseIcon: {
    position: 'absolute',
    zIndex: 99999,
    top: 10,
    right: 20,
    width: 25,
    height: 25,
    backgroundColor: red_t1,
    borderRadius: 25,
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 16,
    color: white,
    fontWeight: '600'
  },
  errorText: {
    color: red_t1,
    fontSize: 12,
    marginBottom: 20,
    textAlign: 'left',
  },
  processingText: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    color: black_t2,
  },  
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backIconTouch: {
    marginRight: 12,
  },
  hollyTicketLogo: {
    width: 140,
    height: 30,
  },
  timeArea: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 30,
    marginLeft: 30,

  },
  subTitle:{
    color: '#000',
    fontWeight: '800',
    marginBottom: 30
  },
  infoArea: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 30,
    marginLeft: 30,
  },
  title: {
    fontSize: 24,
    color: '#3f3f3f',
    fontWeight: '700',
    textAlign: 'center',
    
  },
  dateTitle:{
    color: '#3f3f3f',
    textAlign: 'left',
  },
  date: {
    fontSize: 16,
    marginTop: 5,
    color: '#000',
    fontWeight: '800',
    textAlign: 'left',
    marginBottom: 16,
  },
  eventImage: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    marginBottom: 15,
    backgroundColor: black_t2,
    marginTop: 20
  },
  separator: {
    borderBottomWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#ccc',
    marginVertical: 24,
  },
  infoView: {
    flexDirection: 'row',
    justifyContent: 'space-between',    
    width: '100%',
  },
  qrCodeArea: {
   
    borderRadius: 10,
  },
  infoTextAre:{
    alignContent: 'center',
    justifyContent: 'center',
  },

  mapOverlay: {
    position: 'absolute',
    top: 15,
    left: 0,
    padding: 10,

  },
  overlayCity: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 6,
    color: black_t2,
  },
  overlayButton: {
    backgroundColor: green_t1,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    width: 90
  },
  overlayButtonText: {
    color: white,
    fontSize: 12,
    fontWeight: '600',
  },
  buttonRow: {
    flexDirection: 'row',
    marginVertical: 16,
    justifyContent: 'center',
    marginTop: 30,
  },
  actionButton: {
    backgroundColor: brown_t1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '45%'    
  },
  cancelButton: {
    backgroundColor: red_t1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '45%'    
  },
  buttonText: { 
    color: white, 
    fontWeight: '600', 
    textAlign: 'center'
  },
  modalOverlay: {
    flex:1, 
    backgroundColor:'rgba(0, 0, 0, 0)', 
    justifyContent:'center', 
    alignItems:'center'  
  },
    modalContent: {
       backgroundColor:'#fff', padding:20, borderRadius:12, width:'80%'

    },
    modalTitle: { fontSize:18, fontWeight:'700', marginBottom:12, color: black_t2 },
    input: { borderWidth:1, borderColor:'#ccc', borderRadius:6, padding:8, marginBottom:10, },
    modalButtons: { flexDirection:'row', justifyContent:'space-between' },
    modalButton: { backgroundColor: yellow_t1, padding:12, borderRadius:6, flex:1, marginRight:8 },
    modalButtonText: { color:'#fff', textAlign:'center' },
    modalCancel: { backgroundColor:'#ccc', marginRight:0 },
    modalCancelText: { color:'#333', textAlign:'center' },
    map: { width:'100%', height:200, borderRadius:12, marginTop:16, marginBottom:20 },

});

export default TicketPage;

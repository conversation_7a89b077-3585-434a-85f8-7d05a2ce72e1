import React from 'react';
import { View, Text, StyleSheet, Dimensions, Image } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import QRCode from 'react-native-qrcode-svg';
import { black, red_t1, white } from '../../../constants/Color';
import { getImageURL } from '../../../networking/Server';
const dayjs = require('dayjs');

const { width } = Dimensions.get('window');
const TICKET_WIDTH = width * 0.8;
const TICKET_HEIGHT = (135 / 288.6) * TICKET_WIDTH; // maintain aspect ratio

const ConcertTicket: React.FC<{
    eventTitle: string;
    image: string;
    date: string;
    qrCode: string;
    status: string;
  }> = ({ eventTitle, image, date, qrCode, status }) => (

  <View style={styles.container}>
    {/* SVG background rendered via react-native-svg */}
    <Svg
      width={TICKET_WIDTH}
      height={TICKET_HEIGHT}
      viewBox="0 0 288.6 135"
      style={styles.svg}
    >
      <Path
        fill="#fff"
        fillOpacity={0.8}
        d="M5.8,135h147.2c2.5,0,4.9-1.3,6.1-3.5s4.3-4.5,7.5-4.5,6.1,1.8,7.5,4.5,3.6,3.5,6.1,3.5h107.1c.6,0,1-.5,1-1v-.4c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.3c0-.5-.3-.9-.8-1-1.5-.4-2.6-1.7-2.6-3.3s1.1-2.9,2.6-3.3.8-.5.8-1v-.4c0-.6-.5-1-1-1h-107c-2.7,0-5,1.6-6.2,3.9-1.4,2.7-4.3,4.6-7.6,4.6s-6.2-1.9-7.6-4.6-3.5-3.9-6.2-3.9H5.8C2.6,0,0,2.6,0,5.8v123.3c0,3.2,2.6,5.8,5.8,5.8ZM167.5,124.5c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,117.2c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,110c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,102.8c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,95.5c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,88.3c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,81.1c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,73.9c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,66.6c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,59.4c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,52.2c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,44.9c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,37.7c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,30.5c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,23.2c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8ZM167.5,16c-1.9.5-3.6-1.2-3.1-3.1s.9-1.6,1.8-1.8c1.9-.5,3.6,1.2,3.1,3.1s-.9,1.6-1.8,1.8Z"
      />
    </Svg>

    {/* Overlay Content */}
    <View style={styles.overlay}>
      <View style={styles.leftSection}>
        <Image source={{ uri: getImageURL(image) }} style={styles.ticketImage} />
        <Text style={styles.movieTitle}>{eventTitle}</Text>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>{date}</Text>
        </View>
      </View>
      <View style={styles.rightSection}>
        <View style={styles.qrSection}>
        <QRCode value={qrCode} size={70} />
        </View>
        <Text style={styles.barcodeText}>{qrCode}</Text>
      </View>
    </View>
    {status === 'kullanildi' && (
      <View style={styles.usedRibbon}>
        <Text style={styles.usedRibbonText}>KULLANILDI</Text>
      </View>
    )}

  </View>
);

const styles = StyleSheet.create({
  container: { alignItems: 'center', marginVertical: 16 },
  svg: { borderRadius: 8 },
  overlay: {
    position: 'absolute',
    flexDirection: 'row',
    width: TICKET_WIDTH,
    height: TICKET_HEIGHT,
    
    alignItems: 'center',
    justifyContent: 'space-between',
},
usedRibbon: {
    position: 'absolute',
    top: TICKET_HEIGHT * 0.4,
    left: -TICKET_WIDTH * 0.2,
    width: TICKET_WIDTH * 1.4,
    backgroundColor: 'rgba(255,0,0,0.75)',
    transform: [{ rotate: '-45deg' }],
    alignItems: 'center',
    paddingVertical: 4,
  },
  usedRibbonText: {
    color: white,
    fontSize: 18,
    fontWeight: '700',
  },
  
ticketImage: {
    width: '95%',
    height: 80,
    borderRadius: 8,
    marginBottom: 8,
  },
  leftSection: {
    flex: 5,
    padding: 10,
},
  movieTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#000'
},
  infoRow: { 
    flexDirection: 'row', 
},
  infoLabel: { 
    fontSize: 10, 
    color: '#000', 
    marginRight: 4 
},
  infoValue: { 
    fontSize: 12, 
    fontWeight: '600', 
    color: '#000', 
    marginRight: 12 
},
  rightSection: { 
    flex: 4, 
    alignItems: 'center', 
    
},
qrSection:{
    backgroundColor: white,
    padding: 10,
    borderRadius: 10,
},
barcodeText: { 
    paddingRight: 10, 
    paddingLeft: 10,
    marginTop:5,
    textAlign: 'center',
    fontSize: 10, 
    color: '#000' 
},
});

export default ConcertTicket;

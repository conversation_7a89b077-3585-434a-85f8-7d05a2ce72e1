import React, { useState } from "react";
import { Animated, Button, FlatList, Image, ImageBackground, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Layout from "../../../constants/Layout";
import { black, black_t3, black_t6, gray_t11, green_t1, white, brown_t1, red_t6, kremrengi, pink, red_t1 } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { Input } from "native-base";
import Toast from "../../../components/Toast";
import { get, getImageURL, post } from "../../../networking/Server";
import BottomBar from "../../../components/BottomBar";
import { BackIcon } from "../../../components/Svgs";
import { launchImageLibrary } from 'react-native-image-picker'
import Carousel from "../../../components/Carousel";
import Svg, { Path, Rect } from "react-native-svg";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

const LeaderboardItem = ({ item, index }) => {
    return (
      <View style={styles.itemContainer}>
        <View style={styles.rowContainer}>
          {/* Profile Image */}
          <View style={styles.profileContainer}>
            <Image
              source={{ uri: getImageURL(item?.image) }}
              style={styles.profileImage}
            />
          </View>
          
          {/* User Name */}
          <Text style={styles.userName}>{item.nameLastName}</Text>
          
          {/* Holly Points with Green Background */}
          <View style={styles.pointsContainer}>
                <Image
                  style={styles.shamanListItemHPLogo}
                  resizeMode="contain"
                  source={require('../../../assets/root/hP.png')}
                />
              <Text style={styles.pointsText}>{item.hollyPoints}</Text>
          </View>
          
          {/* Green Arrow Icon */}
          <View style={styles.arrowContainer}>
            <View style={styles.greenCircle}>
              <Image
                  style={styles.shamanListItemDownUpIcon}
                  resizeMode="contain"
                  source={ require('../../../assets/root/shamanListUp.png')
                  }
                />
            </View>
          </View>
        </View>
      </View>
    );
  };

const LoadingSkeleton = () => {
    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            <SkeletonPlaceholder 
                backgroundColor="#E9E9E9" 
                highlightColor="#FFFFFF"
                speed={1200}
            >
                {/* Profile Top Section */}
                <SkeletonPlaceholder.Item>
                    {/* Cover Image */}
                    <SkeletonPlaceholder.Item
                        width={Layout.screen.width}
                        height={200}
                        marginTop={-10}
                    />
                    
                    {/* Profile Card */}
                    <SkeletonPlaceholder.Item
                        width={Layout.screen.width - 40}
                        height={160}
                        borderRadius={15}
                        marginTop={-110}
                        alignSelf="center"
                        backgroundColor={white}
                    >
                        {/* Holly Points & Level */}
                        <SkeletonPlaceholder.Item  marginTop={-20} flexDirection="row" justifyContent="space-between" padding={15}>
                            <SkeletonPlaceholder.Item width={80} height={40} borderRadius={8}/>
                            <SkeletonPlaceholder.Item width={80} height={40} borderRadius={8}/>
                        </SkeletonPlaceholder.Item>
                        
                        {/* Profile Image & Info */}
                        <SkeletonPlaceholder.Item  marginTop={-30} alignItems="center">
                            <SkeletonPlaceholder.Item width={80} height={80} borderRadius={40}/>
                            <SkeletonPlaceholder.Item width={120} height={15} borderRadius={4} marginTop={10}/>
                            <SkeletonPlaceholder.Item width={100} height={12} borderRadius={4} marginTop={5}/>
                            <SkeletonPlaceholder.Item width={150} height={25} borderRadius={12} marginTop={10}/>
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>

                {/* Calendar Section */}
                <SkeletonPlaceholder.Item 
                    flexDirection="row" 
                    marginTop={20}
                    paddingHorizontal={15}
                >
                    {[1,2,3,4,5,6,7,8,9,10,11,12].map((_, index) => (
                        <SkeletonPlaceholder.Item
                            key={index}
                            width={20}
                            height={80}
                            borderRadius={8}
                            marginRight={10}
                        />
                    ))}
                </SkeletonPlaceholder.Item>

                {/* Tickets Section */}
                <SkeletonPlaceholder.Item marginTop={20} padding={15}>
                    {/* Ticket Tabs */}
                    <SkeletonPlaceholder.Item flexDirection="row" justifyContent="space-between">
                        <SkeletonPlaceholder.Item width="48%" height={30} borderRadius={15}/>
                        <SkeletonPlaceholder.Item width="48%" height={30} borderRadius={15}/>
                    </SkeletonPlaceholder.Item>

                    {/* Ticket Card */}
                    <SkeletonPlaceholder.Item
                        width="100%"
                        height={200}
                        borderRadius={15}
                        marginTop={15}
                    />
                </SkeletonPlaceholder.Item>

                {/* Shaman List Section */}
                <SkeletonPlaceholder.Item 
                    marginTop={20}
                    height={160}
                    marginHorizontal={15}
                    borderRadius={15}
                    flexDirection="row"
                    justifyContent="space-around"
                    alignItems="center"
                >
                    {[1,2,3].map((_, index) => (
                        <SkeletonPlaceholder.Item
                            key={index}
                            width={90}
                            height={index === 1 ? 160 : 150}
                            borderRadius={10}
                        />
                    ))}
                </SkeletonPlaceholder.Item>


            </SkeletonPlaceholder>
        </ScrollView>
    );
};

const Profile: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- ACTIVE | USED -- //
    const [ticket, setTicket] = React.useState(0);
    const scrollXIndex = React.useRef(new Animated.Value(1)).current;
    const scrollXAnimated = React.useRef(new Animated.Value(0)).current;

    // -- LOADING -- // 
    const [loading, setLoading] = React.useState(true);
    const [user, setUser]: any = React.useState({});
    const [userActivities, setUserActivities]: any = React.useState([]);
    const [activeTickets, setActiveTickets]: any = React.useState([]);
    const [usedTickets, setUsedTickets]: any = React.useState([]);
    const [shamanList, setShamanList]: any = React.useState([]);

    console.log(shamanList)

    const [walletInfo, setWalletInfo] = React.useState([]);
    

    const [mood, setMood] = React.useState("");

    // -- DATES --//
    const turkishMonths = [
        "Oca",
        "Şub",
        "Mar",
        "Nis",
        "May",
        "Haz",
        "Tem",
        "Ağu",
        "Eyl",
        "Eki",
        "Kas",
        "Ara"
    ]
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentMonthName = turkishMonths[currentMonth];
    const currentYear = currentDate.getFullYear();
    const lastDay = new Date(currentYear, currentMonth + 1, 0).getDate();
    const daysArray = Array.from({ length: lastDay }, (_, index) => index + 1);

    // -- GET USER INFO -- //,
    const getUserData = () => {
        try {
            get("/users/profile").then((res: any) => {
                if (res.type == "success") {
                    setUser(res.user);
                    setUserActivities(res.userActivities);
                    setActiveTickets(res.activeTickets);
                    setUsedTickets(res.usedTickets);
                    setShamanList(res.shamanList);
                    setLoading(false);
                } else {
                    startToast(res.error, "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 1500)
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 1500)
        }
    }

    const sendMood = () => {
        try {
            const data = { mood: mood };
            post("/users/set-mood", data).then((res: any) => {
                if (res.type === "success") {
                    startToast(res.message, "success");
                } else {
                    startToast(res.error, "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 1500);
                }
            });
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 1500);
        }
    };

    const [imageUri, setImageUri] = React.useState("");
    const [imageType, setImageType] = React.useState("");
    const [imageName, setImageName] = React.useState("");
    const [image, setImage] = React.useState("");

    const setImages = () => {
        launchImageLibrary({
            mediaType: "photo",
            maxHeight: 600,
            maxWidth: 600,
            quality: 1,
        }, (callback: any) => {
            if (!callback.didCancel && callback.assets.length > 0) {
                const uri = callback.assets[0].uri;
                const type = callback.assets[0].type;
                const fileName = callback.assets[0].fileName;
                
                setImageUri(uri);
                setImageType(type);
                setImageName(fileName);
        
                // Dosya özelliklerini handleUpdate fonksiyonuna iletip post isteği yap
                handleUpdate(uri, type, fileName); 
            } else {
                startToast("Lütfen fotoğraf seçiniz.", "error");
            }
        });
    }

    const handleUpdate = (uri: string, type: string, fileName: any) => {
        setLoading(true);
        startToast("Fotoğraf yükleniyor. Lütfen bekleyin!", "success");
    
        const formData = new FormData();
        formData.append('image', {
            uri: uri,
            type: type,
            name: fileName
        });
    
        post("users/cover-pic", formData).then((resp: any) => {
            if (resp.type == "success") {
                startToast("Fotoğraf başarılı bir şekilde yüklendi!", "success");
                setTimeout(() => {
                    navigation.pop();
                }, 2000)
            } else {
                startToast(resp.error, "error");
            }
            setLoading(false);
        }).catch((error: any) => {
            startToast("Bir şeyler ters gitti", "error");
            setLoading(false);
        });
    }

    const checkWallet = () => {
            get('wallet/check-wallet')
                .then((res: any) => {
                    if (res.type === 'success') {
                        setWalletInfo(res.walletInfo);
                    } else {
                    }
                })
                .catch((error) => {
                    console.error('Hata:', error);
                });
        };
        
       
    

    React.useEffect(() => {
        getUserData();
        checkWallet();
    }, []);

    React.useEffect(() => {
        Animated.spring(scrollXAnimated, {
            toValue: scrollXIndex,
            useNativeDriver: true
        }).start();
    })
    return (
        <View

            style={styles.main}
        >
           
            {/* BACK ICON */}
            <View style={styles.topbar}>
            <TouchableOpacity
                style={styles.leftIconTouch}
                onPress={() => {
                    navigation.pop();
                }}
            >
                <BackIcon
                    size={25}
                    color={black}
                />
            </TouchableOpacity>
            </View>
             


            <ScrollView showsVerticalScrollIndicator={false}>
                {loading ? <LoadingSkeleton /> : (
                    <>
                        {/* PROFILE TOP */}
                        <ImageBackground
                            style={styles.profileTopBack}
                            source={require('../../../assets/menu/profileTopBack.png')}
                        >
                            <TouchableOpacity onPress={setImages}  >
                                <Image
                                    source={{ uri: getImageURL(user.cover) }}
                                    style={styles.profileImg}
                                />
                                
                            </TouchableOpacity>
                            <View style={[styles.profileTopWhiteBack, shadow]}>
                                <View style={styles.profileTopWhiteAlt}>
                                    {/* HOLLY PUAN */}
                                    <TouchableOpacity


                                        onPress={() => {
                                            navigation.navigate("HollyPuan");
                            }}
                                        style={[styles.profileTopWhiteLRView, shadow]}
                                    >
                                        <Image
                                            style={styles.hollyPuanLogo}
                                            resizeMode="contain"
                                            source={require('../../../assets/root/hollyPuanLogo.png')}
                                        />
                                        <View style={styles.hollyPuanAltView}>
                                            <Text style={styles.hP}>{user.hollyPoints}</Text>
                                            
                                        </View>
                                    </TouchableOpacity>

                                    {/* PROFILE IMG */}
                                    <View style={{ top: -60, alignItems: 'center' }}>
                                        <View style={styles.prfImgView}>
                                            <Image
                                                source={{ uri: getImageURL(user.image) }}
                                                style={styles.prf}
                                            />
                                        </View>
                                        <Text style={styles.prfName}>{user.firstName}</Text>
                                        <Text style={styles.userId}>{user.referralCode}</Text>
                                        <View style={styles.statusInputView}>
                                            <Input
                                                color={black}
                                                placeholderTextColor={black_t3}
                                                textAlign={'center'}
                                                height={25}
                                                maxHeight={35}
                                                fontSize={8}
                                                borderWidth={0}
                                                placeholder={user.mood ? user.mood : "durum ayarla!"}
                                                onChangeText={(text) => setMood(text)} 
                                                onSubmitEditing={() => {
                                                    sendMood();
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* LEVEL */}
                                    <TouchableOpacity
                                        onPress={() => {
                                            startToast( "Modül henüz aktif değil", "error");
                                        }}
                                        style={[styles.profileTopWhiteLRView, shadow]}
                                    >
                                        <Image
                                            style={{
                                                width: 66,
                                                height: 17.33
                                            }}
                                            resizeMode="contain"
                                            source={require('../../../assets/root/level.png')}
                                        />
                                        <Text style={{
                                            fontSize: 25,
                                            color: black,
                                            fontFamily: 'MADE TOMMY',
                                            top: 2,
                                        }}>{user.level + 1}</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </ImageBackground>
                        {/** 48-27 */}

                        {/* COME OR NONE COME */}
                        <View style={styles.comeNoView}>
                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>

                                {
                                    daysArray.map((item: any, index: number) => {
                                        const isHappy = userActivities.findIndex((activity: any) => activity.dayNumber == item);
                                        if (isHappy != -1) {
                                            return (
                                                <View key={index} style={[styles.comeNoItem, { backgroundColor: green_t1 }]}>
                                                    <Text style={[styles.comeNoText, { marginTop: 2, color: white }]}>{item}</Text>
                                                    <Text style={[styles.comeNoText, { color: white }]}>{currentMonthName}</Text>
                                                    <Image
                                                        source={require('../../../assets/menu/star-struck.png')}
                                                        style={[styles.comeNoIcon, { marginTop: 4 }]}
                                                    />
                                                </View>
                                            )
                                        } else if (item > currentDate.getUTCDate()) {
                                            return (<View key={index} style={styles.comeNoItem}>
                                               <Image
                                                        source={require('../../../assets/menu/sleeping.png')}
                                                        style={styles.comeNoIcon}
                                                    />
                                                
                                                <Text style={[styles.comeNoText, { marginTop: 4 }]}>{item}</Text>
                                                <Text style={styles.comeNoText}>{currentMonthName}</Text>
                                            </View>)
                                        } else {
                                            return (
                                                <View key={index} style={styles.comeNoItem}>
                                                    <Image
                                                        source={require('../../../assets/menu/pensive.png')}
                                                        style={styles.comeNoIcon}
                                                    />
                                                    <Text style={[styles.comeNoText, { marginTop: 4 }]}>{item}</Text>
                                                    <Text style={styles.comeNoText}>{currentMonthName}</Text>
                                                </View>
                                            )
                                        }
                                    })
                                }
                            </ScrollView>
                        </View>

                        {/* ACTIVE AND USED TICKET AREA  */}
                        <View style={styles.activeUsed}>

                            {/* ACTIVE TICKET */}
                            <TouchableOpacity
                                onPress={() => { 
                                    setTicket(1)
                                }}
                                activeOpacity={1}
                            
                                style={styles.activeView}
                            >
                                <Svg width='100%' height='100%' viewBox="0 0 210 28">
                                <Rect x="10" y="0" width="100%" height="100%" fill={ticket === 1 ? '#00731C' : '#0BDC05'} />
                                  <Path
                                    fill="#0BDC05"
                                    d="M196.14,27h11.21c-5.9,0-11.46-2.74-15.06-7.42l-4.3-5.58-6.41-8.97C179.34,1.87,175.7,0,171.82,0H10C4.48,0,0,4.48,0,10v17"/>
                                </Svg>
                                <Text
                                    style={
                                        [
                                            styles.activeViewText,
                                            { color: white, right: 40, top: 5 }
                                        ]
                                    }
                                >
                                    aktif biletler
                                </Text>
                            </TouchableOpacity>

                            {/* USED TICKET */}
                            <TouchableOpacity
                                onPress={() => {
                                    setTicket(2)
                                    
                                }}
                                activeOpacity={1}
                                style={styles.usedView}
                            >
                                <Svg width='100%' height='100%' viewBox="0 0 207 28">
                                <Rect x="0" y="0"  width="50%" height="100%" fill={ticket === 1 ? '#00731C' : '#0BDC05'} />
                                  <Path
                                    fill="#00731C"
                                    d="M207.36,27V10c0-5.52-4.48-10-10-10H35.53c-3.88,0-7.51,1.87-9.76,5.03l-6.41,8.97-4.3,5.58c-3.6,4.68-9.16,7.42-15.06,7.42h11.21"/>
                                </Svg>
                                <Text
                                    style={
                                        [
                                            styles.activeViewText,
                                            { color: white }
                                        ]
                                    }
                                >
                                    kullanılan biletler
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* ACTIVE ALT VIEW */}
                        <View style={
                            [
                                styles.activeAltView,
                                {
                                    backgroundColor: ticket == 1 ? '#0BDC05' : green_t1,
                                }
                            ]
                        }
                        >
                            {
                                ticket == 1 ?
                                    <Carousel
                                        type={2}
                                        data={activeTickets}
                                    />
                                    :
                                    <Carousel
                                        type={1}
                                        data={usedTickets}
                                    />
                            
                            }
                        </View>

                        <TouchableOpacity style={styles.walletView}   onPress={() => navigation.navigate('Wallet')}
                        >

                        <View style={styles.walletLeft}>
                          <Text style={styles.welcomeText}>Hoşgeldin {user.firstName}!</Text>
                          {walletInfo && walletInfo.id ? (
                            <>
                              <Text style={styles.balanceText}>
                                Bakiyeniz: <Text style={styles.balanceAmount}>{walletInfo.balance} ₺</Text>
                              </Text>
                              <Text style={styles.walletNumberText}>
                                Cüzdan Numarası: <Text style={styles.walletNumberValue}>{walletInfo.id}</Text>
                              </Text>
                            </>
                          ) : (
                            <Text style={styles.walletNumberText}>Cüzdanınız bulunmamaktadır.</Text>
                          )}
                        </View>
                            <View style={styles.walletRight}>
                                <Image
                                  source={require('../../../assets/cuzdanwhite.png')}
                                  style={styles.walletImage}
                                  resizeMode="contain"
                                />
                            </View>

                        </TouchableOpacity>



<TouchableOpacity style={styles.shamanTopView} navigation={navigation} onPress={() => navigation.navigate('Shaman')}>
    
    <Image
    source={require('../../../assets/root/shaman.png')}
    style={styles.shamanLogo}
  />
  
<FlatList
    data={[...shamanList].sort((a, b) => parseInt(b.hollyPoints) - parseInt(a.hollyPoints))}

        keyExtractor={(item) => item.id}
        renderItem={({ item, index }) => <LeaderboardItem item={item} index={index} />}
      />
</TouchableOpacity>
                        
                        



                    </>
                )
                }
            </ScrollView >
             {/* TOAST */}
             <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            <BottomBar type={1} navigation={navigation} />
        </View >
    )
}
export default Profile;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
    flexGrow: 1,
    backgroundColor: kremrengi
 },
 walletView:{
    height: 140,
    width: Layout.screen.width / 1.11,
    marginTop: 30,
    alignSelf: 'center',
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: pink
 },
 walletLeft:{
    width: '50%',
    padding: 16,
 },
 walletRight: {
    width: '50%',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    right: 14,
    bottom: 10,
 },
 walletImage: {
    width: 130,
    height: 40,
  },
  welcomeText: {
    fontSize: 17,
    fontFamily: 'MADE TOMMY',
    color: white,
    marginBottom: 8,
  },
  
  balanceText: {
    fontSize: 16,
    fontWeight: '600',
    color: white,
    marginBottom: 4,
  },
  balanceAmount: {
    color: white,
  },
  
  walletNumberText: {
    fontSize: 14,
    color: white,
  },
  walletNumberValue: {
    fontWeight: '600',
    color: white,
  },
    leftIconTouch: {
        left: 25,
        position: 'absolute',
        zIndex: 10,
        top: 20,
    },
    safeAreaView: {
        flex: 1,
    },
    topbar: {
        width: '100%',
        backgroundColor: "rgba(255, 255, 255, 0.6)",
        height: 60,
        position: "absolute",
        top: 0,
        zIndex: 20,

    },
    leftIcon: {
        height: 25,
        width: 15
    },
    zoomView: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: 1010,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute'
    },
    zoomCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    fliCardView: {
        height: 224
    },
    backFlip: {
        height: 206.16,
        width: 300.4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    cardId: {
        color: white,
        fontSize: 32,
        fontFamily: 'College',
        fontWeight: 'bold'
    },
    goldCard: {
        height: 206.16,
        width: 300.4
    },
    discountView: {
        position: 'absolute',
        left: 10,
        top: 10
    },
    discount: {
        fontFamily: 'MADE TOMMY',
        color: white,
        fontSize: 26
    },
    discounText: {
        fontSize: 12,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
    centerView: {
        position: 'absolute',
        top: 40,
        right: Layout.screen.width / 10,
        alignItems: 'center'
    },
    centerTitle: {
        fontSize: 36,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
    centerPrice: {
        fontSize: 24,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
    nameView: {
        position: 'absolute',
        bottom: 24,
        left: 68
    },
    name: {
        fontSize: 13,
        color: white,
        fontFamily: 'College'
    },
    cardNo: {
        fontSize: 13,
        color: white,
        fontFamily: 'College'
    },
    zoomCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 200,
        left: 5,
        right: 5,
        
    },
    modal: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalAltView: {
        width: Layout.screen.width / 1.1,
        paddingVertical: 0,
        backgroundColor: "rgba(256,256,256,0.8)",
        borderRadius: 32,
        minHeight: 300,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalWarnText: {
        alignSelf: 'center',
        marginTop: 10,
        fontFamily: 'MADE TOMMY',
        letterSpacing: 1.1
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    profileTopBack: {
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        height: 242
    },
    profileTopWhiteBack: {
        height: 106,
        width: Layout.screen.width / 1.05,
        backgroundColor: white,
        bottom: 0,
        position: 'absolute',
        borderBottomLeftRadius: 40,
        borderBottomRightRadius: 40
    },
    profileTopWhiteAlt: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15
    },
    profileTopWhiteLRView: {
        width: 87,
        height: 74,
        backgroundColor: white,
        borderBottomLeftRadius: 37,
        borderBottomRightRadius: 37,
        alignItems: 'center',
        paddingTop: 10
    },
    hollyPuanLogo: {
        width: 84,
        height: 14
    },
    hollyPuanAltView: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    hP: {
        fontSize: 24,
        top: 5,
        color: green_t1,
        marginRight: 2,
        fontFamily: 'MADE TOMMY'
    },
    hPLogo: {
        width: 19.86,
        height: 22
    },
    prfImgView: {
        width: 95,
        height: 95,
        borderRadius: 49,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center'
    },
    prf: {
        height: 85,
        borderRadius: 42.5,
        borderWidth: 1.5,
        width: 85
    },
    prfName: {
        fontSize: 18,
        color: black,
        fontWeight: 'bold'
    },
    userId: {
        fontSize: 9,
        color: black
    },
    statusInputView: {
        marginTop: 5,
        height: 25,
        width: 97,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: green_t1
    },
    comeNoView: {
        flexDirection: 'row',
        marginTop: 20,
        width: Layout.screen.width / 1.02,
        alignSelf: 'flex-end'
    },
    comeNoItem: {
        height: 62,
        width: 27,
        backgroundColor: white,
        borderRadius: 18,
        alignItems: 'center',
        paddingVertical: 2,
        borderWidth: 1,
        borderColor: gray_t11,
        marginLeft: 5
    },
    comeNoIcon: {
        width: 21,
        height: 21
    },
    comeNoText: {
        fontSize: 10,
        fontWeight: 'bold',
        color: black
    },
    activeUsed: {
        marginTop: 20,
        flexDirection: 'row',
        alignSelf: 'center',
        width: '90%',
        height: 30,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        overflow: 'hidden',
        
       
    },
    activeView: {
        borderTopLeftRadius: 12,
        width: '50%',
        
    },
    activeViewText: {
        color: black,
        fontWeight: 'bold',
        fontSize: 12,
        position: 'absolute',
        top: 5
        
    },
    activeTicket: {
        width: 48,
        alignSelf: 'center'
    },
    usedView: {
        width: '50%',
        borderTopRightRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden'
        
    },
    activeAltView: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        borderBottomEndRadius: 10,
        borderBottomLeftRadius: 10,
        marginTop: -5,
        height: 210
    },
    shamanTopView: {
        width: Layout.screen.width / 1.11,
        height: 230,
        marginTop: 30,
        alignSelf: 'center',
        backgroundColor: white,
        marginBottom: 150,
        justifyContent: 'center',
        borderRadius: 10,

    },

    shamanLogo: {
        width: 120,
        height: 25,
        alignContent: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        marginBottom: 15,
        marginTop: 15,
    },
    shamanTopTitle: {
        position: 'absolute',
        fontSize: 9,
        top: 9,
        fontWeight: 'bold',
        alignSelf: 'center',
        color: white
    },
    shamanTopPrf: {
        width: 50,
        height: 50,
        borderRadius: 35,
        alignSelf: 'center',
        top: 33,
        position: 'absolute'
    },
    shamanTopListTitle: {
        position: 'absolute',
        fontSize: 10,
        top: 95,
        fontWeight: 'bold',
        alignSelf: 'center',
        color: black,
        textAlign: 'center',
    },
    
    shamanTophPView: {
        height: 17.8,
        position: 'absolute',
        top: 118,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    
    shamanTopHPLogo: {
        width: 13,
        height: 14,
        left: 10
    },
    shamanTopHP: {
        fontSize: 12,
        color: green_t1,
        marginLeft: 20,
        fontWeight: 'bold'
    },

    Cmain: {
        top: 25,
        width: 300,
        height: 120
    },
    CmainAlt: {
        alignSelf: 'flex-end',
        width: 100,
        marginRight: 71
    },
    CtitleTime: {
        marginTop: 49,
        fontSize: 8,
        fontWeight: 'bold',
        color: black_t3
    },
    CcountDownView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 8,
        left: -1.5
    },
    CcountDownText: {
        fontSize: 8,
        color: white
    },
    profileChangeImgView:{
        width: '100%',
        height: 200,
        top: 0,
    },
    profileImg: {
        width: '100%',
        height: 200,
    },
    // Regular list item styles
  itemContainer: {
    paddingVertical: 4,
    backgroundColor: white,
    borderRadius: 30,
    padding: 10,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    borderRadius: 100,
    backgroundColor: white,
    borderWidth: 1,
    borderColor: gray_t11,
    paddingVertical: 6,
  },
  profileContainer: {
    width: 30,
    height: 30,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 30,
    height: 30,
    borderRadius: 20,
  },
  userName: {
    flex: 1,
    marginLeft: 12,
    fontSize: 12,
    color: black,
    fontWeight: '500',
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  pointsBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pointsText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: green_t1,
  },
  rankText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  arrowContainer: {
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  greenCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: green_t1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginTop: 4,
  },
  shamanListItemDownUpIcon: {
    width: 14.72,
    height: 5.25
},
shamanListItemHPLogo: {
    width: 17,
    height: 18.97,
    marginRight: 8
},

});
import React from "react";
import { StyleSheet, ImageBackground, View, Text, ScrollView } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import { gray_t4, gray_t7, green_t1, kremren<PERSON>, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import MenuProfileHeader from "../../../components/MenuProfileHeader";
import SettingsFirstInput from "../../../components/SettingsFirstInput";
import SettingsSecondInput from "../../../components/SettingsSecondInput";
import { <PERSON><PERSON>, Spinner } from "native-base";
import { shadow } from "../../../constants/Shadow";
import SettingsSelect from "../../../components/SettingsSelect";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { WriteIcon } from "../../../components/Svgs";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";


const Career: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingOpenPositions, setLoadingOpenPositions] = React.useState(true);
    const [buttonLoading, setButtonLoading] = React.useState(false);

    // -- PROFILE INFO -- //
    const [nameLastName, setNameLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [dateOfBirth, setDateOfBirth] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [city, setCity] = React.useState("");

    // -- CAREER -- //
    const [position, setPosition] = React.useState("");
    const [resume, setResume] = React.useState("");
    const [description, setDescription] = React.useState("");
    const [openPositions, setOpenPositions] = React.useState([]);
    const [verify, setVerify] = React.useState(false);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const LoadingSkeleton = () => {
        return (
            
            <ScrollView 
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.skeletonContainer}

            >
                <View style={[styles.form2, styles.skeletonForm]}>

                {/* Profile Header Skeleton */}
                <SkeletonPlaceholder 
                    backgroundColor="#F5F5F5" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item 
                        flexDirection="row" 
                        padding={20}
                        backgroundColor={white}
                        borderRadius={15}
                        marginHorizontal={15}
                        height={160}
                    >
                        {/* Profile Info */}
                        <SkeletonPlaceholder.Item marginLeft={15} flex={1}>
                            <SkeletonPlaceholder.Item
                                width="80%"
                                height={12}
                                borderRadius={6}
                            />
                            <SkeletonPlaceholder.Item
                                width="60%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                            <SkeletonPlaceholder.Item
                                width="40%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                </View>
    
                {/* Form Skeleton */}
                <View style={[styles.form, styles.skeletonForm]}>
                    <SkeletonPlaceholder 
                        backgroundColor="#F5F5F5" 
                        highlightColor="#FFFFFF"
                        speed={1200}
                    >
                        {/* Subject Input */}
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={90}
                                borderRadius={15}
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={90}
                                borderRadius={15}
                                marginTop={30}
                            />
    
                            {/* Submit Button */}
                            <SkeletonPlaceholder.Item
                                width={180}
                                height={45}
                                borderRadius={15}
                                alignSelf="center"
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder>
                </View>
            </ScrollView>
        );
    };

    const getOpenPositions = () => {
        get('open-positions').then((res: any) => {
            if (res.type == "success") {
                setOpenPositions(res.openPositions);
                setLoadingOpenPositions(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    const getProfile = () => {
        get('users/profile').then((res: any) => {
            if (res.type == "success") {
                setNameLastName(`${res.user.firstName} ${res.user.lastName}`);
                setEmail(res.user.email);
                setDateOfBirth(res.user.dateOfBirth);
                setPhoneNumber(res.user.phoneNumber);
                setCity(res.user.city);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getProfile();
        getOpenPositions();
    }, []);

    const postApplication = () => {
        if (!verify) {
            startToast("Kimlik bilgilerinizi onaylayın!", "error");
            return;
        }
        setButtonLoading(true);
        const resumeLength = resume.length;
        const descriptionLength = description.length;
        const positionLength = position.length;
        const isresumeValid = resumeLength > 1 && resumeLength < 1000;
        const isdescriptionValid = descriptionLength > 1 && descriptionLength < 1000;
        const positionValid = positionLength > 0;
        if (isresumeValid && isdescriptionValid && positionValid) {
            post('apply-position', {
                profession: position,
                resume,
                description
            }).then((res: any) => {
                if (res.type == "success") {
                    startToast("Başvurunuz gönderildi", "success");
                    setTimeout(() => {
                        navigation.pop();
                    }, 2000);
                } else {
                    startToast(res.error, "error");
                    setButtonLoading(false);
                }
            })
        } else {
            let errorMessage = "";
            if (!isresumeValid) {
                errorMessage += "Özgeçmiş 25 ile 256 karakter arasında olmalıdır. ";
            }
            if (!isdescriptionValid) {
                errorMessage += "Açıklama 25 ile 1000 karakter arasında olmalıdır. ";
            }
            if (!positionValid) {
                errorMessage += "Başvuru alanı seçmek zorunludur.";
            }
            startToast(errorMessage, "error");
            setButtonLoading(false);
        }
    }

    return (
        <View
            
            style={styles.main}
        >
            <SafeAreaView style={styles.safeAreaView}>
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>
                {/* HEADER */}
                <HeaderFour
                    headerStatus={header}
                    navigation={navigation}
                    title={"KARİYER"}
                />
                {
                    loading || loadingOpenPositions ? <LoadingSkeleton /> : (
                        <ScrollView
                            onScroll={handleScroll}
                            showsVerticalScrollIndicator={false}
                        >
                            {/* PROFILE HEADER */}
                            <MenuProfileHeader
                                nameLastName={nameLastName}
                                email={email}
                                dateOfBirth={dateOfBirth}
                                phoneNumber={phoneNumber}
                                city={city}
                                setVerify={setVerify}
                            />

                            {/* FORM */}
                            <View style={[styles.form, shadow]}>

                                {/* SELECT BOX - APPLY FOR WHERE? */}
                                <Text style={styles.titles}>Hangi alana başvuruda bulunacaksınız?</Text>

                                <SettingsSelect
                                    variables={openPositions}
                                    setSelected={setPosition}
                                />

                                {/* DESC */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 25 }]}>Hangi alana başvuruda bulunacaksınız?</Text>

                                <SettingsSecondInput
                                    icon={<WriteIcon size={5} color={'transparent'} />}
                                    number={100}
                                    setText={setDescription}
                                />

                                {/* DESC */}
                                <Text style={[styles.titles, { marginTop: 20, marginBottom: 25 }]}>Kendinizden bahsediniz</Text>

                                <SettingsSecondInput
                                    icon={<WriteIcon size={5} color={'transparent'} />}
                                    number={100}
                                    setText={setResume}
                                />

                                {/* BUTTON */}
                                {buttonLoading ? <Spinner color={gray_t4} size={18} mt={5} /> : (
                                    <Button
                                        onPress={() => {
                                            postApplication();
                                        }}
                                        style={styles.button}
                                    >
                                        <Text style={styles.buttonText}>gönder</Text>
                                    </Button>
                                )}
                            </View>
                        </ScrollView>
                    )
                }

            </SafeAreaView>
        </View>
    )
}
export default Career;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1, 
    backgroundColor: kremrengi
},
    safeAreaView: { flexGrow: 1 },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    skeletonContainer: {
        paddingTop: 50, // Added top padding
    },
    skeletonForm: {
        backgroundColor: white, // Changed form background to white
        marginTop: 25,
    },
    form2: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 40
    },
    form: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        paddingBottom: 50,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20,
        marginBottom: 100
    },
    titles: {
        marginLeft: 10,
        fontWeight: 'bold',
        marginBottom: 15
    },
    button: {
        position: 'absolute',
        alignSelf: 'center',
        bottom: -20,
        backgroundColor: green_t1,
        width: 180,
        height: 45,
        borderRadius: 15
    },
    buttonText: {
        fontSize: 18,
        color: white,
        fontFamily: 'MADE TOMMY'
    }
});
import React from "react";
import { StyleSheet, ImageBackground, View, Text, ScrollView } from "react-native";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import { black, gray_t4, gray_t7, green_t1, krem<PERSON><PERSON>, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import MenuProfileHeader from "../../../components/MenuProfileHeader";
import SettingsFirstInput from "../../../components/SettingsFirstInput";
import SettingsSecondInput from "../../../components/SettingsSecondInput";
import { <PERSON><PERSON>, Spinner } from "native-base";
import { shadow } from "../../../constants/Shadow";
import { get, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { Messages } from "../../../components/Svgs";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";


const Contact: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [buttonLoading, setButtonLoading] = React.useState(false);

    // -- PROFILE INFO -- //
    const [nameLastName, setNameLastName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [dateOfBirth, setDateOfBirth] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [city, setCity] = React.useState("");

    // -- CONTACT -- //
    const [topic, setTopic] = React.useState("");
    const [message, setMessage] = React.useState("");
    const [verify, setVerify] = React.useState(false);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const LoadingSkeleton = () => {
        return (
            
            <ScrollView 
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.skeletonContainer}

            >
                <View style={[styles.form2, styles.skeletonForm]}>

                {/* Profile Header Skeleton */}
                <SkeletonPlaceholder 
                    backgroundColor="#F5F5F5" 
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item 
                        flexDirection="row" 
                        padding={20}
                        backgroundColor={white}
                        borderRadius={15}
                        marginHorizontal={15}
                        height={160}
                    >
                        {/* Profile Info */}
                        <SkeletonPlaceholder.Item marginLeft={15} flex={1}>
                            <SkeletonPlaceholder.Item
                                width="80%"
                                height={12}
                                borderRadius={6}
                            />
                            <SkeletonPlaceholder.Item
                                width="60%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                            <SkeletonPlaceholder.Item
                                width="40%"
                                height={12}
                                borderRadius={6}
                                marginTop={12}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
                </View>
    
                {/* Form Skeleton */}
                <View style={[styles.form, styles.skeletonForm]}>
                    <SkeletonPlaceholder 
                        backgroundColor="#F5F5F5" 
                        highlightColor="#FFFFFF"
                        speed={1200}
                    >
                        {/* Subject Input */}
                        <SkeletonPlaceholder.Item>
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={45}
                                borderRadius={15}
                            />
    
                            {/* Message Textarea */}
                            <SkeletonPlaceholder.Item
                                width="100%"
                                height={120}
                                borderRadius={15}
                                marginTop={30}
                            />
    
                            {/* Submit Button */}
                            <SkeletonPlaceholder.Item
                                width={180}
                                height={45}
                                borderRadius={15}
                                alignSelf="center"
                                marginTop={30}
                            />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder>
                </View>
            </ScrollView>
        );
    };

    const getProfile = () => {
        get('users/profile').then((res: any) => {
            if (res.type == "success") {
                setNameLastName(`${res.user.firstName} ${res.user.lastName}`);
                setEmail(res.user.email);
                setDateOfBirth(res.user.dateOfBirth);
                setPhoneNumber(res.user.phoneNumber);
                setCity(res.user.city);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getProfile();
    }, []);

    const postContact = () => {
        if (!verify) {
            startToast("Kimlik bilgilerinizi onaylayın!", "error");
            return;
        }
        setButtonLoading(true);
        const topicLength = topic.length;
        const messageLength = message.length;
        const isTopicValid = topicLength > 1 && topicLength < 256;
        const isMessageValid = messageLength > 1 && messageLength < 1000;
        if (isTopicValid && isMessageValid) {
            post('contact', {
                topic,
                message
            }).then((res: any) => {
                if (res.type == "success") {
                    startToast("Mesajınız gönderildi", "success");
                    setTimeout(() => {
                        navigation.pop();
                    }, 2000);
                } else {
                    startToast(res.error, "error");
                    setButtonLoading(false);
                }
            })
        } else {
            let errorMessage = "";
            if (!isTopicValid) {
                errorMessage += "Konu 8 ile 256 karakter arasında olmalıdır. ";
            }
            if (!isMessageValid) {
                errorMessage += "Mesaj 25 ile 1000 karakter arasında olmalıdır.";
            }
            startToast(errorMessage, "error");
            setButtonLoading(false);
        }
    }

    return (
        <View
            
            style={styles.main}
        >
            <SafeAreaView style={styles.safeAreaView}>
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>
                {/* HEADER */}
                <HeaderFour
                    headerStatus={header}
                    navigation={navigation}
                    title={"İLETİŞİM"}
                />
                {
                    loading ? <LoadingSkeleton /> : (
                        <ScrollView
                            onScroll={handleScroll}
                            showsVerticalScrollIndicator={false}
                        >

                            {/* PROFILE HEADER */}
                            <MenuProfileHeader
                                nameLastName={nameLastName}
                                email={email}
                                dateOfBirth={dateOfBirth}
                                phoneNumber={phoneNumber}
                                city={city}
                                setVerify={setVerify}
                            />

                            {/* FORM */}
                            <View style={[styles.form, shadow]}>

                                {/* SUBJECT */}
                                <SettingsFirstInput
                                    placeHolder="Konu"
                                    setText={setTopic}
                                />

                                <View style={styles.desc}>
                                    {/* DESC */}
                                    <SettingsSecondInput
                                        // icon={require('../../../assets/menu/message.png')}
                                        icon={<Messages size={5} color={black} />}
                                        setText={setMessage}
                                        number={100}
                                    />
                                </View>

                                {/* BUTTON */}
                                {buttonLoading ? <Spinner color={gray_t4} size={18} mt={5} /> : (
                                    <Button
                                        onPress={() => {
                                            postContact();
                                        }}
                                        style={styles.button}
                                    >
                                        <Text style={styles.buttonText}>gönder</Text>
                                    </Button>
                                )}
                            </View>
                        </ScrollView>
                    )
                }

            </SafeAreaView>
        </View>
    )
}
export default Contact;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi
 },
    safeAreaView: { flexGrow: 1 },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    skeletonContainer: {
        paddingTop: 50, // Added top padding
    },
    skeletonForm: {
        backgroundColor: white, // Changed form background to white
        marginTop: 25,
    },
    form: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        paddingBottom: 50,
        marginBottom: 100,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20
    },
    form2: {
        backgroundColor: gray_t7,
        borderRadius: 15,
        padding: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 40
    },
    desc: { marginTop: 30 },
    button: {
        position: 'absolute',
        alignSelf: 'center',
        bottom: -20,
        backgroundColor: green_t1,
        width: 180,
        height: 45,
        borderRadius: 15
    },
    buttonText: {
        fontSize: 18,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
});
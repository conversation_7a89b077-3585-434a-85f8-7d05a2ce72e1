import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../components/HeaderFour";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { black, blue_t1, green_t1, kremrengi, pink, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import LottieView from 'lottie-react-native';
import { get } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import CreditCard from "../../../components/CreditCard";

const LoadingSkeleton = () => {
    return (
        <View>
            <ScrollView contentContainerStyle={styles.scrollContainer}>
                {/* Gift Cards */}
                <SkeletonPlaceholder
                    backgroundColor="#E8E8E8"
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    {/* Render multiple card placeholders */}
                    {[1, 2, 3].map((_, index) => (
                        <SkeletonPlaceholder.Item
                            key={index}
                            style={[
                                styles.cardContainer,
                                { marginTop: index === 0 ? 70 : 20 }
                            ]}
                        >
                            {/* Main Card */}
                            <SkeletonPlaceholder.Item
                                width={Layout.screen.width / 1.15}
                                height={206}
                                borderRadius={15}
                                alignSelf="center"
                            />

                            {/* Card Details Section */}
                            <SkeletonPlaceholder.Item
                                flexDirection="row"
                                justifyContent="space-between"
                                alignItems="center"
                                marginTop={10}
                                paddingHorizontal={20}
                            >
                                {/* Left side details */}
                                <SkeletonPlaceholder.Item>
                                    <SkeletonPlaceholder.Item
                                        width={100}
                                        height={16}
                                        borderRadius={4}
                                    />
                                    <SkeletonPlaceholder.Item
                                        width={80}
                                        height={14}
                                        borderRadius={4}
                                        marginTop={8}
                                    />
                                </SkeletonPlaceholder.Item>

                                {/* Radio button placeholder */}
                                <SkeletonPlaceholder.Item
                                    width={24}
                                    height={24}
                                    borderRadius={12}
                                />
                            </SkeletonPlaceholder.Item>
                        </SkeletonPlaceholder.Item>
                    ))}
                </SkeletonPlaceholder>
            </ScrollView>

            {/* Bottom Buttons Section */}
            <View style={styles.bottomSection}>
                <SkeletonPlaceholder
                    backgroundColor="#E8E8E8"
                    highlightColor="#FFFFFF"
                    speed={1200}
                >
                    <SkeletonPlaceholder.Item style={styles.bottomButtonsContainer}>
                        {/* Top buttons row */}
                        <SkeletonPlaceholder.Item
                            flexDirection="row"
                            justifyContent="space-between"
                        >
                            <SkeletonPlaceholder.Item
                                width={Layout.screen.width / 2.3}
                                height={38}
                                borderRadius={15}
                            />
                            <SkeletonPlaceholder.Item
                                width={Layout.screen.width / 2.3}
                                height={38}
                                borderRadius={15}
                            />
                        </SkeletonPlaceholder.Item>

                        {/* Or divider */}
                        <SkeletonPlaceholder.Item
                            flexDirection="row"
                            alignItems="center"
                            justifyContent="center"
                            marginVertical={20}
                        >
                            <SkeletonPlaceholder.Item
                                width={Layout.screen.width / 2.6}
                                height={1}
                            />
                            <SkeletonPlaceholder.Item
                                width={30}
                                height={18}
                                borderRadius={4}
                                marginHorizontal={10}
                            />
                            <SkeletonPlaceholder.Item
                                width={Layout.screen.width / 2.6}
                                height={1}
                            />
                        </SkeletonPlaceholder.Item>

                        {/* Bottom button */}
                        <SkeletonPlaceholder.Item
                            width={Layout.screen.width / 2.3}
                            height={40}
                            borderRadius={15}
                            alignSelf="center"
                        />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder>
            </View>
        </View>
    );
};

const GiftCard: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);



    // -- DEFINE CARD MODAL -- //
    const [showDefineModal, setShowDefineModal] = React.useState(false);

    // -- GIFT CARD -- //
    const [giftCards, setGiftCards] = React.useState([]);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const getGiftCards = () => {
        get('users/gift-cards').then((res: any) => {
            if (res.type == "success") {
                setGiftCards(res.giftCards);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getGiftCards();
    }, []);



    return (
        <View
            style={styles.main}
        >





            <SafeAreaView style={styles.safeAreaView}>
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={green_t1}
                    />
                </View>
                {/* HEADER */}
                <HeaderFour
                    headerStatus={header}
                    navigation={navigation}
                    title="GIFT CARD"
                />

                {
                    loading ?  <LoadingSkeleton /> : (
                        <>
                            {
                                giftCards.length < 1 ?
                                <View style={styles.gifcardare}>
                                <Text style={{
                                    fontFamily: 'MADE TOMMY',
                                    color: black,
                                    fontSize: 16,
                                    margin: 20,
                                    marginTop: 70,
                                    textAlign: 'center'
                                }}>Pay kartınız bulunmamaktadır. HollyStone şubelerimizden temin edebilirsiniz!</Text>

                                </View>


                                    :
                                    <ScrollView onScroll={handleScroll} >
                                        {/* CREDIT CARD ITEMS */
                                            giftCards.map((item: any, index: number) => {
                                                return (
                                                    <View
                                                        key={index}
                                                        style={{
                                                            marginTop: index == 0 ? 70 : 0,
                                                            marginBottom: index == giftCards.length - 1 ? 200 : 0,
                                                            alignItems: 'center'
                                                        }}
                                                    >
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                // @ts-ignore
                                                                navigation.navigate("CardDetails", {
                                                                    cardNumber: item.code,
                                                                    cardType: item.type
                                                                });
                                                            }}
                                                        >
                                                            <CreditCard
                                                                cardNumber={item.code}
                                                                type={item.type}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>
                                                )
                                            })
                                        }
                                    </ScrollView>
                            }

                            {/* Gift Kartı Tanımla ve Ödeme Yap Butonları */}
                            <View style={styles.defineCardButtonContainer}>
                                <View style={styles.buttonRow}>
                                    <TouchableOpacity
                                        style={styles.defineCardButton}
                                        onPress={() => setShowDefineModal(true)}
                                    >
                                        <Text style={styles.defineCardButtonText}>Pay Kart Tanımla</Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={styles.paymentButton}
                                        onPress={() => {
                                            navigation.navigate("PayMoneyWithCard");
                                        }}
                                    >
                                        <Text style={styles.paymentButtonText}>Ödeme Yap</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>

                            {/* Gift Kartı Tanımlama Modalı */}
                            <Modal
                                visible={showDefineModal}
                                transparent={true}
                                animationType="slide"
                                onRequestClose={() => setShowDefineModal(false)}
                            >
                                <View style={styles.defineModalContainer}>
                                    <View style={styles.defineModalContent}>
                                        <Text style={styles.defineModalTitle}>Pay Kart Tanımlama</Text>

                                        <View style={styles.lottieContainer}>
                                            <LottieView
                                                source={require('../../../assets/animations/card-scan.json')}
                                                autoPlay
                                                loop
                                                style={styles.lottieAnimation}
                                            />
                                        </View>

                                        <Text style={styles.defineModalDescription}>
                                            Pay kartınızı tanımlamak için kartı telefonunuzun arka kısmına yaklaştırınız.
                                        </Text>

                                        <TouchableOpacity
                                            style={styles.closeModalButton}
                                            onPress={() => setShowDefineModal(false)}
                                        >
                                            <Text style={styles.closeModalButtonText}>Kapat</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </Modal>



                        </>
                    )
                }

            </SafeAreaView>
        </View >
    )
}
export default GiftCard;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },


    safeAreaView: { flexGrow: 1 },

    gifcardare: {
        alignContent: 'center',
        alignItems: 'center',
    },
    scrollContainer: {
        paddingBottom: 200, // Space for bottom buttons

    },
    cardContainer: {
        marginBottom: 20,
        padding: 10,
    },
    bottomSection: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: kremrengi,
        paddingBottom: 20,
    },
    bottomButtonsContainer: {
        padding: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
    },
    // Gift Kartı Tanımla Butonu Stilleri
    defineCardButtonContainer: {
        position: 'absolute',
        bottom: 20,
        width: Layout.screen.width,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '90%',
    },
    defineCardButton: {
        backgroundColor: blue_t1,
        paddingVertical: 15,
        paddingHorizontal: 20,
        borderRadius: 15,
        flex: 1,
        marginRight: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    defineCardButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    paymentButton: {
        backgroundColor: pink,
        paddingVertical: 15,
        paddingHorizontal: 20,
        borderRadius: 15,
        flex: 1,
        marginLeft: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    paymentButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    // Modal Stilleri
    defineModalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    defineModalContent: {
        backgroundColor: white,
        borderRadius: 20,
        padding: 20,
        width: Layout.screen.width * 0.85,
        alignItems: 'center',
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    defineModalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: black,
        marginBottom: 20,
        textAlign: 'center',
    },
    lottieContainer: {
        width: 200,
        height: 200,
        marginVertical: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    lottieAnimation: {
        width: '100%',
        height: '100%',
    },
    defineModalDescription: {
        fontSize: 16,
        color: black,
        textAlign: 'center',
        marginBottom: 20,
    },
    closeModalButton: {
        backgroundColor: blue_t1,
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 10,
        marginTop: 10,
    },
    closeModalButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
    },
});
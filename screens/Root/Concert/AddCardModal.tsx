import React, { useState } from "react";
import {
    View,
    Text,
    Modal,
    TouchableOpacity,
    StyleSheet
} from "react-native";
import { CreditCardInput, LiteCreditCardInput  } from "react-native-credit-card-input";
import { black, white, green_t2, gray_t4 } from "../../../constants/Color";

const AddCardModal = ({ visible, onClose, onAddCard }) => {
    const [cardData, setCardData] = useState(null);

    const handleCardInput = (form) => {
        setCardData(form);
    };

    const handleAddCard = () => {
        if (!cardData || !cardData.valid) {
            alert("Lütfen geçerli bir kart bilgisi girin.");
            return;
        }
    
        const { number, expiry, cvc, name, type } = cardData.values;
        const [expiryMonth, expiryYear] = expiry.split("/");
    
        const newCard = {
            id: `local-${Date.now()}`,
            c_name: name || "Yeni Kart",
            last_4: number.slice(-4),
            card_number: number,
            expiry,
            expiryMonth,
            expiryYear,
            cvc,
            schema: type.toUpperCase(),
            isLocal: true,
        };
    
        onAddCard(newCard);
        onClose(); // Modalı kapat
    };
    

    return (
        <Modal
            visible={visible}
            transparent
            animationType="slide"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContainer}>
                    <Text style={styles.modalTitle}>Kart Ekle</Text>

                    <LiteCreditCardInput
                        onChange={handleCardInput}
                    />

                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                            <Text style={styles.cancelButtonText}>İptal</Text>
                        </TouchableOpacity>

                        <TouchableOpacity style={styles.addButton} onPress={handleAddCard}>
                            <Text style={styles.addButtonText}>Kartı Ekle</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
    },
    modalContainer: {
        width: "90%",
        padding: 20,
        backgroundColor: white,
        borderRadius: 10,
        alignItems: "center",
    },
    modalTitle: {
        fontSize: 22,
        fontWeight: "bold",
        textAlign: "center",
        marginBottom: 20,
    },
    label: {
        fontSize: 14,
        color: gray_t4,
        marginBottom: 5,
    },
    input: {
        fontSize: 16,
        color: black,
    },
    buttonContainer: {
        flexDirection: "row",
        marginTop: 20,
    },
    cancelButton: {
        flex: 1,
        backgroundColor: gray_t4,
        padding: 15,
        borderRadius: 8,
        alignItems: "center",
        marginRight: 10,
    },
    cancelButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: "bold",
    },
    addButton: {
        flex: 1,
        backgroundColor: green_t2,
        padding: 15,
        borderRadius: 8,
        alignItems: "center",
    },
    addButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: "bold",
    },
});

export default AddCardModal;

import { useNavigation } from "@react-navigation/native";
import React from "react";
import { 
  FlatList, 
  Image, 
  ImageBackground, 
  StyleSheet, 
  Text, 
  View, 
  Dimensions 
} from "react-native";
import { screens } from "../../../navigation";
import NotifyView from "../../../components/NotifyView";
import Menu from "../../../components/Menu";
import Swiper from "react-native-swiper";
import HeaderFive from "../../../components/HeaderFive";
import { black_t2, gray_t15, green_t1, green_t2, kremrengi, white } from "../../../constants/Color";
import { getImageURL, post } from "../../../networking/Server";
import ConcertItem from "../../../components/ConcertItem";
import { MainStore } from "../../../stores/MainStore";
import BottomBar from "../../../components/BottomBar";
import Svg, { Path } from 'react-native-svg';
import { AnnouncementStore } from "../../../stores/AnnouncementStore";


const { width, height } = Dimensions.get('window');

const Concerts: React.FC = (props: any) => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    const [conDai, setConDai] = React.useState(props?.route?.params?.conDai);
    const [conDai_] = React.useState(props?.route?.params?.conDai);

    const [announcements, setAnnouncements] = React.useState(props?.route?.params?.announcements || []);
    const [annLoading, setAnnLoading] = React.useState(false);

    const onSearch = (search: string) => {
        if (search) {
            const search_ = search.toLowerCase();
            let filterConDai = conDai_.filter((item: any) => {
                return (
                    (item.name.toLowerCase()).match(search_)
                );
            })
            setConDai(filterConDai);
        } else {
            setConDai(conDai_);
        }
    }


    const filteredAnnouncements = AnnouncementStore.announcements.filter(a => a.type === 1);



    return (
        <View style={styles.main}>
          {/* MENU */}
            <Menu
                menuColor={green_t1}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />

            {/* ÜST SWIPER ALANI */}
      <View style={styles.swiperMainView}>
      <HeaderFive
                    searchIconColor={green_t2}
                    logoStyle={false}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menu.png')}
                    backIcon={white}
                    logo={false}
                    navigation={navigation}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    onSearch={onSearch}
                    menuStatus={menu}
                />
        
        {annLoading ? (
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        ) : (
          <Swiper
            style={styles.swiper}
            dotStyle={styles.dotStyle}
            activeDotStyle={styles.activeDotStyle}
            dotColor={white}
            activeDotColor={green_t1}
          >
            {filteredAnnouncements.map((announcement: any, index: React.Key) => (
              <ImageBackground
                key={index}
                source={{ uri: getImageURL(announcement?.image) }}
                style={styles.topBack}
                resizeMode="cover"
              >
<NotifyView
                                    navigation={navigation}
                                    content={announcement?.content}
                                    header={announcement?.header}
                                    detail={announcement?.detail}
                                    detailId={announcement?.detailId}
                                    bottomDesc={MainStore.language.upcoming_concerts}
                                />
              </ImageBackground>
            ))}
          </Swiper>
        )}
        
        {/* SVG AYRIM ÇİZGİSİ */}
        <View style={styles.svgContainer}>
          <Svg width={width} height={60} viewBox="0 0 370 50">
            <Path
              fill="#F4F4F4"
              d="M0 41H101.5H172.618C177.444 41 182.175 39.6568 186.281 37.1207L197 30.5L214.5 19.5L234.555 7.64904C243.03 2.64155 252.692 0 262.536 0H318.5H390V557H0V41Z"
            />
          </Svg>
        </View>
      </View>

      {/* ANA İÇERİK ALANI */}
      <View style={styles.contentContainer}>
        <Image
          style={styles.hollyTicketLogo}
          resizeMode="contain"
          source={require('../../../assets/root/hollyticket.png')}
        />
        
        <View style={styles.concertListContainer}>
          {conDai && conDai.length > 0 ? (
            <FlatList
              data={conDai}
              numColumns={2}
              showsVerticalScrollIndicator={false} // Bu satırı ekliyoruz
              columnWrapperStyle={{ marginBottom: 10 }} // Satırlar arasına boşluk
              ListFooterComponent={<View style={{ height: 80 }} />} // Listenin en altında boşluk

              renderItem={({ item, index }) => (
                <ConcertItem
                  key={index}
                  item={item}
                  navigation={navigation}
                />
              )}
              ListEmptyComponent={
                <View style={styles.noActivityContainer}>
                  <Text style={styles.noActivityText}>Aktif etkinlik bulunmadı</Text>
                </View>
              }
            />
          ) : (
            <View style={styles.noActivityContainer}>
              <Text style={styles.noActivityText}>Aktif etkinlik bulunmadı</Text>
            </View>
          )}
        </View>
      </View>

      <BottomBar navigation={navigation} type={1} />
    </View>
  )
}
// OPTİMİZE EDİLMİŞ STİLLER
const styles = StyleSheet.create({
    main: { 
      flex: 1, 
      backgroundColor: kremrengi 
    },
    swiperMainView: {
      height: height * 0.4, // Dinamik yükseklik
      backgroundColor: gray_t15,
    },
    swiper: {
      flex: 1,
    },
    topBack: {
      width: '100%',
      height: '100%',
    },
    svgContainer: {
      position: 'absolute',
      bottom: -25,
      left:2,
      width: '100%',
    },
    contentContainer: {
      flex: 1,
      marginTop: -30,
      paddingHorizontal: 15,
    },
    hollyTicketLogo: {
      alignSelf: 'flex-end',
      marginTop: 10,
      height: 22,
      width: 122,
    },
    concertListContainer: {
      flex: 1,
      marginTop: 15,
    },
    columnWrapper: {
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    concertItem: {
      width: (width - 40) / 2, // Dinamik genişlik
    },
    noActivityContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noActivityText: {
      fontSize: 16,
      fontFamily: 'MADE TOMMY',
      color: gray_t15,
    },
    dotStyle: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
      bottom: 20,
    },
    activeDotStyle: {
      width: 24,
      height: 8,
      borderRadius: 4,
      bottom: 20,
    },
    loadingText: {
      textAlign: 'center',
      marginTop: 150,
      color: black_t2,
      fontFamily: 'MADE TOMMY',
    }
  });
  
  export default Concerts;
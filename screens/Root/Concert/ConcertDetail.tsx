import React, { useRef, useState } from "react";
import { StyleSheet, View, ImageBackground, Image, TouchableOpacity, ScrollView, Text, Modal, ActivityIndicator } from "react-native";
import Layout from "../../../constants/Layout";
import Menu from "../../../components/Menu";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderTwo from "../../../components/HeaderTwo";
import { shadow } from "../../../constants/Shadow";
import { black, blue_t2, green_t1, green_t2, white, yellow_t1, gray_t4, kremrengi, black_t2, pink } from "../../../constants/Color";
import { get, post, getImageURL } from "../../../networking/Server";
import ConcertList from "../../../components/ConcertList";
import { Button } from "native-base";
import Toast from "../../../components/Toast";
import dayjs from "dayjs";
import WebView from "react-native-webview";
import { MainStore } from "../../../stores/MainStore";
import AddCardModal from "./AddCardModal";

const ConcertDetail: React.FC = (props: any) => {

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- STATUS -- //
    const [status, setStatus] = React.useState(0);

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(false);

    // STATUSSES -- //
    const [pay, setPay] = React.useState(false); // 1
    const [basket, setBasket] = React.useState(false); // 2
    const [hollyTicket, setHollyTicket] = React.useState(false); // 3

    const [concert, setConcert]: any = React.useState({})
    const [tickets, setTickets]: any = React.useState([])
    const [selectedTicket, setSelectedTicket]: any = React.useState({})
    const [ticketQuantity, setTicketQuantity]: any = React.useState(1)
    const [cards, setCards]: any = React.useState([])
    const [selectedCard, setSelectedCard]: any = React.useState({})
    const [basketItems, setBasketItems]: any = React.useState([])
    const [paytrLink, setPaytrLink] = React.useState("");
    const webview: any = React.useRef(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [localCards, setLocalCards]: any = React.useState([]);
    const [isWalletModalVisible, setIsWalletModalVisible] = useState(false);
    const [isWalletPaymentLoading, setIsWalletPaymentLoading] = useState(false);


    const getConcert = () => {
        setLoading(true);
        try {
            get(`concerts/detail?id=${props.route.params.concertId}`).then((res: any) => {
                if (res.type == "success") {
                    setLoading(false);
                    setConcert(res.concert);
                    setTickets(res.tickets);
                    setSelectedTicket(res.tickets[0]);
                } else {
                    setTimeout(() => {
                        navigation.pop()
                    }, 2000);
                    startToast(res.error, "error");
                }
            })
        } catch (e) {
            startToast(MainStore.language.went_wrong, "error");
            setTimeout(() => {
                navigation.pop()
            }, 2000)
        }
    };

    const getCards = () => {
        try {
            get("payment/card-list").then((res: any) => {
                if (res.type == "success") {
                    setCards(res.list);
                    setSelectedCard(res.list[0]);
                } else {
                    startToast(MainStore.language.went_wrong, "error");
                    navigation.pop();
                }
            })
        } catch (e) {
            startToast(MainStore.language.went_wrong, "error");
            navigation.pop();
        }
    }

    const addNewCard = (card) => {
        setLocalCards([...localCards, card]);
        setSelectedCard(card);
    };


    const allCards = [...cards, ...localCards]; // Sunucu + Yerel kartları birleştir


    const fastPay = () => {
        if (!selectedCard || !selectedCard.isLocal) {
            startToast("Lütfen bir kart seçin.", "error");
            return;
          }

        if (selectedCard.isLocal) {
            // Yerel kart ile işlem yap (Sunucuya gitmeyecek)
            startToast("Ödeme Başarılı (Yerel Kart)", "success");
        } else {
            // Sunucuya ödeme isteği gönder
            post("concerts/fast-pay", {
                ticketId: selectedTicket?.id,
                quantity: selectedTicket?.quantity,
                ctoken: selectedCard?.ctoken, // Sunucudan gelen kartın token'ı
            }).then((res: any) => {
                if (res.type === "success") {
                    startToast("Ödeme Başarılı", "success");
                } else {
                    startToast(res.error || "Ödeme Başarısız", "error");
                }
            }).catch((err) => {
                startToast("Ödeme sırasında hata oluştu", "error");
                console.error(err);
            });
        }
    };

    useFocusEffect(
        React.useCallback(() => {
          getCards(); // Kartları güncelle
          if (tickets.length > 0 && !selectedTicket) {
            // Eğer biletler yüklendiyse ve selectedTicket null ise ilk bileti seç
            setSelectedTicket(tickets[0]);
            setTicketQuantity(1); // Miktarı sıfırla
          }
          if (pay && selectedCard && selectedCard.isLocal && selectedTicket) {
            // Ödeme ekranı açıksa ve gerekli veriler varsa fastPayLocal'ı çalıştır
            fastPayLocal();
          }
        }, [pay, selectedCard, selectedTicket, tickets]) // Bağımlılıklar güncellendi
      );


    const fastPayLocal = () => {
        if (!selectedCard || !selectedCard.isLocal) {
            startToast("Ödeme Başarılı (Yerel Kart)", "success");
            return;
          }

        const requestData = {
          ticketId: selectedTicket?.id,
          quantity: selectedTicket?.quantity,
          cardNumber: selectedCard.card_number,
          expiryMonth: selectedCard.expiryMonth,
          expiryYear: selectedCard.expiryYear,
          cvv: selectedCard.cvc,
        };

        post("concerts/fast-pay-local", requestData)
          .then((res: any) => {
            if (res.type === "success") {
              setPaytrLink(res.paymentLink);
            } else if (res.type === "error" && res.error === "Adres bulunamadı!") {
              // Adres bulunamadı hatası gelirse AddAddress sayfasına yönlendir
              startToast("Adres bulunamadı, lütfen adres ekleyin", "error");
              setTimeout(() => {
                navigation.navigate("AddAddress"); // AddAddress sayfasına yönlendirme
              }, 1500);
            } else {
              startToast(res.error || "Ödeme hatası", "error");
            }
          })
          .catch((err) => {
            console.error(err);
            startToast("Ödeme sırasında hata oluştu.", "error");
          });
      };

    const payWithWallet = () => {
        // Yükleniyor durumunu aktifleştir
        setIsWalletPaymentLoading(true);

        // Cüzdan ile ödeme işlemi
        const requestData = {
          concertId: concert?.id,
          amount: selectedTicket?.price * selectedTicket?.quantity
        };

        post("payment/wallet-pay", requestData)
          .then((res: any) => {
            // Yükleniyor durumunu kapat
            setIsWalletPaymentLoading(false);

            if (res.type === "success") {
              startToast("Ödeme başarıyla tamamlandı", "success");
              setIsWalletModalVisible(false);
              setTimeout(() => {
                navigation.navigate("Profile"); // Profil sayfasına yönlendirme
              }, 2000);
            } else {
              startToast(res.error || "Ödeme hatası", "error");
            }
          })
          .catch((err) => {
            // Hata durumunda da yükleniyor durumunu kapat
            setIsWalletPaymentLoading(false);
            console.error(err);
            startToast("Ödeme sırasında hata oluştu.", "error");
          });
    };


    // -- WEBVIEW PAYTR -- //
    const handleWebViewNavigationStateChange = (params: { url: any; }) => {
        const { url } = params;
        if (url?.includes('success')) {
            // webview.current.stopLoading();
            // webview.current = null;
            setPaytrLink("");
            startToast(MainStore.language.success_pay, "success")
            setTimeout(() => {
                navigation.navigate("Profile"); // Profil sayfasına yönlendirme
            }, 2000);
        } else if (url?.includes('fail')) {
            startToast(MainStore.language.denied_pay, "error")
            setPaytrLink("");
        }
    }

    React.useEffect(() => {
        getConcert();
    }, []);


    // -- CONCERT INFO DETAIL -- //
    const concertInfos = [
        {
            id: 1,
            rightImg: require('../../../assets/root/concertInfosBlueBack.png'),
            logo: {
                type: 1,
                logoAlt: MainStore.language.payment,
                style: { top: 41 }
            },
            backgroundColor: "#4B70CF",
            children: <View style={styles.concertInfoFirstMain}>
                <Text style={styles.concertInfoFirstTitle}>{MainStore.language.choose_card}</Text>
                {
    allCards.map((item: any, index: React.Key) => (
        <TouchableOpacity
            key={index}
            onPress={() => setSelectedCard(item)}
            style={[
                styles.concertInfoCardView,
                { borderColor: green_t2, borderWidth: selectedCard?.id == item.id ? 1 : 0 },
            ]}
        >
            <View>
                <Text style={styles.concertInfoCardName}>{item.c_name}</Text>
                <Text style={styles.concertInfoCardNo}>**** **** **** {item.last_4}</Text>
            </View>
            <Image
                source={
                    item?.schema === "MASTERCARD"
                        ? require('../../../assets/exp/masterCard.png')
                        : item?.schema === "VISA"
                        ? require('../../../assets/exp/visa.png')
                        : require('../../../assets/exp/cardIcon.png')
                }
                style={styles.concertInfoCardLogo}
                resizeMode="contain"
            />
        </TouchableOpacity>
    ))
}


<View style={styles.concertInfoFirstButtonsView}>
    {selectedCard?.isLocal ? (
        <TouchableOpacity style={styles.payButton} onPress={() => fastPayLocal()}>
            <Text style={styles.payButtonText}>Öde</Text>
        </TouchableOpacity>
    ) : (
        <TouchableOpacity style={styles.payButton} onPress={() => fastPay()}>
            <Text style={styles.payButtonText}>Öde</Text>
        </TouchableOpacity>
    )}

    <TouchableOpacity style={styles.addCardButton} onPress={() => setIsModalVisible(true)}>
        <Text style={styles.addCardButtonText}>Kart Ekle</Text>
    </TouchableOpacity>
</View>






            </View>
        },
        {
            id: 2,
            rightImg: require('../../../assets/root/concertInfosYellowBack.png'),
            logo: {
                type: 1,
                logoAlt: MainStore.language.basket,
                style: {}
            },
            backgroundColor: yellow_t1,
            children: <View style={styles.concertInfoSecondMain}>

                {/* LIST OF BASKET */}
                <View style={styles.concertInfoSecondAlt}>
                    <View style={styles.concertInfoSecondListView}>
                        <Text style={styles.concertInfoSecondListLeft}>{selectedTicket?.title}</Text>
                        <Text style={styles.concertInfoSecondListRight}>x {selectedTicket?.quantity}</Text>
                    </View>
                    <View style={styles.concertInfoSecondGrayWhiteLine} />
                    <View style={styles.concertInfoSecondListView}>
                        <Text style={styles.concertInfoSecondListLeft}>{MainStore.language.amount}</Text>
                        <Text style={styles.concertInfoSecondListRight}>{`${(selectedTicket?.price * selectedTicket?.quantity).toFixed(2)} ₺`}</Text>
                    </View>
                </View>

                {/* BUTTONS OF BASKET */}
                <View style={styles.concertInfoSecondButtonsView}>
                    <TouchableOpacity
                        onPress={() => {
                            setStatus(3);
                            setPay(true);
                        }}
                        style={styles.concertInfoSecondButton}
                    >
                        <Text style={styles.concertInfoSecondButtonText}>Kredi Kartı</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={() => {
                            setIsWalletModalVisible(true);
                        }}
                        style={[styles.concertInfoSecondButton, { marginTop: 10, backgroundColor: pink, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }]}
                    >
                        <Image
                            source={require('../../../assets/root/cuzdanwhite.png')}
                            style={styles.walletIcon}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                </View>
            </View>
        },
        {
            id: 3,
            rightImg: require('../../../assets/root/concertInfosWhiteBack-new.png'),
            logo: {
                type: 2,
                logoAlt: require('../../../assets/root/hollyticket.png'),
                style: { top: 45, left:-10 }
            },
            backgroundColor: white,
            children: <View style={styles.concertInfoThirdMain}>

                {/* TICKET TYPE AND BUTTONS */}
                {
                    tickets.length > 0 ? (
                        <>
                            <Text style={styles.concertInfoThirdTitle}>{MainStore.language.ticket_type}</Text>
                            {tickets.map((item: any, index: React.Key) => (
                                <TouchableOpacity
                                    onPress={() => {
                                        setSelectedTicket(item);
                                    }}
                                    key={index}
                                    style={[
                                        styles.concertInfoThirdTopButton,
                                        {
                                            borderColor: green_t2,
                                            borderWidth: selectedTicket?.id === item.id ? 1 : 0,
                                        },
                                    ]}
                                >
                                    <Text style={styles.concertInfoThirdTopButtonText}>{item?.title}</Text>
                                </TouchableOpacity>
                            ))}
                            {(() => {
                                const remaining_quota = selectedTicket?.remaining_quota ?? Infinity;
                                return remaining_quota > 0 ? (
                                    <>
                                        {/* PIECE OF TICKETS */}
                                        <Text style={styles.concertInfoThirdTitle}>{MainStore.language.piece}</Text>
                                        <View style={styles.concertInfoThirdPieceView}>
                                            <TouchableOpacity
                                                onPress={() => {
                                                    if (ticketQuantity > 1) setTicketQuantity(ticketQuantity - 1);
                                                }}
                                                style={styles.concertInfoThirdPieceAltView}
                                            >
                                                <Text style={styles.concertInfoThirdPieceAltViewText}>-</Text>
                                            </TouchableOpacity>
                                            <Text style={styles.concertInfoThirdPiece}>{ticketQuantity}</Text>
                                            <TouchableOpacity
                                                onPress={() => {
                                                    const remaining_quota = selectedTicket?.remaining_quota ?? Infinity;
                                                    if (ticketQuantity < remaining_quota)
                                                        setTicketQuantity(ticketQuantity + 1);
                                                }}
                                                style={styles.concertInfoThirdPieceAltView}
                                            >
                                                <Text style={styles.concertInfoThirdPieceAltViewText}>+</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <Text style={styles.concertInfoThirdTitle}>{MainStore.language.price_big}</Text>
                                        <Text style={styles.concertInfoThirdPrice}>{`${(selectedTicket?.price * ticketQuantity).toFixed(2)}₺`}</Text>
                                        {/* NEXT BUTTON */}
                                        <TouchableOpacity
                                            onPress={() => {
                                                let basketItem = selectedTicket;
                                                basketItem.quantity = ticketQuantity;
                                                setBasket(true);
                                                setBasketItems([...basketItems, basketItem]);
                                                setStatus(2);
                                            }}
                                            style={styles.concertInfoThirdButton}
                                        >
                                            <Text style={styles.concertInfoThirdButtonText}>{MainStore.language.go_on}</Text>
                                        </TouchableOpacity>
                                    </>
                                ) : (
                                    <Text style={[styles.altText, { marginTop: 150 }]}>{MainStore.language.sold_out}</Text>
                                );
                            })()}
                        </>
                    ) : (
                        <Text style={[styles.altText, { marginTop: 150 }]}>{MainStore.language.sold_out}</Text>
                    )
                }

            </View>
        },
    ];

    if (paytrLink) {

        return (
            <WebView
                ref={webview}
                style={styles.containerPayTr}
                source={{ uri: paytrLink }}
                onNavigationStateChange={handleWebViewNavigationStateChange}
            />
        )
    }

    return (
        <View
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t2}
                />
            </View>

            <AddCardModal visible={isModalVisible} onClose={() => setIsModalVisible(false)} onAddCard={addNewCard} />

            {/* Cüzdan Ödeme Modalı */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={isWalletModalVisible}
                onRequestClose={() => setIsWalletModalVisible(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Cüzdan ile Ödeme</Text>

                        <Text style={styles.modalText}>
                            Cüzdanınızdan çekilecek tutar:
                        </Text>

                        <Text style={styles.modalAmount}>
                            {selectedTicket?.price * selectedTicket?.quantity} ₺
                        </Text>

                        <View style={styles.modalButtonContainer}>
                            <TouchableOpacity
                                style={[styles.modalButton, { backgroundColor: green_t1 }]}
                                onPress={payWithWallet}
                                disabled={isWalletPaymentLoading}
                            >
                                {isWalletPaymentLoading ? (
                                    <ActivityIndicator color={white} size="small" />
                                ) : (
                                    <Text style={styles.modalButtonText}>Onayla</Text>
                                )}
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[styles.modalButton, { backgroundColor: "#dc1354" }]}
                                onPress={() => setIsWalletModalVisible(false)}
                            >
                                <Text style={styles.modalButtonText}>İptal</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>


             {
                loading ?
                <View style={styles.loadingContainer}>
                <Image source={require('../../../assets/root/hollyticketgif.gif') } style={styles.loadingGif} />
                </View>
                    :
                    <>

            {/* MENU */}
            <Menu
                menuColor={green_t1}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
            <SafeAreaView style={styles.safeAreaView}>
                {/* HEADER */}
                <HeaderTwo
                    onMenu={(set: boolean) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                    navigation={navigation}
                    leftIcon={black}
                    logo={false}
                    menuIcon={require('../../../assets/header/menu.png')}
                />
            </SafeAreaView>


                    <ScrollView showsVerticalScrollIndicator={false}>

                        {/* STABIL */}
                        <View style={[shadow, { zIndex: 2 }]}>
                            <Image
                                resizeMode="cover"
                                style={styles.concertImg}
                                source={{ uri: getImageURL(concert?.image) }}
                            />
                        </View>

                        {/* ANIMATION CIRCLE */}
                        <Image
                            source={
                                status == 0 ?
                                    require('../../../assets/root/concertPro1.png')
                                    :
                                    status == 1 ?
                                        require('../../../assets/root/concertPro2.png')
                                        :
                                        status == 2 ?
                                            require('../../../assets/root/concertPro3.png')
                                            :
                                            require('../../../assets/root/concertPro4.png')
                            }
                            style={styles.animationCircle}
                        />

                        {/* ALL LISTS */}
                        <View style={styles.allListView}>

                            {/* ARTIST */}
                            <Text style={styles.artistName}>{concert?.name}</Text>

                            {/* DATE */}
                            <Text style={styles.date}>{dayjs(concert?.date).format('D MMMM')} <Text style={styles.dateAlt}>{dayjs(concert?.date).format('dddd').toUpperCase()}</Text></Text>

                            {
                                concert?.stageId == 1 ?
                                    <Image
                                        source={require('../../../assets/root/atriumgreen.png')}
                                        style={styles.stage}
                                    />
                                    :
                                    concert?.stageId == 2 ?
                                        <Text style={styles.stageText}>{MainStore.language.performance} <Text style={{ fontFamily: '', letterSpacing: 0.001 }}>{MainStore.language.scene_title}</Text></Text>
                                        :
                                        <Text style={styles.stageText}>{concert?.stageName}</Text>
                            }

                            {/* HOLLY TICKET - BASKET - PAYMENT */}
                            <View style={styles.list}>
                                {
                                    concertInfos.map((item, index) => {
                                        return (
                                            <ConcertList
                                                backgroundColor={item.backgroundColor}
                                                children={item.children}
                                                indexNo={index}
                                                key={index}
                                                logo={item.logo}
                                                rightImg={item.rightImg}
                                                setStatus={(set: boolean) => {
                                                    if (index == 0 && pay == true)
                                                        setPay(set)
                                                    else if (index == 1 && basket == true){
                                                        setPay(set)
                                                        setBasket(set)
                                                    }
                                                    else if (index == 2 && hollyTicket == true) {
                                                        setHollyTicket(set)
                                                        setBasket(set)
                                                        setPay(set)
                                                    }
                                                }}
                                                status={
                                                    index == 0 ?
                                                        pay
                                                        :
                                                        index == 1 ?
                                                            basket
                                                            :
                                                            hollyTicket
                                                }
                                            />
                                        )
                                    })
                                }

                                {/* RULES */}
                                <View style={styles.rulesAltView}>
                                    <ScrollView style={{ height: 295 }} showsVerticalScrollIndicator={false}>
                                        <Text style={styles.rulesText}>
                                            {rules}
                                        </Text>
                                    </ScrollView>

                                </View>
                                {/* BUTTON*/}
                                <Button
                                    onPress={() => {
                                        setStatus(1);
                                        setHollyTicket(true)
                                    }}
                                    style={styles.rulesButton}
                                >
                                    <Text style={styles.rulesButtonText}>{MainStore.language.approwal}</Text>
                                </Button>
                                <Image
                                    source={require('../../../assets/root/green-fold-old.png')}
                                    style={styles.greenFold}
                                    resizeMode="cover"
                                />
                                <Text style={styles.rules}>{MainStore.language.rules}</Text>
                            </View>

                            {/* WIN BUTTON */}

                        </View>
                    </ScrollView>
                    </>
}
        </View >
    )
}
export default ConcertDetail;

// -- STYLES -- //
const styles = StyleSheet.create({
    concertInfoFirstMain: {
        paddingTop: 30,
        paddingHorizontal: 20,
        height: 295
    },
    concertInfoFirstTitle: {
        fontWeight: 'bold',
        color: white,
        fontSize: 18
    },
    concertInfoCardView: {
        borderWidth: 1,
        borderColor: yellow_t1,
        borderRadius: 8,
        height: 44,
        marginTop: 8,
        backgroundColor: white,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8
    },
    concertInfoCardName: { fontSize: 10 },
    concertInfoCardNo: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 2
    },
    concertInfoCardLogo: {
        width: 28,
        height: 17
    },
    concertInfoFirstButtonsView: {
        position: 'absolute',
        bottom: 10,
        height: 40,
        left: 10,
        width: 200,
        flexDirection: 'row',
        alignContent: 'space-between',
        justifyContent: 'space-between'

    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingGif: {
        width: 250,
        height: 35,
    },
    concertInfoFirstButton: {
        borderRadius: 15,
        backgroundColor: white,
        height: 40,
        width: Layout.screen.width - 230,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertInfoFirstText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: blue_t2,
    },
    concertInfoSecondMain: {
        width: Layout.screen.width - 195,
        left: -25,
        height: '100%'
    },
    concertInfoSecondAlt: { margin: 20 },
    concertInfoSecondListView: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    concertInfoSecondListLeft: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
    },
    concertInfoSecondListRight: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold',
        marginRight: 20
    },
    concertInfoSecondGrayWhiteLine: {
        height: 1,
        marginVertical: 15,
        width: '100%',
        backgroundColor: white
    },
    concertInfoSecondButtonsView: {
        position: 'absolute',
        bottom: 0,
        alignSelf: 'center',
        marginBottom: 10
    },
    concertInfoSecondButton: {
        borderRadius: 15,
        height: 40,
        width: 180,
        backgroundColor: blue_t2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertInfoSecondButtonText: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold'
    },
    concertInfoThirdMain: {
        width: Layout.screen.width - 190,
        left: -15,
        marginVertical: 10,
        flex: 1
    },
    concertInfoThirdTitle: { fontSize: 20,         color: black
    },
    concertInfoThirdTopButton: {
        width: '75%',
        alignSelf: 'center',
        height: 30,
        backgroundColor: green_t1,
        marginVertical: 5,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },
    concertInfoThirdTopButtonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    concertInfoThirdTopButtonTwo: {
        width: '75%',
        alignSelf: 'center',
        height: 30,
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: green_t2,
        marginVertical: 5,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8
    },
    concertInfoThirdTopButtonTwoText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: black
    },
    concertInfoThirdPieceView: {
        borderWidth: 1,
        borderColor: green_t2,
        borderRadius: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 30,
        width: '75%',
        alignSelf: 'center',
        marginVertical: 10,
        backgroundColor: green_t1,
        alignItems: 'center',
        paddingHorizontal: 1
    },
    concertInfoThirdPieceAltView: {
        width: 44,
        borderRadius: 8,
        height: 26,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertInfoThirdPieceAltViewText: {
        fontSize: 24,
        top: -2,
        color: black
    },
    concertInfoThirdPiece: {
        fontSize: 16,
        color: white,
        fontWeight: 'bold',
    },
    concertInfoThirdPrice: {
        alignSelf: 'center',
        fontSize: 18,
        bottom:10,
        fontWeight: 'bold'
    },
    concertInfoThirdButton: {
        borderRadius: 8,
        width: '75%',
        height: 40,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: green_t1,
        marginTop: 0,
        marginBottom:80
    },
    concertInfoThirdButtonText: {
        fontWeight: 'bold',
        color: white,
        fontSize: 18
    },
    altText: {
        color: gray_t4,
        fontWeight: 'bold',
        fontSize: 16,
        left : 30
    },
    payButton: {
        backgroundColor: yellow_t1,  // Ödeme butonu rengi
        borderRadius: 10,
        alignItems: "center",
        justifyContent: "center",
        width: 95,  // Buton genişliği
        height: 35,
        alignSelf: "center",
    },
    payButtonText: {
        color: white,
        fontSize: 15,
        fontWeight: "bold",
    },

    addCardButton: {
        backgroundColor: green_t2,  // Kart ekleme butonu rengi
        borderRadius: 10,
        alignItems: "center",
        justifyContent: "center",
        width: 95,  // Buton genişliği
        height: 35,
        alignSelf: "center",
    },
    addCardButtonText: {
        color: white,
        fontSize: 15,
        fontWeight: "bold",
    },
    main: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        backgroundColor: kremrengi,
    },
    containerPayTr: { flex: 1 },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    concertImg: {
        alignSelf: 'center',
        borderBottomLeftRadius: 204,
        width: 246,
        height: 300,
        borderBottomRightRadius: 204
    },
    artistName: {
        color: black,
        alignSelf: 'center',
        fontSize: 25,
        fontWeight: 'bold'
    },
    date: {
        fontWeight: 'bold',
        fontSize: 12,
        color: black,
        alignSelf: 'center',
        marginVertical: 2
    },
    stage: {
        height: 10,
        width: 118,
        alignSelf: 'center',
        marginTop: 7
    },
    stageText: {
        fontFamily: 'Helvetica Neue LT Pro',
        textAlign: 'center',
        fontSize: 18,
        marginTop: 10,
        color: green_t1
    },
    dateAlt: { fontWeight: 'normal' },
    safeAreaView: {
        flexGrow: 1,
        zIndex: 1,
        position: 'absolute',
        alignSelf: 'center'
    },
    loadingSpinner: { marginTop: 90 },
    animationCircle: {
        width: 321,
        height: 161,
        top: 175,
        alignSelf: 'center',
        position: 'absolute',
    },
    allListView: {
        top: 50,
        marginBottom: 150
    },
    list: {
        backgroundColor: "#107E0D",
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        height: 295,
        marginVertical: 20,
        width: Layout.screen.width / 1.17,
        marginRight: 20,
        alignSelf: 'center',
    },
    rulesAltView: {
        height: 215,
        width: Layout.screen.width / 1.4,
        alignItems: 'center',
        marginLeft: 48,
        paddingHorizontal: 10,
        zIndex: -1,
    },
    rulesText: {
        marginLeft: 75,
        marginVertical: 20,
        color: white,
        fontSize: 8
    },
    rulesButton: {
        marginRight: Layout.screen.width / 16,
        width: Layout.screen.width / 1.5 / 2,
        height: 40,
        marginLeft: 150,
        backgroundColor: white,
        borderRadius: 15,
        marginBottom: 100
    },
    rulesButtonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: green_t1
    },
    greenFold: {
        height: 295,
        position: 'absolute',
        right: -Layout.screen.width / 20,
        width: 31,
        borderRadius: 10
    },
    rules: {
        fontSize: 12,
        fontWeight: 'bold',
        zIndex: 2,
        color: white,
        position: 'absolute',
        width: 85,
        height: 15,
        right: '-13.5%',
        top: 35,
        transform: [{ rotate: '270deg' }]
    },
    winButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: green_t1,
        height: 40,
        width: 180,
        borderRadius: 15,
        alignSelf: 'center',
        justifyContent: 'center'
    },
    hPWhite: {
        height: 31.02,
        width: 28
    },
    win: {
        fontSize: 18,
        fontWeight: 'bold',
        color: white,
        marginLeft: 5
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: white,
        borderRadius: 20,
        padding: 20,
        width: '80%',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        color: black
    },
    modalText: {
        fontSize: 16,
        marginBottom: 10,
        textAlign: 'center',
        color: black
    },
    modalAmount: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
        color: black
    },
    modalButtonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        paddingHorizontal: 20,
    },
    modalButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 10,
        minWidth: 100,
        alignItems: 'center',
    },
    modalButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
    },
    walletIcon: {
        width: 70,
        height: 40,
    }
});


const rules = `· 18 yaş sınırı vardır. 18 yaş altı giriş yapması yasaktır

· Belirtilen saat kapı açılış saatidir. Konser : 22:00

· Etkinlik mekanına giriş esnasında etkinliğe katılan bay-bayan sayısı eşitliğine dikkat edilecektir. Uymayan kişiler etkinlik mekanına alınmayacaktır.

· Satın alınan biletlerde iptal, iade ve değişiklik yapılmamaktadır.

· Etkinlik girişinde bilet kontrolü
`
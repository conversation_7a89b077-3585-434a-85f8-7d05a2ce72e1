import React, { useState, useRef } from 'react';
import { StyleSheet, View, Text, ScrollView, Image, TouchableOpacity, Linking, Platform, FlatList, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Layout from '../../../constants/Layout';
import { black, gray_t4, gray_t7, green_t1, kremrengi, white } from '../../../constants/Color';
import { shadow } from '../../../constants/Shadow';
import HeaderSix from '../../../components/HeaderSix';
import ImageSlider from './ImageSlider';

const menuData = [
    {
        id: 1,
        category: "Kahvalt<PERSON>",
        icon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png',
        items: [
            {
                id: 1,
                name: "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>",
                description: "<PERSON><PERSON><PERSON>, peynir çeşitleri, domates",
                price: "280 ₺",
                image: "https://images.pexels.com/photos/5490965/pexels-photo-5490965.jpeg",
                prepTime: "20-25 dk"
            },
            {
                id: 2,
                name: "O<PERSON><PERSON>",
                description: "Seçtiğiniz malzemeler ile hazırlanır",
                price: "85 ₺",
                image: "https://images.pexels.com/photos/5490965/pexels-photo-5490965.jpeg",
                prepTime: "10-15 dk"
            }
        ]
    },
    {
        id: 2,
        category: "Ana Yemekler",
        icon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png',
        items: [
            {
                id: 3,
                name: "Garden Burger",
                description: "Özel sos, marul, domates, turşu, patates",
                price: "160 ₺",
                image: "https://images.pexels.com/photos/5490965/pexels-photo-5490965.jpeg",
                prepTime: "20-25 dk"
            }
        ]
    },
    {
        id: 3,
        category: "Tatlılar",
        icon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png',
        items: [
            {
                id: 4,
                name: "Magnolia",
                description: "Frambuaz sos ile servis edilir",
                price: "95 ₺",
                image: "https://images.pexels.com/photos/5490965/pexels-photo-5490965.jpeg",
                prepTime: "15-20 dk"
            }
        ]
    }
];

const contactInfo = {
    address: 'Barbaros Mahallesi, Ihlamur Sokak No: 5, Ataşehir/İstanbul',
    phone: '+90 (555) 123 45 67',
    email: '<EMAIL>',
    workingHours: 'Hergün 09:00 - 22:00',
    mapIcon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png',
    phoneIcon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png',
    emailIcon: 'https://cdn-icons-png.flaticon.com/512/985/985505.png'
};


const MenuItem = ({ item }) => (
    <View style={styles.menuItem}>
        <Image source={{ uri: item.image }} style={styles.menuItemImage} />
        <View style={styles.menuItemContent}>
            <View style={styles.menuItemHeader}>
                <View>
                    <Text style={styles.menuItemName}>{item.name}</Text>
                    <Text style={styles.menuItemPrep}>{item.prepTime}</Text>
                </View>
                <Text style={styles.menuItemPrice}>{item.price}</Text>
            </View>
            <Text style={styles.menuItemDescription}>{item.description}</Text>
        </View>
    </View>
);

const HollyGarden = ({ navigation }) => {
    const [header, setHeader] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState(menuData[0].id);

    const handleScroll = (event) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        setHeader(offsetY > 5);
    };

    return (
        <SafeAreaView style={styles.container}>
            <HeaderSix
                navigation={navigation}
            />
            
            <View style={styles.safeArea}>
                <ScrollView 
                    showsVerticalScrollIndicator={false}
                    onScroll={handleScroll}
                    scrollEventThrottle={16}
                >
                    <ImageSlider />
                    {/* Menu Section */}
                    <View style={styles.menuContainer}>
                        <Text style={styles.sectionTitle}>Menü</Text>
                        <ScrollView 
                            horizontal 
                            showsHorizontalScrollIndicator={false}
                            style={styles.categoryTabs}
                        >
                            {menuData.map((category) => (
                                <TouchableOpacity
                                    key={category.id}
                                    style={[
                                        styles.categoryTab,
                                        selectedCategory === category.id && styles.selectedCategoryTab
                                    ]}
                                    onPress={() => setSelectedCategory(category.id)}
                                >
                                    <Image 
                                        source={{ uri: category.icon }} 
                                        style={[
                                            styles.categoryIcon,
                                            selectedCategory === category.id && styles.selectedCategoryIcon
                                        ]}
                                    />
                                    <Text style={[
                                        styles.categoryTabText,
                                        selectedCategory === category.id && styles.selectedCategoryTabText
                                    ]}>
                                        {category.category}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </ScrollView>

                        {menuData
                            .filter(category => category.id === selectedCategory)
                            .map(category => (
                                <View key={category.id} style={styles.categoryContainer}>
                                    {category.items.map(item => (
                                        <MenuItem key={item.id} item={item} />
                                    ))}
                                </View>
                            ))
                        }
                    </View>

                    {/* Contact Section */}
                    <View style={styles.infoContainer}>
                        <Text style={styles.sectionTitle}>İletişim Bilgileri</Text>
                        
                        <TouchableOpacity 
                            style={styles.infoRow}
                            onPress={() => Linking.openURL(`https://maps.google.com/?q=${contactInfo.address}`)}
                        >
                            <Image 
                                source={{ uri: contactInfo.mapIcon }}
                                style={styles.infoIcon}
                            />
                            <View style={styles.infoTextContainer}>
                                <Text style={styles.infoLabel}>Adres</Text>
                                <Text style={styles.infoText}>{contactInfo.address}</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity 
                            style={styles.infoRow}
                            onPress={() => Linking.openURL(`tel:${contactInfo.phone}`)}
                        >
                            <Image 
                                source={{ uri: contactInfo.phoneIcon }}
                                style={styles.infoIcon}
                            />
                            <View style={styles.infoTextContainer}>
                                <Text style={styles.infoLabel}>Telefon</Text>
                                <Text style={styles.infoText}>{contactInfo.phone}</Text>
                            </View>
                        </TouchableOpacity>

                        <View style={styles.workingHoursContainer}>
                            <Text style={styles.workingHoursTitle}>Çalışma Saatleri</Text>
                            <Text style={styles.workingHoursText}>{contactInfo.workingHours}</Text>
                        </View>
                    </View>
                </ScrollView>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    safeArea: {
        flex: 1,
    },
    bannerContainer: {
        marginTop: 20,
        height: 220,
    },
    bannerList: {
        paddingHorizontal: 20,
    },
    bannerItem: {
        width: Layout.screen.width - 40,
    },
    bannerImage: {
        width: Layout.screen.width - 40,
        height: 180,
        borderRadius: 15,
    },
    paginationDots: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 10,
    },
    dot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginHorizontal: 4,
    },
    menuContainer: {
        padding: 20,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: black,
        marginBottom: 20,
        fontFamily: 'MADE TOMMY',
    },
    categoryTabs: {
        marginBottom: 20,
    },
    categoryTab: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 10,
        paddingHorizontal: 15,
        marginRight: 10,
        borderRadius: 20,
        backgroundColor: white,
    },
    selectedCategoryTab: {
        backgroundColor: green_t1,
    },
    categoryIcon: {
        width: 24,
        height: 24,
        marginRight: 8,
        tintColor: green_t1,
    },
    selectedCategoryIcon: {
        tintColor: white,
    },
    categoryTabText: {
        fontSize: 16,
        color: black,
        fontFamily: 'MADE TOMMY',
    },
    selectedCategoryTabText: {
        color: white,
    },
    menuItem: {
        backgroundColor: white,
        flexDirection: 'row',
        borderRadius: 15,
        marginBottom: 15,
        overflow: 'hidden',
        ...shadow,
    },
    menuItemImage: {
        width: '35%',
        height: 130,
    },
    menuItemContent: {
        padding: 15,
        width: '65%',
    },
    menuItemHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    menuItemName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: black,
        fontFamily: 'MADE TOMMY',
    },
    menuItemPrep: {
        fontSize: 12,
        color: gray_t4,
        marginTop: 4,
        fontFamily: 'MADE TOMMY',
    },
    menuItemPrice: {
        fontSize: 16,
        fontWeight: 'bold',
        color: green_t1,
        fontFamily: 'MADE TOMMY',
    },
    menuItemDescription: {
        fontSize: 14,
        color: gray_t4,
        lineHeight: 20,
        fontFamily: 'MADE TOMMY',
    },
    infoContainer: {
        padding: 20,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: white,
        padding: 15,
        borderRadius: 10,
        marginBottom: 15,
        ...shadow,
    },
    infoIcon: {
        width: 24,
        height: 24,
        tintColor: green_t1,
    },
    infoTextContainer: {
        marginLeft: 15,
        flex: 1,
    },
    infoLabel: {
        fontSize: 14,
        color: gray_t4,
        marginBottom: 4,
        fontFamily: 'MADE TOMMY',
    },
    infoText: {
        fontSize: 16,
        color: black,
        fontFamily: 'MADE TOMMY',
    },
    workingHoursContainer: {
        backgroundColor: green_t1,
        padding: 20,
        borderRadius: 15,
        alignItems: 'center',
        marginTop: 10,
        marginBottom: 30,
        ...shadow,
    },
    workingHoursTitle: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        fontFamily: 'MADE TOMMY',
    },
    workingHoursText: {
        color: white,
        fontSize: 16,
        fontFamily: 'MADE TOMMY',
    },
    categoryContainer: {
        marginBottom: 20,
    },
    sectionSeparator: {
        height: 1,
        backgroundColor: gray_t7,
        marginVertical: 20,
    },
    addressTouchable: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: white,
        padding: 15,
        borderRadius: 10,
        marginBottom: 15,
        ...shadow,
    },
    socialIconsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 15,
        gap: 20,
    },
    socialIcon: {
        width: 32,
        height: 32,
        tintColor: white,
    },
    badgeContainer: {
        position: 'absolute',
        top: -5,
        right: -5,
        backgroundColor: green_t1,
        borderRadius: 10,
        paddingHorizontal: 6,
        paddingVertical: 2,
    },
    badgeText: {
        color: white,
        fontSize: 12,
        fontFamily: 'MADE TOMMY',
    },
    searchContainer: {
        backgroundColor: white,
        borderRadius: 10,
        marginHorizontal: 20,
        marginBottom: 20,
        padding: 10,
        flexDirection: 'row',
        alignItems: 'center',
        ...shadow,
    },
    searchInput: {
        flex: 1,
        marginLeft: 10,
        fontSize: 16,
        fontFamily: 'MADE TOMMY',
        color: black,
    },
    searchIcon: {
        width: 20,
        height: 20,
        tintColor: gray_t4,
    },
    filterButton: {
        backgroundColor: green_t1,
        borderRadius: 8,
        padding: 8,
        marginLeft: 10,
    },
    filterIcon: {
        width: 20,
        height: 20,
        tintColor: white,
    },
    emptyStateContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
        marginTop: 50,
    },
    emptyStateImage: {
        width: 150,
        height: 150,
        marginBottom: 20,
        tintColor: gray_t4,
    },
    emptyStateText: {
        fontSize: 16,
        color: gray_t4,
        textAlign: 'center',
        fontFamily: 'MADE TOMMY',
    },
    loadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255,255,255,0.8)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    menuItemTag: {
        position: 'absolute',
        top: 10,
        right: 10,
        backgroundColor: 'rgba(0,0,0,0.6)',
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderRadius: 15,
    },
    menuItemTagText: {
        color: white,
        fontSize: 12,
        fontFamily: 'MADE TOMMY',
    },
    favoriteButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        backgroundColor: 'rgba(255,255,255,0.9)',
        padding: 8,
        borderRadius: 20,
        ...shadow,
    },
    favoriteIcon: {
        width: 20,
        height: 20,
        tintColor: green_t1,
    },
    orderButton: {
        backgroundColor: green_t1,
        borderRadius: 10,
        padding: 12,
        alignItems: 'center',
        marginTop: 10,
    },
    orderButtonText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        fontFamily: 'MADE TOMMY',
    },
    categoryImage: {
        width: '100%',
        height: 120,
        marginBottom: 15,
        borderRadius: 10,
    },
    categoryTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: black,
        marginBottom: 10,
        fontFamily: 'MADE TOMMY',
    },
    categoryDescription: {
        fontSize: 14,
        color: gray_t4,
        marginBottom: 20,
        lineHeight: 20,
        fontFamily: 'MADE TOMMY',
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5,
    },
    ratingIcon: {
        width: 16,
        height: 16,
        tintColor: '#FFD700',
        marginRight: 4,
    },
    ratingText: {
        fontSize: 14,
        color: gray_t4,
        fontFamily: 'MADE TOMMY',
    },
    bottomSheet: {
        backgroundColor: white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 20,
        minHeight: 300,
    },
    bottomSheetHandle: {
        width: 40,
        height: 4,
        backgroundColor: gray_t4,
        borderRadius: 2,
        alignSelf: 'center',
        marginBottom: 20,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: white,
        borderRadius: 15,
        padding: 20,
        width: '90%',
        ...shadow,
    },
    closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        padding: 10,
    },
    closeIcon: {
        width: 20,
        height: 20,
        tintColor: black,
    },
});

export default HollyGarden;

import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { white, black, gray_t4 } from '../../../constants/Color';

type ProductItemProps = {
    name: string;
    price: string;
    description: string;
    image: string;
    isNew?: boolean;
    rating?: number;
}

const ProductItem: React.FC<ProductItemProps> = ({ 
    name, 
    price, 
    description, 
    image,
    isNew,
}) => {
    return (
        <View style={styles.productItem}>
            <View style={styles.contentContainer}>
                <View style={styles.titleRow}>
                    <Text style={styles.title}>{name}</Text>
                    {isNew && <View style={styles.newBadge}><Text style={styles.newText}>NEW</Text></View>}
                </View>
                <Text style={styles.description}>{description}</Text>
                <Text style={styles.price}>${price}</Text>
            </View>
            <Image source={{ uri: image }} style={styles.productImage} />
        </View>
    );
};

const styles = StyleSheet.create({
    productItem: {
        flexDirection: 'row',
        backgroundColor: white,
        padding: 12,
        marginBottom: 12,
        borderRadius: 10,
        alignItems: 'center',
    },
    productImage: {
        width: 70,
        height: 70,
        borderRadius: 35,
        marginLeft: 12,
    },
    contentContainer: {
        flex: 1,
        justifyContent: 'space-between',
    },
    titleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: black,
    },
    newBadge: {
        backgroundColor: '#FF4B4B',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 12,
    },
    newText: {
        color: white,
        fontSize: 10,
        fontWeight: 'bold',
    },
    price: {
        fontSize: 16,
        fontWeight: 'bold',
        color: black,
        alignSelf: 'flex-start',
        marginTop: 8,
    },
    description: {
        fontSize: 14,
        color: gray_t4,
        marginBottom: 8,
    }
});

export default ProductItem;

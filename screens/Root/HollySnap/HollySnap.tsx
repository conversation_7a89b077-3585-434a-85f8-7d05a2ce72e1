import React from "react";
import { ScrollView, StyleSheet, Text, View, Image } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { observer } from "mobx-react-lite";
import { screens } from "../../../navigation";
import HeaderFive from "../../../components/HeaderFive";
import Menu from "../../../components/Menu";
import { black, black_t4, green_t1, white, yellow_t2, kremrengi } from "../../../constants/Color";
import StageItem from "../../../components/StageItem";
import { MotiView } from "moti";
import { SafeAreaView } from "react-native-safe-area-context";
import Toast from "../../../components/Toast";
import BottomBar from "../../../components/BottomBar";
import SnapView from "../../../components/SnapView";
import { HollySnapStoreInstance } from "../../../stores/HollySnapStore";
import { getImageURL } from "../../../networking/Server";

const HollySnap: React.FC = observer(() => {
    const navigation = useNavigation<screens>();

    React.useEffect(() => {
        HollySnapStoreInstance.getSnaps(navigation);
    }, []);

    const [menu, setMenu] = React.useState(false);
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const [header, setHeader] = React.useState(false);
    const [snapArray, setSnapArray] = React.useState([]);
    const [isSnapViewVisible, setIsSnapViewVisible] = React.useState(false);

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        setHeader(offsetY >= 5);
    };

    const GetUserSnap = (snapUsers: any[]) => {
        const snapsWithRecent = snapUsers.flatMap((user) =>
            user.recentSnaps.map((snap: any) => ({
                id: snap.id,
                image: snap.image, // Snap resmini burada kullanalım
                createdAt: snap.createdAt,
                userId: user.userId,
                ownerFirstName: user.firstName,
                ownerLastName: user.lastName,
                ownerImage: user.image,
                isOwner: user.isOwner,
            }))
        );
    
    
        setSnapArray(snapsWithRecent);
        setIsSnapViewVisible(true);
    };
    
    
    

    return (
        <View style={styles.main}>
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={yellow_t2}
                />
            </View>

            <>
                <Menu
                    menuColor={yellow_t2}
                    navigation={navigation}
                    menuStatus={menu}
                    onMenu={(set: boolean) => setMenu(set)}
                />

                <SafeAreaView style={{ position: 'absolute', zIndex: 10 }}>
                    <HeaderFive
                        logoStyle={styles.logoStyle}
                        searchIcon={false}
                        menuIcon={require('../../../assets/header/menuYellow.png')}
                        backIcon={yellow_t2}
                        navigation={navigation}
                        headerControl={header}
                        logo={require('../../../assets/root/hollySnap.png')}
                        onMenu={(set: boolean | ((prevState: boolean) => boolean)) => setMenu(set)}
                        menuStatus={menu}
                        searchIconColor={""}
                    />
                </SafeAreaView>

                <SafeAreaView>
                    <ScrollView
                        onScroll={handleScroll}
                        style={styles.scroll}
                        showsVerticalScrollIndicator={false}
                    >
                        {HollySnapStoreInstance.snaps.length < 1 ? (
                            <Text style={styles.noVariable}>
                                Henüz uyanmadık {":)"}{"\n"} Snap paylaşabilmeniz için lütfen bekleyiniz.
                            </Text>
                        ) : (
                            HollySnapStoreInstance.snaps.map((item: any, index: number) => (
                                <MotiView
                                    key={index}
                                    from={{ left: -500 }}
                                    animate={{ left: 0 }}
                                    transition={{ type: "timing", duration: 1000 * (index + 1) }}
                                    style={{ marginTop: index === 0 ? 70 : 0 }}
                                >
                                    
                                    <StageItem
                                        onCameraPress={() => {
                                            //@ts-ignore
                                            navigation.navigate("HollySnapDetail", { id: item.id });
                                        }}
                                        arraySnaps={item.snapUsers}
                                        artistImage={{ uri: getImageURL(item.activityImage) }}
                                        cameraIcon={item.stageId === 2 ? require('../../../assets/root/cameraBlack.png') : require('../../../assets/root/cameraWhite.png')}
                                        stage={item.stageId === 1 ? "" : item.stageName}
                                        stageLogo={item.stageId === 1 ? require('../../../assets/root/atriumWhite.png') : ""}
                                        topBackgroundColor={item.stageId === 2 ? white : item.stageId === 1 ? green_t1 : black_t4}
                                        prfTextColor={item.stageId === 2 ? black : white}
                                        artistName={item.activityName}
                                        onProfilePress={() => GetUserSnap(item.snapUsers)}
                                    />
                                    
                                </MotiView>
                            ))
                        )}
                    </ScrollView>
                </SafeAreaView>
                <BottomBar navigation={navigation} type={3} />
            </>

            <SnapView
                snaps={snapArray}
                isVisible={isSnapViewVisible}
                onClose={() => setIsSnapViewVisible(false)}
            />
        </View>
    );
});

export default HollySnap;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flex: 1,
        backgroundColor: kremrengi
    },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    logoStyle: {
        height: 30,
        position: 'absolute',
        left: 35,
        width: 134,
        top: 20
    },
    scroll: {
        marginTop: 0,
        marginBottom: 100
    },
    noVariable: {
        fontFamily: 'MADE TOMMY',
        textAlign: 'center',
        color: yellow_t2,
        height: 500,
        top: 100,
    },
});

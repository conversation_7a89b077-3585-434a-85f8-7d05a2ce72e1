import React from "react";
import { Image, ImageBackground, Linking, PermissionsAndroid, Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { get, getImageURL, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import { black, white, yellow_t2 } from "../../../constants/Color";
import { useNavigation } from "@react-navigation/native";
import Layout from "../../../constants/Layout";
import { Spinner } from "native-base";
import { SafeAreaView } from "react-native-safe-area-context";
import { Camera, useCameraDevice, useCameraPermission } from "react-native-vision-camera";
import ViewShot from "react-native-view-shot";
import SwipeButtonInitialThree from "../../../components/SwipeButtonInitialThree";
import { CameraRoll } from "@react-native-camera-roll/camera-roll";
import Share from "react-native-share";
import { MainStore } from "../../../stores/MainStore";
import HeaderFive from "../../../components/HeaderFive";
import { BackIcon } from "../../../components/Svgs";
import { HollyPointsStoreInstance } from "../../../stores/HollyPointsStore";


const HollySnapDetail: React.FC = (props: any) => {

    const navigation: any = useNavigation();

    const [snapFilters, setSnapFilters]: any = React.useState([]);
    const [hollyPoint, setHollyPoint] = React.useState(0);
    const [instagramPoint, setinstagramPoint] = React.useState(0);

    const [index, setIndex] = React.useState(0);
    const [loading, setLoading] = React.useState(false);

    // -- status -- //
    const [status, setStatus] = React.useState(false);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }
    const [cameraS, setCameraS] = React.useState(true);
    const device = useCameraDevice(cameraS ? "front" : "back");
    const { hasPermission, requestPermission } = useCameraPermission();
    const [photo, setPhoto] = React.useState("");
    const [snap, setSnap] = React.useState("");

    const camera: any = React.useRef<Camera>(null)
    const ref: any = React.useRef();

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- SAVE PHOTO -- // 
    async function hasAndroidPermission() {
        const getCheckPermissionPromise = () => {
            if (Platform.Version >= "33") {
                return Promise.all([
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES),
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO),
                ]).then(
                    ([hasReadMediaImagesPermission, hasReadMediaVideoPermission]) =>
                        hasReadMediaImagesPermission && hasReadMediaVideoPermission,
                );
            } else {
                return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
            }
        };

        const hasPermission = await getCheckPermissionPromise();
        if (hasPermission) {
            return true;
        }

        const getRequestPermissionPromise = () => {
            if (Platform.Version >= "33") {
                return PermissionsAndroid.requestMultiple([
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
                ]).then(
                    (statuses) =>
                        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] ===
                        PermissionsAndroid.RESULTS.GRANTED &&
                        statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] ===
                        PermissionsAndroid.RESULTS.GRANTED,
                );
            } else {
                return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE).then((status) => status === PermissionsAndroid.RESULTS.GRANTED);
            }
        };

        return await getRequestPermissionPromise();
    }

    async function savePicture() {
        if (Platform.OS === "android" && !(await hasAndroidPermission())) {
            return;
        }

        CameraRoll.save(snap, { type: "photo" });
        startToast("Fotoğraf kayıt edildi. Galerine bak ;)", "success");
    };

    const takePicture = async () => {

        const photo = await camera?.current?.takePhoto({
            flash: 'auto', // 'on' | 'off',
            qualityPrioritization: 'speed'
        });

        setPhoto(photo.path);
    }

    const getSnapFilters = () => {
        try {
            get(`snaps/detail?id=${props.route.params.id}`).then((res: any) => {
                if (res.type == "success") {
                    setHollyPoint(res.snap.hollyPoints);
                    setinstagramPoint(res.snap.instagramPoints);
                    setSnapFilters(res.snap.images)
                } else {
                    startToast("Bir hata oluştu.", "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 2500)
                }
            })
        } catch (e) {
            startToast("Bir hata oluştu.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 2500)
        }
    }


    React.useEffect(() => {
        getSnapFilters()
        requestPermission();
    }, []);

    const shareInstagramStory = async () => {
        const shareOptions = {
            title: 'Share image as instastory',
            backgroundImage: snap.toString(),
            social: Share.Social.INSTAGRAM_STORIES,
            appId: '500149042271363',
        };

        try {
            //@ts-ignore
            const ShareResponse = await Share.shareSingle(shareOptions);
        } catch (error) {
        }
    };

    const sendSnap = async () => {
        setLoading(true);
        try {
            post("snaps/share-snap", {
                id: props.route.params.id
            }).then(async (res: any) => {
                if (res.type == "success") {
                    startToast("Fotoğraf yükleniyor. Lütfen bekleyin!", "success");
                    const formData = new FormData();
                    const uriParts = snap.split('/');
                    formData.append('image', {
                        uri: snap,
                        type: "image/jpeg",
                        name: uriParts[uriParts.length - 1]
                    });
                    formData.append('fileName', res.fileName);

                    try {
                        post("functions/upload", formData).then(async (resTwo: any) => {
                            if (resTwo.type == 'success') {
                                startToast("Snap başarıyla paylaşıldı.", "success");
                                await HollyPointsStoreInstance.fetchHollyPoints();

                                setTimeout(() => {
                                    navigation.pop();
                                }, 2500)
                            } else {
                                setLoading(false);
                                startToast("Bir hata oluştu 1.", "error");
                            }
                        });
                    } catch (e) {
                        setLoading(false);
                        startToast("Bir hata oluştu 2.", "error");
                    }
                } else {
                    setLoading(false);
                    startToast("Bir hata oluştu. 3", "error");
                }
            });
        } catch (e) {
            setLoading(false);
            startToast("Bir hata oluştu.", "error");
        }
    }

    const handleSwipe = (direction: any) => {
        if (direction === 'left') {
           
            shareInstagramStory();
        } else if (direction === 'right') {
            

            sendSnap();
        }
    };



    if (device == null) return <View style={{
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    }}>
        <SafeAreaView style={{ position: 'absolute', zIndex: 10, top: 0, left: 0, alignSelf: 'center' }}>
            {/* HEADER */}

            
            <HeaderFive
                logoStyle={{
                    height: 30,
                    position: 'absolute',
                    left: 35,
                    width: 134,
                    top: 17
                }}
                searchIcon={false}
                backIcon={yellow_t2}
                navigation={navigation}
                logo={require('../../../assets/root/hollySnap.png')}
                searchIconColor={""} onMenu={undefined} menuStatus={false}
            />
        </SafeAreaView>
        <Image
            source={require('../../../assets/root/hollySnap.png')}
            style={{ height: 70, width: 300 }}
            resizeMode="contain"
        />
        <Text
            style={{
                fontFamily: 'MADE TOMMY',
                color: yellow_t2,
                fontSize: 22,
                textAlign: 'center'
            }}
        >{MainStore.language.no_camera} {"\n"} :'(</Text>
    </View>

    if (!hasPermission) {
        return (
            <View style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <SafeAreaView style={{ position: 'absolute', zIndex: 10, top: 0, left: 0, alignSelf: 'center' }}>
                    {/* HEADER */}
                    
                    <HeaderFive
                        logoStyle={{
                            height: 30,
                            position: 'absolute',
                            left: 35,
                            width: 134,
                            top: 17
                        }}
                        searchIcon={false}
                        backIcon={yellow_t2}
                        navigation={navigation}
                        logo={require('../../../assets/root/hollySnap.png')}
                        searchIconColor={""} onMenu={undefined} menuStatus={false}
                    />
                </SafeAreaView>
                <Image
                    source={require('../../../assets/root/hollySnap.png')}
                    style={{ height: 100, width: 300 }}
                    resizeMode="contain"
                />
                <Text
                    style={{
                        fontFamily: 'MADE TOMMY',
                        color: yellow_t2,
                        fontSize: 24,
                        textAlign: 'center',
                        width: Layout.screen.width / 1.2
                    }}
                >{MainStore.language.request_camera}</Text>
                <TouchableOpacity
                    onPress={() => {
                        Linking.openSettings();
                    }}
                    style={{
                        width: Layout.screen.width / 1.5,
                        height: 45,
                        marginTop: 15,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 10,
                        backgroundColor: yellow_t2
                    }}>
                    <Text style={{
                        fontSize: 18,
                        color: white,
                        fontFamily: 'MADE TOMMY'
                    }}>{MainStore.language.go_settings}</Text>
                </TouchableOpacity>
            </View>
        )
    }

    if (snap) {
        return (
            <SafeAreaView style={{ flexGrow: 1 }}>
                            <TouchableOpacity
                style={styles.goBackButton}
                onPress={() => navigation.goBack()}
            >
                <BackIcon
                        size={25}
                        color={white}
                    />
            </TouchableOpacity>
                {/* TOAST */}
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={yellow_t2}
                    />
                </View>
                <Image
                    source={{ uri: snap }}
                    resizeMode="cover"
                    style={{
                        height: Layout.screen.height - 90,
                        width: Layout.screen.width
                    }}
                />
                <View style={{
                    height: 100,
                    paddingBottom: 10,
                    position: 'absolute',
                    bottom: 0,
                    paddingHorizontal: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: Layout.screen.width,
                    backgroundColor: black
                }}>

                    {
                        loading ?
                            <View style={{
                                width: '100%'
                            }}>
                                <Spinner alignSelf={'center'} color={white} size={18} />
                            </View>
                            :
                            <View>
                                <SwipeButtonInitialThree hollyPoint={hollyPoint} instagramPoint={instagramPoint} onSwipe={handleSwipe} />
                            </View>
                    }
                    {
                        loading ?
                            <></>
                            :
                            <TouchableOpacity
                                onPress={() => {
                                    savePicture();
                                }}
                                style={{
                                    height: 50,
                                    backgroundColor: 'rgba(256, 256, 256, 0.15)',
                                    borderRadius: 15,
                                    width: Layout.screen.width / 8,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    flexDirection: 'row',
                                }}
                            >
                                <Image
                                    source={require('../../../assets/root/download.png')}
                                    style={{
                                        width: 21,
                                        height: 27
                                    }}
                                />
                            </TouchableOpacity>
                    }

                </View>
                
            </SafeAreaView>
        )
    }

    return (
        <SafeAreaView style={{ flexGrow: 1 }}>
            <TouchableOpacity
                style={styles.goBackButton}
                onPress={() => navigation.goBack()}
            >
                <BackIcon
                        size={25}
                        color={white}
                    />
            </TouchableOpacity>
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={yellow_t2}
                />
            </View>
            <ViewShot
                ref={ref}
                options={{ fileName: "hollystone", format: "jpg", quality: 0.9 }}
            >
                {
                    photo ?
                        <Image
                            style={{
                                width: Layout.screen.height,
                                height: Layout.screen.width * 2,
                                left: -Layout.screen.width + 180,
                                zIndex: -2,
                                position: 'absolute',
                            }}
                            onLoad={() => {
                                setTimeout(() => {
                                    ref.current.capture().then((uri: any) => {
                                        setSnap(uri);
                                    })
                                }, 500)
                            }}
                            resizeMode="contain"
                            source={{ uri: "file://" + photo }}
                        />
                        :
                        <Camera
                            photo={true}
                            ref={camera}
                            focusable={true}
                            style={StyleSheet.absoluteFill}
                            device={device}
                            isActive={true}
                        />
                }
                <Image
                    source={{ uri: getImageURL(snapFilters[index]) }}
                    resizeMode="cover"
                    style={{
                        height: Layout.screen.height - 90,
                        width: Layout.screen.width,
                        zIndex: 1,
                    }}
                />
            </ViewShot>

            <View style={{
                height: 100,
                paddingBottom: 10,
                position: 'absolute',
                bottom: 0,
                paddingHorizontal: 20,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: Layout.screen.width,
                backgroundColor: black
            }}>
                <TouchableOpacity
                    onPress={() => {
                        if (snapFilters.length != index + 1)
                            setIndex(index + 1)
                        else {
                            setIndex(0);
                        }
                    }}
                    style={{
                        height: 35,
                        width: 28,
                        borderRadius: 5,
                        borderWidth: 1,
                        borderColor: white
                    }}
                >
                    <Image
                        source={{ uri: getImageURL(snapFilters[index]) }}
                        resizeMode="cover"
                        style={{
                            height: 34,
                            borderRadius: 5,
                            width: 27,
                        }}
                    />
                </TouchableOpacity>
                {/* CAMERA ICON */}
                <TouchableOpacity
                    onPress={() => {
                        takePicture();
                    }}
                    style={
                        [
                            styles.cameraTouch,
                            {
                                borderColor: yellow_t2,
                            }
                        ]
                    }
                >
                    <View
                        style={
                            [
                                styles.cameraView,
                                {
                                    backgroundColor: yellow_t2,
                                }
                            ]
                        }
                    >
                        <Image
                            source={require('../../../assets/root/cameraWhite.png')}
                            style={styles.cameraIcon}
                            resizeMode="contain"
                        />
                    </View>
                </TouchableOpacity>
                <TouchableOpacity
                    onPress={() => {
                        setCameraS(!cameraS)
                    }}
                >
                    <Image
                        source={require('../../../assets/root/camFrontBack.png')}
                        style={{
                            width: 33,
                            height: 26
                        }}
                    />
                </TouchableOpacity>
            </View>

        </SafeAreaView>
    )
}
export default HollySnapDetail;

// -- STYLES -- // 
const styles = StyleSheet.create({
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    button: {
        height: 54,
        backgroundColor: 'rgba(256, 256, 256, 0.15)',
        borderRadius: 15,
        width: Layout.screen.width / 1.3,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    goBackButton: {
        position: 'absolute',
        top: 20, // Üstten boşluk
        left: 20, // Soldan boşluk
        zIndex: 10, // Üst katmana çıkması için
        padding: 10, // Etrafındaki boşluk
    },
    buttonLeftText: {
        fontSize: 13,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
    buttonAlt: {
        height: 50,
        width: 150,
        position: 'absolute',
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 5
    },
    
    buttonRightText: {
        fontSize: 13,
        fontFamily: 'MADE TOMMY',
        color: white,
    },

    cameraTouch: {
        borderWidth: 2,
        width: 68,
        height: 68,
        borderRadius: 34,
        alignItems: 'center',
        justifyContent: 'center'
    },
    cameraView: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 58,
        width: 58,
        borderRadius: 29
    },
    cameraIcon: {
        width: 29,
        height: 23
    },
});

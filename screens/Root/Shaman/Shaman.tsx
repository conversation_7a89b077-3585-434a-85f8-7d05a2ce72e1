import React from "react";
import { FlatList, Image, ImageBackground, ScrollView, StyleSheet, Animated, Text, TouchableOpacity, View, LayoutAnimation, Platform, UIManager } from "react-native";
import HeaderFive from "../../../components/HeaderFive";
import { useNavigation } from "@react-navigation/native";
import Layout from "../../../constants/Layout";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { black, black_t3, gray_t10, gray_t9, green_t1, green_t2, white, red_t2, gray_t8, gray_t7, gray_t1, gray_t2, gray_t3, gray_t4 } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { getImageURL } from "../../../networking/Server"; // get kaldırıldı, artık必要ない
import Toast from "../../../components/Toast";
import { screens } from "../../../navigation";
import Ticket from "../../../components/Ticket";
import BottomBar from "../../../components/BottomBar";
import { MainStore } from "../../../stores/MainStore";
import { ShamanStoreInstance as ShamanStore } from "../../../stores/ShamanStore";
import { observer } from "mobx-react-lite"; // MobX observer

const Shaman: React.FC = observer(() => { // observer ile sar
  const navigation = useNavigation<screens>();

  const [menu, setMenu] = React.useState(false);
  const [statusToast, setStatusToast]: any = React.useState(null);
  const [typeToast, setTypeToast] = React.useState("");
  const [subtitleToast, setSubTitleToast] = React.useState("");
  const startToast = (message: string, type: string) => {
    setStatusToast(true);
    setSubTitleToast(message);
    setTypeToast(type);
    setTimeout(() => setStatusToast(false), 1500);
  };

  React.useEffect(() => {
    ShamanStore.fetchShamanData(); // veriyi store'dan tekrar fetch et
  }, []);
  

  const [totalSeconds, setTotalSeconds]: any = React.useState(3600);
  const [hours, setHours] = React.useState(1);
  const [minutes, setMinutes] = React.useState(0);
  const [seconds, setSeconds] = React.useState(0);
  const [isRunning, setIsRunning] = React.useState(false);

  const [statusWeek, setStatusWeek] = React.useState(true);
  const [statusMonth, setStatusMonth] = React.useState(false);
  const [statusAll, setStatusAll] = React.useState(false);

  const insets = useSafeAreaInsets();
  const [header, setHeader] = React.useState(false);

  const handleScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setHeader(offsetY >= 5);
  };

  const animateTransition = () => {
    LayoutAnimation.configureNext({
      duration: 600,
      update: {
        type: LayoutAnimation.Types.spring,
        springDamping: 0.5,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  };

  const getShamanWeek = () => {
    animateTransition();
    setStatusMonth(false);
    setStatusWeek(true);
    setStatusAll(false);
  };

  const getShamanMonth = () => {
    animateTransition();
    setStatusMonth(true);
    setStatusWeek(false);
    setStatusAll(false);
  };

  const getShamanAll = () => {
    animateTransition();
    setStatusMonth(false);
    setStatusWeek(false);
    setStatusAll(true);
  };

  // Countdown Logic
  React.useEffect(() => {
    let interval: any = null;
    if (isRunning) {
      interval = setInterval(() => {
        if (totalSeconds > 0) {
          setTotalSeconds((prevSeconds: number) => prevSeconds - 1);
          setHours(Math.floor(totalSeconds / 3600));
          setMinutes(Math.floor((totalSeconds % 3600) / 60));
          setSeconds(totalSeconds % 60);
        } else {
          setIsRunning(false);
        }
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRunning, totalSeconds]);

  function calculateSecondsBetweenDates(startDate: any, endDate: any) {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    if (isNaN(start) || isNaN(end)) return 'Geçersiz tarih';
    const secondsDifference = Math.abs((end - start) / 1000);
    return Math.floor(secondsDifference);
  }

  const startTimer = (endDate: any) => {
    if (endDate) {
      setTotalSeconds(calculateSecondsBetweenDates(new Date(), endDate));
      setTimeout(() => setIsRunning(true), 1000);
    }
  };

  return (
    <View style={styles.main}>
      <View style={styles.toastView}>
        <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
      </View>

      {ShamanStore.loading ? (
        <View style={styles.loadingContainer}>
          <Image source={require('../../../assets/root/shamangif.gif')} style={styles.loadingGif} />
        </View>
      ) : (
        <>
          <SafeAreaView style={{ zIndex: 10, position: 'absolute' }}>
            <HeaderFive
              headerControl={header}
              logoStyle={styles.logoStyle}
              searchIcon={false}
              menuIcon={false}
              backIcon={black_t3}
              navigation={navigation}
              logo={<Image style={styles.shamanLogo} source={require('../../../assets/root/shaman.png')} />}
              onMenu={setMenu}
              menuStatus={menu}
              searchIconColor={""}
            />
          </SafeAreaView>

          <SafeAreaView style={styles.scroll}>
            <FlatList
              onScroll={handleScroll}
              ListHeaderComponent={
                <>
                  <View style={[styles.top, shadow, { marginTop: Math.max(insets.bottom, 0) ? 70 : 70 }]}>
                    <TouchableOpacity
                      onPress={getShamanWeek}
                      style={[styles.buttonStyle, statusWeek && styles.activeButton]}
                    >
                      <Text style={[styles.topTexts, { textAlign: 'center' }]}>
                        {MainStore.language.weekly}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={getShamanMonth}
                      style={[styles.buttonStyle, statusMonth && styles.activeButton]}
                    >
                      <Text style={[styles.topTexts, { textAlign: 'center' }]}>
                        {MainStore.language.montly}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={getShamanAll}
                      style={[styles.buttonStyle, statusAll && styles.activeButton]}
                    >
                      <Text style={[styles.topTexts, { textAlign: 'center' }]}>
                        yıllık
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <View style={styles.shamanTopView}>
                    {/* SECOND */}
                    <ImageBackground
                      style={{ height: 188, width: 104 }}
                      resizeMode="cover"
                      source={statusAll ? require('../../../assets/root/shamanBackgroundGold.png') : require('../../../assets/root/shamanBackground.png')}
                    >
                      <Text style={styles.shamanTopTitle}>2</Text>
                      <Image
                        style={styles.shamanTopPrf}
                        source={
                          statusWeek && ShamanStore.shamanWeekList?.leaderBoard?.[1]
                            ? { uri: getImageURL(ShamanStore.shamanWeekList.leaderBoard[1].image) }
                            : statusMonth && ShamanStore.shamanMonthList?.leaderBoard?.[1]
                            ? { uri: getImageURL(ShamanStore.shamanMonthList.leaderBoard[1].image) }
                            : statusAll && ShamanStore.shamanAllList?.leaderBoard?.[1]
                            ? { uri: getImageURL(ShamanStore.shamanAllList.leaderBoard[1].image) }
                            : require('../../../assets/root/questionMark.png')
                        }
                      />
                      <Text style={styles.shamanTopListTitle}>
                        {statusWeek
                          ? ShamanStore.shamanWeekList?.leaderBoard?.[1]?.nameLastName
                          : statusMonth
                          ? ShamanStore.shamanMonthList?.leaderBoard?.[1]?.nameLastName
                          : ShamanStore.shamanAllList?.leaderBoard?.[1]?.nameLastName}
                      </Text>
                      <View style={styles.shamanTophPView}>
                        <Image style={styles.shamanTopHPLogo} source={require('../../../assets/root/hP.png')} />
                        <Text style={styles.shamanTopHP}>
                          {statusWeek
                            ? ShamanStore.shamanWeekList?.leaderBoard?.[1]?.hollyPoints
                            : statusMonth
                            ? ShamanStore.shamanMonthList?.leaderBoard?.[1]?.hollyPoints
                            : ShamanStore.shamanAllList?.leaderBoard?.[1]?.hollyPoints}
                        </Text>
                      </View>
                    </ImageBackground>

                    {/* FIRST */}
                    <ImageBackground
                      style={{ height: 220, width: 125 }}
                      resizeMode="cover"
                      source={statusAll ? require('../../../assets/root/shamanBackgroundGold.png') : require('../../../assets/root/shamanBackground.png')}
                    >
                      <Text style={styles.shamanTopTitle}>1</Text>
                      <Image
                        style={[styles.shamanTopPrf, { width: 97, height: 97, borderRadius: 48.5, top: 40 }]}
                        source={
                          statusWeek && ShamanStore.shamanWeekList?.leaderBoard?.[0]
                            ? { uri: getImageURL(ShamanStore.shamanWeekList.leaderBoard[0].image) }
                            : statusMonth && ShamanStore.shamanMonthList?.leaderBoard?.[0]
                            ? { uri: getImageURL(ShamanStore.shamanMonthList.leaderBoard[0].image) }
                            : statusAll && ShamanStore.shamanAllList?.leaderBoard?.[0]
                            ? { uri: getImageURL(ShamanStore.shamanAllList.leaderBoard[0].image) }
                            : require('../../../assets/root/questionMark.png')
                        }
                      />
                      <Text style={[styles.shamanTopListTitle, { top: 146 }]}>
                        {statusWeek
                          ? ShamanStore.shamanWeekList?.leaderBoard?.[0]?.nameLastName
                          : statusMonth
                          ? ShamanStore.shamanMonthList?.leaderBoard?.[0]?.nameLastName
                          : ShamanStore.shamanAllList?.leaderBoard?.[0]?.nameLastName}
                      </Text>
                      <View style={[styles.shamanTophPView, { top: 172 }]}>
                        <Image style={styles.shamanTopHPLogo} source={require('../../../assets/root/hP.png')} />
                        <Text style={[styles.shamanTopHP, { left: 15 }]}>
                          {statusWeek
                            ? ShamanStore.shamanWeekList?.leaderBoard?.[0]?.hollyPoints
                            : statusMonth
                            ? ShamanStore.shamanMonthList?.leaderBoard?.[0]?.hollyPoints
                            : ShamanStore.shamanAllList?.leaderBoard?.[0]?.hollyPoints}
                        </Text>
                      </View>
                    </ImageBackground>

                    {/* THIRD */}
                    <ImageBackground
                      style={{ height: 188, width: 104 }}
                      resizeMode="cover"
                      source={statusAll ? require('../../../assets/root/shamanBackgroundGold.png') : require('../../../assets/root/shamanBackground.png')}
                    >
                      <Text style={styles.shamanTopTitle}>3</Text>
                      <Image
                        style={styles.shamanTopPrf}
                        source={
                          statusWeek && ShamanStore.shamanWeekList?.leaderBoard?.[2]
                            ? { uri: getImageURL(ShamanStore.shamanWeekList.leaderBoard[2].image) }
                            : statusMonth && ShamanStore.shamanMonthList?.leaderBoard?.[2]
                            ? { uri: getImageURL(ShamanStore.shamanMonthList.leaderBoard[2].image) }
                            : statusAll && ShamanStore.shamanAllList?.leaderBoard?.[2]
                            ? { uri: getImageURL(ShamanStore.shamanAllList.leaderBoard[2].image) }
                            : require('../../../assets/root/questionMark.png')
                        }
                      />
                      <Text style={styles.shamanTopListTitle}>
                        {statusWeek
                          ? ShamanStore.shamanWeekList?.leaderBoard?.[2]?.nameLastName
                          : statusMonth
                          ? ShamanStore.shamanMonthList?.leaderBoard?.[2]?.nameLastName
                          : ShamanStore.shamanAllList?.leaderBoard?.[2]?.nameLastName}
                      </Text>
                      <View style={styles.shamanTophPView}>
                        <Image style={styles.shamanTopHPLogo} source={require('../../../assets/root/hP.png')} />
                        <Text style={styles.shamanTopHP}>
                          {statusWeek
                            ? ShamanStore.shamanWeekList?.leaderBoard?.[2]?.hollyPoints
                            : statusMonth
                            ? ShamanStore.shamanMonthList?.leaderBoard?.[2]?.hollyPoints
                            : ShamanStore.shamanAllList?.leaderBoard?.[2]?.hollyPoints}
                        </Text>
                      </View>
                    </ImageBackground>
                  </View>

                  <View style={styles.centerView}>
                    <ImageBackground
                      style={styles.centerTop}
                      resizeMode="cover"
                      source={statusAll ? require('../../../assets/root/shamanGiftGold.png') : require('../../../assets/root/shamanGiftBronz.png')}
                    >
                      <Text style={styles.centerTopTitle}>
                        {statusWeek
                          ? MainStore.language.prize_weekly
                          : statusMonth
                          ? MainStore.language.prize_montly
                          : MainStore.language.prize_all_time}
                      </Text>
                    </ImageBackground>
                    {statusWeek ? (
                      ShamanStore.shamanWeekList?.prizeType == 1 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={styles.centerLeftImg}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanWeekList?.sponsorImage) }}
                          />
                          <Image
                            style={styles.centerRightImg}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanWeekList?.prizeImage) }}
                          />
                        </View>
                      ) : ShamanStore.shamanWeekList?.prizeType == 2 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyticket.png')}
                          />
                          <View style={{ bottom: 10 }}>
                            <Ticket
                              name={ShamanStore.shamanWeekList?.ticket?.name}
                              image={ShamanStore.shamanWeekList?.ticket?.image}
                              date={ShamanStore.shamanWeekList?.ticket?.date}
                            />
                          </View>
                        </View>
                      ) : (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyPuanLogo.png')}
                          />
                          <View style={styles.hpView}>
                            <Image source={require('../../../assets/root/hP.png')} style={styles.hP} />
                            <Text style={styles.giftHollyPoint}>{ShamanStore.shamanWeekList?.hollyPoints}</Text>
                          </View>
                        </View>
                      )
                    ) : statusMonth ? (
                      ShamanStore.shamanMonthList?.prizeType == 1 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={styles.centerLeftImg}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanMonthList?.sponsorImage) }}
                          />
                          <Image
                            style={styles.centerRightImg}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanMonthList?.prizeImage) }}
                          />
                        </View>
                      ) : ShamanStore.shamanMonthList?.prizeType == 3 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyticket.png')}
                          />
                          <View style={{ bottom: 10 }}>
                            <Ticket
                              name={ShamanStore.shamanMonthList?.ticket?.name}
                              image={ShamanStore.shamanMonthList?.ticket?.image}
                              date={ShamanStore.shamanMonthList?.ticket?.date}
                            />
                          </View>
                        </View>
                      ) : (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyPuanLogo.png')}
                          />
                          <View style={styles.hpView}>
                            <Image source={require('../../../assets/root/hP.png')} style={styles.hP} />
                            <Text style={styles.giftHollyPoint}>{ShamanStore.shamanMonthList?.hollyPoints}</Text>
                          </View>
                        </View>
                      )
                    ) : (
                      ShamanStore.shamanAllList?.prizeType == 1 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg]}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanAllList?.sponsorImage) }}
                          />
                          <Image
                            style={styles.centerRightImg}
                            resizeMode="contain"
                            source={{ uri: getImageURL(ShamanStore.shamanAllList?.prizeImage) }}
                          />
                        </View>
                      ) : ShamanStore.shamanAllList?.prizeType == 2 ? (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17, bottom: 20, right: 5 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyticket.png')}
                          />
                          <View style={{ bottom: 10 }}>
                            <Ticket
                              name={ShamanStore.shamanAllList?.ticket?.name}
                              image={ShamanStore.shamanAllList?.ticket?.image}
                              date={ShamanStore.shamanAllList?.ticket?.date}
                            />
                          </View>
                        </View>
                      ) : (
                        <View style={styles.centerAltView}>
                          <Image
                            style={[styles.centerLeftImg, { width: 110, height: 17, bottom: 20, right: 5 }]}
                            resizeMode="contain"
                            source={require('../../../assets/root/hollyPuanLogo.png')}
                          />
                          <View style={styles.hpView}>
                            <Image source={require('../../../assets/root/hP.png')} style={styles.hP} />
                            <Text style={styles.giftHollyPoint}>{ShamanStore.shamanAllList?.hollyPoints}</Text>
                          </View>
                          <View style={styles.countDownView}>
                            <View style={{ alignItems: 'center' }}>
                              <View style={{ flexDirection: 'row' }}>
                                {hours
                                  .toString()
                                  .split("")
                                  .map((item, index) => (
                                    <ImageBackground
                                      key={index}
                                      source={require('../../../assets/root/shamanCountBack.png')}
                                      style={styles.countDownBackImg}
                                    >
                                      <Text style={styles.countDownText}>{item}</Text>
                                    </ImageBackground>
                                  ))}
                              </View>
                              <Text style={styles.countDownDateText}>SAAT</Text>
                            </View>
                            <View style={{ alignItems: 'center', marginLeft: 5 }}>
                              <View style={{ flexDirection: 'row' }}>
                                {minutes
                                  .toString()
                                  .padStart(2, "0")
                                  .split("")
                                  .map((item, index) => (
                                    <ImageBackground
                                      key={index}
                                      source={require('../../../assets/root/shamanCountBack.png')}
                                      style={styles.countDownBackImg}
                                    >
                                      <Text style={styles.countDownText}>{item}</Text>
                                    </ImageBackground>
                                  ))}
                              </View>
                              <Text style={styles.countDownDateText}>DAKİKA</Text>
                            </View>
                            <View style={{ alignItems: 'center', marginLeft: 5 }}>
                              <View style={{ flexDirection: 'row' }}>
                                {seconds
                                  .toString()
                                  .padStart(2, "0")
                                  .split("")
                                  .map((item, index) => (
                                    <ImageBackground
                                      key={index}
                                      source={require('../../../assets/root/shamanCountBack.png')}
                                      style={styles.countDownBackImg}
                                    >
                                      <Text style={styles.countDownText}>{item}</Text>
                                    </ImageBackground>
                                  ))}
                              </View>
                              <Text style={styles.countDownDateText}>SANİYE</Text>
                            </View>
                          </View>
                        </View>
                      )
                    )}
                  </View>

                  {(statusWeek && ShamanStore.lastWinners.week?.winner) ||
      (statusMonth && ShamanStore.lastWinners.month?.winner) ||
      (statusAll && ShamanStore.lastWinners.all?.winner) ? (
        <View style={styles.lastWinnerView}>
 <Text style={styles.lastWinnerTitle}>
      {statusWeek ? "Geçen haftanın kazananı" : statusMonth ? "Geçen ayın kazanan" : "Tüm zamanların kazanan"}
    </Text>
    <View style={styles.lastWinnerContent}>
            <Image
              style={styles.lastWinnerImage}
              source={{
                uri: getImageURL(
                  statusWeek
                    ? ShamanStore.lastWinners.week.winner.image
                    : statusMonth
                    ? ShamanStore.lastWinners.month.winner.image
                    : ShamanStore.lastWinners.all.winner.image
                ),
              }}
            />
            <View >
            <Text style={styles.lastWinnerName}>
              {statusWeek
                ? ShamanStore.lastWinners.week.winner.firstName + " " + ShamanStore.lastWinners.week.winner.lastName
                : statusMonth
                ? ShamanStore.lastWinners.month.winner.firstName + " " + ShamanStore.lastWinners.month.winner.lastName
                : ShamanStore.lastWinners.all.winner.firstName + " " + ShamanStore.lastWinners.all.winner.lastName}
            </Text>
            <Text style={styles.lastWinnerPrizeName}>
            {statusWeek ? "Junior Shaman" : statusMonth ? "Senior Shaman" : "Master Shaman"}
            </Text>
            </View>
  
            <Image
              style={styles.lastWinnerPrizeImage}
              source={{
                uri: getImageURL(
                  statusWeek
                    ? ShamanStore.lastWinners.week.prize.prizeImage
                    : statusMonth
                    ? ShamanStore.lastWinners.month.prize.prizeImage
                    : ShamanStore.lastWinners.all.prize.prizeImage
                ),
              }}
            />
            
          </View>
        </View>
      ) : null}

                  <View style={{ marginBottom: 10 }}>
                    <Image
                      source={statusAll ? require('../../../assets/root/shamanListGold.png') : require('../../../assets/root/shamanListBronz.png')}
                      style={styles.listOfShamanTopView}
                      resizeMode="cover"
                    />
                    <Text style={styles.listOfShamanTitle}>{MainStore.language.arrangement}</Text>
                  </View>
                </>
              }
              data={
                statusWeek && ShamanStore.shamanWeekList?.leaderBoard
                  ? ShamanStore.shamanWeekList.leaderBoard.length > 0
                    ? ShamanStore.shamanWeekList.leaderBoard
                    : [{ empty: true }]
                  : statusMonth && ShamanStore.shamanMonthList?.leaderBoard
                  ? ShamanStore.shamanMonthList.leaderBoard.length > 0
                    ? ShamanStore.shamanMonthList.leaderBoard
                    : [{ empty: true }]
                  : statusAll && ShamanStore.shamanAllList?.leaderBoard
                  ? ShamanStore.shamanAllList.leaderBoard.length > 0
                    ? ShamanStore.shamanAllList.leaderBoard
                    : [{ empty: true }]
                  : [{ empty: true }]
              }
              showsVerticalScrollIndicator={false}
              renderItem={({ item, index }) => {
                if (item.empty) {
                  return (
                    <View
                      style={{
                        height: 80,
                        width: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'transparent',
                        marginTop: 20,
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: 'MADE TOMMY',
                          fontSize: 16,
                          color: black_t3,
                          textAlign: 'center',
                        }}
                      >
                        Henüz Sıralama Mevcut Değil
                      </Text>
                    </View>
                  );
                }

                return (
                  <View
                    key={index}
                    style={[
                      styles.shamanListItemView,
                      {
                        marginBottom: index ===
                        (statusWeek
                          ? ShamanStore.shamanWeekList?.leaderBoard?.length - 1
                          : statusMonth
                          ? ShamanStore.shamanMonthList?.leaderBoard?.length - 1
                          : ShamanStore.shamanAllList?.leaderBoard?.length - 1)
                          ? 100
                          : 0,
                      },
                    ]}
                  >
                    <View style={styles.shamanListItemAltView}>
                      <Image style={styles.shamanListItemPrf} source={{ uri: getImageURL(item.image) }} />
                      <Text style={styles.shamanListItemNameSurname}>{item.nameLastName}</Text>
                    </View>
                    <View style={styles.shamanListItemRightView}>
                      <View style={styles.shamanListItemRightLeftView}>
                        <Text style={styles.shamanListItemHP}>{item.hollyPoints}</Text>

                        <Image
                          style={styles.shamanListItemHPLogo}
                          resizeMode="contain"
                          source={require('../../../assets/root/hP.png')}
                        />
                        
                      </View>
                      <View style={styles.shamanListItemRightRightView}>
                        <View
                          style={[styles.shamanListItemRightBack, { backgroundColor: item.status ? green_t1 : red_t2 }]}
                        >
                            <Text style={styles.shamanListItemRightNumber}>{index + 1}</Text>
                        </View>
                      </View>
                    </View>
                  </View>
                );
              }}
              keyExtractor={(item, index) => index.toString()}
            />
          </SafeAreaView>
          <BottomBar navigation={navigation} type={5} />
        </>
      )}
    </View>
  );
});

export default Shaman

// -- STYLES -- // 
const styles = StyleSheet.create({
    main: { flex: 1 },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    lastWinnerView: {
      backgroundColor: 'rgba(221, 217, 217, 0.5)',
      width: Layout.screen.width / 1.05,
      alignSelf: 'center',
      borderRadius: 10,
      marginTop: 20,
      borderWidth: 0.5,
      borderColor: 'rgba(200, 200, 200, 1)',
    },
    lastWinnerTitle: {
      fontSize: 14,
      fontFamily: 'MADE TOMMY',
      color: gray_t4,
      textAlign: 'center',
      padding: 10,
      backgroundColor: 'rgb(199, 199, 199)',
      borderRadius: 10,
      
    },
    lastWinnerContent: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: 10   
    },
    lastWinnerImage: {
      width: 50,
      height: 50,
      borderRadius: 35,
    },
    lastWinnerName: {
      fontSize: 16,
      fontWeight: '600',
      color: black_t3,
      marginBottom: 8,
      textAlign: 'center'
    },
    lastWinnerPrizeImage: {
      width: 50,
      height: 50,
    },
    lastWinnerPrizeName: {
      fontSize: 14,
      color: gray_t1,
      textAlign: 'center',
    },
    
    logoStyle: {
        height: 21,
        position: 'absolute',
        left: 50,
        width: 77.39,
        top: 20
    },
    scroll: {
        flexGrow: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingGif: {
        width: 250,
        height: 45,
    },

    buttonStyle: {
        width: '33%',
        height: 34,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: gray_t9, // Normalde gri tonunda olacak
        borderRadius: 17, 
    },
    activeButton: {
        backgroundColor: gray_t8, // Seçili olduğunda yeşil yap
        opacity: 0.6, // Butonu biraz saydam yap

    },
    topTexts: {
        fontSize: 12,
        fontWeight: 'bold',
        color: black
    },
    top: {
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        backgroundColor: gray_t9,
        height: 34,
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 17.5,
        flexDirection: 'row',
    },
    selectionTopView: {
        width: '33%',
        height: 34,
        backgroundColor: gray_t10,
        borderRadius: 17.5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    topTexts: {
        fontSize: 12,
        fontWeight: 'bold',
        color: black
    },
    shamanTopView: {
        height: 204,
        paddingHorizontal: 10,
        marginTop: 30,
        alignItems: 'flex-end',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    shamanTopTitle: {
        position: 'absolute',
        fontSize: 17,
        top: 9,
        fontWeight: 'bold',
        alignSelf: 'center',
        color: white
    },
    shamanTopPrf: {
        width: 70,
        height: 70,
        borderRadius: 35,
        alignSelf: 'center',
        top: 40,
        position: 'absolute'
    },
    shamanTopListTitle: {
        position: 'absolute',
        fontSize: 9,
        top: 122,
        fontWeight: 'bold',
        alignSelf: 'center',
        color: white
    },
    shamanTophPView: {
        paddingHorizontal: 5,
        height: 32,
        left: 5,
        borderRadius: 15,
        bottom: 12,
        position: 'absolute',
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    shamanTopHPLogo: {
        width: 21.04,
        height: 23.47
    },
    shamanTopHP: {
        fontSize: 16,
        color: green_t1,
        marginLeft: 3,
        left: 10,
        fontWeight: 'bold'
    },
    centerView: {
        backgroundColor: 'rgba(221, 217, 217, 0.5)',
        width: Layout.screen.width / 1.05,
        height: 125,
        alignSelf: 'center',
        borderRadius: 10,
        marginTop: 40,
        borderWidth: 0.5,
        borderColor: 'rgba(200, 200, 200, 1)'
    },
    centerTop: {
        height: 21,
        width: 201.5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    centerTopTitle: {
        fontSize: 9,
        letterSpacing: 1.19,
        fontWeight: 'bold',
        color: black_t3
    },
    centerAltView: {
        height: 104,
        width: Layout.screen.width / 1.05,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        paddingRight: 10,
        paddingLeft: 50
    },
    centerLeftImg: {
        width: 74,
        height: 63
    },
    hpView: {
        borderRadius: 10,
        bottom: 10,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        height: 109,
        width: Layout.screen.width / 2.5
    },
    hP: {
        height: 36,
        width: 32.5
    },
    giftHollyPoint: {
        fontSize: 22,
        color: green_t2,
        fontWeight: 'bold',
        marginLeft: 10
    },
    countDownView: {
        position: 'absolute',
        bottom: 24,
        left: Layout.screen.width / 16,
        flexDirection: 'row'
    },
    countDownDateText: {
        fontSize: 10,
        color: black
    },
    countDownBackImg: {
        width: 15,
        height: 26,
        alignItems: 'center',
        justifyContent: 'center'
    },
    countDownText: {
        fontSize: 19,
        color: white
    },
    centerRightImg: {
        width: 141,
        height: 109,
        bottom: 10,
        borderRadius: 20,
    },
    listOfShamanTopView: {
        height: 42,
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        marginTop: 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20
    },
    listOfShamanTitle: {
        fontSize: 18,
        letterSpacing: 2,
        fontWeight: 'bold',
        color: black_t3,
        position: 'absolute',
        alignSelf: 'center',
        top: 30
    },
    shamanListItemView: {
        height: 42,
        width: Layout.screen.width / 1.05,
        alignSelf: 'center',
        backgroundColor: 'rgba(256, 256, 256, 0.55)',
        borderWidth: 0.5,
        borderTopLeftRadius: 21,
        borderBottomLeftRadius: 21,
        marginTop: 10,
        borderColor: 'rgba(200, 200, 200, 1)',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingLeft: 8
    },
    shamanListItemAltView: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    shamanListItemPrf: {
        width: 30,
        height: 30,
        borderRadius: 15,
    },
    shamanListItemNameSurname: {
        fontSize: 13,
        color: black,
        letterSpacing: 1.1,
        fontWeight: 'bold',
        marginLeft: 20
    },
    shamanListItemRightView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    shamanListItemRightLeftView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    shamanListItemHPLogo: {
        width: 17,
        height: 18.97
    },
    shamanListItemHP: {
        fontSize: 13,
        fontWeight: 'bold',
        color: green_t1,
        marginRight: 10
    },
    shamanListItemRightRightView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 10,
        marginRight: 15
    },
    shamanListItemRightNumber: {
        fontSize: 11,
        fontWeight: 'bold',
        color: white,
        marginRight: 1
    },
    shamanListItemRightBack: {
        height: 21,
        width: 21,
        borderRadius: 10.5,
        alignItems: 'center',
        justifyContent: 'center'
    },
    shamanListItemDownUpIcon: {
        width: 14.72,
        height: 5.25
    },
    shamanLogo: {
        height: 20,
        width: 120,
        top: 25,
    }
});
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import Transaction from '../../../components/Wallet/Transaction';
import { get } from '../../../networking/Server';
import { kremrengi } from '../../../constants/Color';
import GoBack from '../../../components/Wallet/GoBack';
import { SafeAreaView } from 'react-native-safe-area-context';

const TransactionHistory = () => {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    
    useEffect(() => {
        setLoading(true);
        fetchTransactions();
    }, []);
    
    const fetchTransactions = () => {
        get('wallet/transactions')
            .then((res) => {
                if (res.type === 'success') {
                    setTransactions(res.transactions);
                } else {
                    console.error('İşlem geçmişi alınamadı:', res.message);
                }
            })
            .catch((error) => {
                console.error('Hata:', error);
            })
            .finally(() => {
                setLoading(false);
            });
    };
    
    // İşlem tipine göre ikon seçme fonksiyonu
    const getTransactionIcon = (type) => {
        switch(type) {
            case 'holly_points_exchange':
                return require('../../../assets/root/hollyPuanLogo.png'); // Holly Points ikonu
            default:
                return require('../../../assets/root/pay.png'); // Varsayılan ödeme ikonu
        }
    };
    
    return (
        <SafeAreaView style={styles.container}>
            <GoBack />
            <Text style={styles.gecistext}>Geçmiş İşlemleriniz</Text>
            {loading ? (
                <View style={styles.loadingContainer}>
                    <Image source={require('../../../assets/root/cuzdangif.gif')} style={styles.loadingGif} />
                </View>
            ) : (
                <ScrollView showsVerticalScrollIndicator={false} style={styles.scroll}>
                    {transactions.length > 0 ? (
                        transactions.slice().reverse().map((transaction, index) => (
                            <Transaction
                                key={index}
                                icon={getTransactionIcon(transaction.type)}
                                description={transaction.description}
                                date={transaction.createdAt}
                                amount={transaction.amount}
                                type={transaction.type}
                                cashback={transaction.cashback}
                            />
                        ))
                    ) : (
                        <Text style={styles.noTransactionsText}>Geçmiş işleminiz bulunmamaktadır.</Text>
                    )}
                </ScrollView>
            )}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    header: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    transactionList: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingGif: {
        width: 200,
        height: 50,
    },
    gecistext: {
        padding: 20,
        fontSize: 16,
        fontWeight: '700',
        right: '2%',
    },
    scroll: {
        padding: 10,
    },
    noTransactionsText: {
        textAlign: 'center',
        marginVertical: 20,
        fontSize: 16,
        color: 'gray',
    },
});

export default TransactionHistory;
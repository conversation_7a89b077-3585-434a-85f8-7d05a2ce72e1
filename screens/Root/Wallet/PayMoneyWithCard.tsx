import React, { useState, useEffect } from "react";
import {
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  Alert,
  Modal,
  ActivityIndicator,
  FlatList,
  Image,
  StatusBar,
} from "react-native";
import { white, kremrengi, pink, black } from "../../../constants/Color";
import { BackIcon } from "../../../components/Svgs";
import { get } from "../../../networking/Server";
import { useNavigation } from "@react-navigation/native";
import CreditCard from "../../../components/CreditCard";
import Menu from "../../../components/Menu";
import { SafeAreaView } from "react-native-safe-area-context";
import LottieView from "lottie-react-native";

const PayMoneyWithCard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();
  const [giftCards, setGiftCards] = useState<any[]>([]);
  const [showTerminalModal, setShowTerminalModal] = useState(false);
  const [menu, setMenu] = useState(false);

  const startToast = (message: string, type: string) => {
    Alert.alert(
      type === "success" ? "Başarılı" : "Hata",
      message,
      [{ text: "Tamam", style: "default" }],
      { cancelable: true }
    );
  };

  const getGiftCards = () => {
    setLoading(true);
    get("users/gift-cards").then((res: any) => {
      if (res.type === "success") {
        setGiftCards(res.giftCards);
      } else {
        startToast(res.error, "error");
        setTimeout(() => navigation.goBack(), 2000);
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    getGiftCards();
  }, []);



  const handleUseCard = (card: any) => {
    setShowTerminalModal(true);
  };

  const handleCloseTerminalModal = () => {
    setShowTerminalModal(false);
  };

  const renderGiftCardItem = ({ item }: any) => (
    <View style={styles.cardItemContainer}>
      <TouchableOpacity
        style={styles.cardItem}
        onPress={() => handleUseCard(item)}
      >
        <CreditCard
          cardNumber={item.code}
          type={item.type}
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={white} barStyle="dark-content" />

      <Menu
        menuColor={pink}
        navigation={navigation}
        menuStatus={menu}
        onMenu={(set: boolean) => setMenu(set)}
      />

      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.touchTwo}>
        <BackIcon size={25} color={pink} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.touch} onPress={() => setMenu(!menu)}>
        <Image
          style={styles.specialTop}
          resizeMode="contain"
          source={require("../../../assets/menu/menuDikeyPink.png")}
        />
      </TouchableOpacity>

      {loading ? (
        <ActivityIndicator size="large" color={pink} style={styles.loading} />
      ) : giftCards.length < 1 ? (
        <View style={styles.noCardContainer}>
          <Text style={styles.noCardText}>
            Pay kartınız bulunmamaktadır. HollyStone şubelerimizden satın alabilirsiniz!
          </Text>
        </View>
      ) : (
        <View style={styles.contentContainer}>
          <View style={styles.cardArea}>
            <FlatList
              data={giftCards}
              renderItem={renderGiftCardItem}
              keyExtractor={(item) => item.id.toString()}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.cardList}
            />
          </View>
        </View>
      )}

      {/* Terminal Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showTerminalModal}
        onRequestClose={handleCloseTerminalModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Kart ile Ödeme</Text>

            <View style={styles.lottieContainer}>
              <LottieView
                source={require("../../../assets/animations/pay-phone.json")}
                autoPlay
                loop
                style={styles.lottieAnimation}
              />
            </View>

            <Text style={styles.modalText}>
              Lütfen telefonunuzu pos cihazına yaklaştırın.
            </Text>

            <TouchableOpacity style={styles.button} onPress={handleCloseTerminalModal}>
              <Text style={styles.buttonText}>İptal</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: kremrengi,
  },
  contentContainer: {
    flex: 1,
    width: "100%",
    top: 50,
  },
  cardArea: {
    marginVertical: 20,
    width: '100%',
  },
  touch: {
    width: 40,
    height: 40,
    top: 20,
    right: 20,
    zIndex: 100,
    position: "absolute",
  },
  touchTwo: {
    width: 40,
    height: 40,
    top: 20,
    left: 20,
    borderRadius: 10,
    zIndex: 100,
    position: "absolute",
    backgroundColor: white,
    alignItems: "center",
    justifyContent: "center",
  },
  specialTop: {
    width: 40,
    height: 40,
    borderRadius: 5,
  },
  cardList: {
    paddingVertical: 10,
    paddingHorizontal: 5,
    width: '100%',
    paddingBottom: 100, // Ekranın altında boşluk bırakmak için
    alignItems: 'center',
  },
  cardItemContainer: {
    width: '100%',
    marginBottom: 20,
    alignItems: 'center',
  },
  cardItem: {
    borderRadius: 10,
    marginTop: 10,
    width: '95%',
    alignSelf: 'center',

  },
  noCardContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  noCardText: {
    fontSize: 16,
    textAlign: "center",
  },
  loading: {
    flex: 1,
    justifyContent: "center",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: white,
    width: "80%",
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 10,
    fontWeight: "bold",
  },
  modalText: {
    fontSize: 16,
    textAlign: "center",
    marginVertical: 5,
    paddingBottom: 10,
  },
  lottieContainer: {
    width: 200,
    height: 200,
    marginVertical: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  lottieAnimation: {
    width: "100%",
    height: "100%",
  },
  button: {
    backgroundColor: "#dc1354",
    padding: 15,
    alignItems: "center",
    borderRadius: 12,
    width: "40%",
  },
  buttonText: {
    color: white,
    fontWeight: "700",
  },
});

export default PayMoneyWithCard;

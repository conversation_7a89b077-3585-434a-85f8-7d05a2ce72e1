import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFour from "../../../components/HeaderFour";
import { black, blue_t1, green_t1, kremrengi, white, gray_t1 } from "../../../constants/Color";
import CreditCard from "../../../components/CreditCard";
import { get } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import Layout from "../../../constants/Layout";

type CardTransaction = {
  id: number;
  date: string;
  amount: number;
  type: string;
  description: string;
};

const CardDetails = ({ route, navigation }: any) => {
  const { cardNumber, cardType } = route.params;

  const [loading, setLoading] = useState(true);
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState<CardTransaction[]>([]);

  // Toast state'leri
  const [statusToast, setStatusToast] = useState(null);
  const [typeToast, setTypeToast] = useState("");
  const [subtitleToast, setSubTitleToast] = useState("");

  const startToast = (message: string, type: string) => {
    setStatusToast(true);
    setSubTitleToast(message);
    setTypeToast(type);
    setTimeout(() => {
      setStatusToast(false);
    }, 1500);
  };

  // Kart detaylarını ve işlemleri getir
  const getCardDetails = () => {
    setLoading(true);

    get(`users/card-details/${cardNumber}`).then((res: any) => {
      if (res.type === "success") {
        setBalance(res.balance);
        setTransactions(res.transactions);
        setLoading(false);
      } else {
        startToast(res.error, "error");
        setTimeout(() => navigation.goBack(), 2000);
      }
    });
    
  
  };

  useEffect(() => {
    getCardDetails();
  }, []);

  // Tarih formatını düzenle
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.toastView}>
          <Toast
            type={typeToast}
            subtitle={subtitleToast}
            status={statusToast}
            successColor={green_t1}
          />
        </View>

        {/* HEADER */}
        <HeaderFour
          headerStatus={true}
          navigation={navigation}
          title="KART DETAYLARI"
        />

        {loading ? (
          <ActivityIndicator size="large" color={blue_t1} style={styles.loading} />
        ) : (
          <ScrollView style={styles.scrollView}>
            {/* Kart Görünümü */}
            <View style={styles.cardContainer}>
              <View style={styles.cardWrapper}>
                <CreditCard cardNumber={cardNumber} type={cardType} />
              </View>
            </View>

            {/* Bakiye Bilgisi */}
            <View style={styles.balanceContainer}>
              <Text style={styles.balanceLabel}>Mevcut Bakiye</Text>
              <Text style={styles.balanceAmount}>{balance.toFixed(2)} ₺</Text>
            </View>

            {/* İşlem Geçmişi */}
            <View style={styles.transactionsContainer}>
              <Text style={styles.transactionsTitle}>İşlem Geçmişi</Text>

              {transactions.length === 0 ? (
                <Text style={styles.noTransactionsText}>Henüz işlem bulunmamaktadır.</Text>
              ) : (
                transactions.map((transaction) => (
                  <View key={transaction.id} style={styles.transactionItem}>
                    <View style={styles.transactionLeft}>
                      <Text style={styles.transactionDate}>{formatDate(transaction.date)}</Text>
                      <Text style={styles.transactionDescription}>{transaction.description}</Text>
                    </View>
                    <Text
                      style={[
                        styles.transactionAmount,
                        { color: transaction.amount > 0 ? green_t1 : black }
                      ]}
                    >
                      {transaction.amount > 0 ? '+' : ''}{transaction.amount.toFixed(2)} ₺
                    </Text>
                  </View>
                ))
              )}
            </View>

          </ScrollView>
        )}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: kremrengi,
  },
  safeArea: {
    flex: 1,
  },
  toastView: {
    top: 0,
    alignSelf: 'center',
    zIndex: 20,
  },
  loading: {
    flex: 1,
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
    marginTop: 70,
  },
  cardContainer: {
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  cardWrapper: {
    width: '100%',
    paddingHorizontal: 0,
    alignItems: 'center',
  },
  balanceContainer: {
    backgroundColor: white,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    width: '100%',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  balanceLabel: {
    fontSize: 16,
    color: gray_t1,
    marginBottom: 5,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: black,
  },
  transactionsContainer: {
    backgroundColor: white,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    width: '100%',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  transactionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: black,
  },
  noTransactionsText: {
    textAlign: 'center',
    color: gray_t1,
    marginVertical: 20,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionLeft: {
    flex: 1,
  },
  transactionDate: {
    fontSize: 14,
    color: gray_t1,
    marginBottom: 4,
  },
  transactionDescription: {
    fontSize: 16,
    color: black,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  reloadButton: {
    backgroundColor: blue_t1,
    paddingVertical: 15,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 30,
    width: '100%',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  reloadButtonText: {
    color: white,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CardDetails;

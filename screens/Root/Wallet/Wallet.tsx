import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, Image, ScrollView, Dimensions } from 'react-native';
import { get, post } from '../../../networking/Server'; 
import Toast from '../../../components/Toast';
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { black_t2, kremrengi, pink, white } from '../../../constants/Color';
import BackgroundMain from '../../../components/Wallet/BackgroundMain';
import Card from '../../../components/Wallet/Card';
import Transaction from '../../../components/Wallet/Transaction';
import Menu from "../../../components/Menu";
import RenderHTML from 'react-native-render-html';
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { SafeAreaView } from 'react-native-safe-area-context';
import { BackIcon } from "../../../components/Svgs";



const WalletSkeleton = () => {
    return (
        <SkeletonPlaceholder borderRadius={4}>
            <View style={styles.skeletonContainer}>
                {/* Üst Bilgi (Hoşgeldin, Bakiye) */}
                <View style={styles.skeletonHeader}>
                    <SkeletonPlaceholder.Item width="60%" height={20} />
                    <SkeletonPlaceholder.Item width="20%" height={30} marginTop={10} />
                    <SkeletonPlaceholder.Item width="50%" height={15} marginTop={5} />
                </View>

                {/* Butonlar */}
                <View style={styles.skeletonButtons}>
                    {[1, 2, 3, 4].map((_, index) => (
                        <SkeletonPlaceholder.Item
                            key={index}
                            width={50}
                            height={50}
                            borderRadius={10}
                        />
                    ))}
                </View>

                {/* Son İşlemler Başlığı */}
                <SkeletonPlaceholder.Item width="40%" height={20} marginTop={20} />

                {/* İşlem Listesi */}
                {[1, 2, 3, 4].map((_, index) => (
                    <SkeletonPlaceholder.Item key={index} flexDirection="row" alignItems="center" marginTop={15}>
                        <SkeletonPlaceholder.Item marginLeft={10}>
                            <SkeletonPlaceholder.Item width={200} height={15} />
                            <SkeletonPlaceholder.Item width={150} height={15} marginTop={5} />
                        </SkeletonPlaceholder.Item>
                    </SkeletonPlaceholder.Item>
                ))}
            </View>
        </SkeletonPlaceholder>
    );
};



const windowHeight = Dimensions.get('window').height;
const windowWidth = Dimensions.get('window').width;

type WalletInfo = {
    balance: number;
};

type Transaction = {
    description: string;
    createdAt: string;
    amount: number;
    type: string;
    cashback: number;
};

type User = {
    firstName: string;
    walletId: string;
};

const WalletPage: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [statusToast, setStatusToast] = useState(false);
    const [typeToast, setTypeToast] = useState('');
    const [subtitleToast, setSubTitleToast] = useState('');
    const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [user, setUser] = useState<User>({ firstName: '', walletId: '' });
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [refreshing, setRefreshing] = useState(false);

    const [contractHTML, setContractHTML] = useState<string | null>(null);



    const navigation = useNavigation<screens>();

    useEffect(() => {
        checkWallet();
        getUserData();
        getTransactions();
    }, []);

    const fetchContract = async () => {
        try {
            const res = await get('/wallet/contract');
            if (res.type === 'success') {
                setContractHTML(res.contractHTML);
                setShowModal(true); // Sözleşme modalını aç
            } else {
                startToast('Sözleşme yüklenemedi.', 'error');
            }
        } catch (error) {
            console.error('Sözleşme yükleme hatası:', error);
            startToast('Bir hata oluştu.', 'error');
        }
    };
    

    const getTransactions = () => {
        get('wallet/transactions')
            .then((res: any) => {
                if (res.type === 'success') {
                    setTransactions(res.transactions);
                } else {
                    console.error('İşlem geçmişi alınamadı:', res.message);
                }
            })
            .catch((error) => {
                console.error('Hata:', error);
            });
    };

    const checkWallet = () => {
        get('wallet/check-wallet')
            .then((res: any) => {
                if (res.type === 'success') {
                    setWalletInfo(res.walletInfo);
                } else {
                    // Eğer kullanıcı cüzdana sahip değilse sözleşmeyi getir
                    fetchContract();
                }
            })
            .catch((error) => {
                console.error('Hata:', error);
            });
    };
    

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const getUserData = () => {
        get("/users/profile")
            .then((res: any) => {
                if (res.type == "success") {
                    setUser(res.user);
                    setLoading(false);
                } else {
                    startToast(res.error, "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 1500);
                }
            })
            .catch(() => {
                startToast("Bir şeyler ters gitti.", "error");
                setTimeout(() => {
                    navigation.pop();
                }, 1500);
            });
    };

    const createWallet = () => {
        setLoading(true);
        post('wallet/create-wallet')
            .then((res: any) => {
                if (res.type === 'success') {
                    startToast('Cüzdan başarıyla oluşturuldu.', "success");
                    checkWallet();
                } else {
                    startToast('Cüzdan oluşturulurken bir hata oluştu.', 'error');
                }
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const handleRefresh = () => {
        setRefreshing(true);
        checkWallet();
        getTransactions();
        setRefreshing(false);
    };

      const goBack = () => {
        navigation.goBack();
      };
    
      const toggleMenu = () => {
        setMenu(prevMenu => !prevMenu);
    };

    const [menu, setMenu] = React.useState(false);

      

    return (
        <SafeAreaView style={styles.container}>
             <Menu
                                              menuColor={pink}
                                              navigation={navigation}
                                              menuStatus={menu}
                                              onMenu={(set: boolean) => {
                                                  setMenu(set);
                                              }}
                                          />
                                <View style={styles.leftTouch} >
                                  <TouchableOpacity onPress={goBack} style={styles.touch}>
                                    <BackIcon size={25} color={white} />
                                  </TouchableOpacity>
                                  <TouchableOpacity onPress={toggleMenu} style={styles.touch}>
                                    <Image
                                      style={styles.specialTop}
                                      resizeMode="contain"
                                      source={require('../../../assets/menu/menuDikeyPink.png')}
                                    />
                                  </TouchableOpacity>
                              </View>

            {walletInfo ? (
                <View style={styles.main}>
                              
                    <BackgroundMain userName={user.firstName} walletid={user.walletId} balance={walletInfo.balance} handleRefresh={handleRefresh} />
                    <Card />
                    <View style={styles.gecis}>
                        <Text style={styles.gecistext}>Son 10 işleminiz</Text>
                        {transactions.length > 0 ? (
                            <ScrollView showsVerticalScrollIndicator={false} style={styles.scroll}>
                                {transactions.slice(-10).reverse().map((transaction, index) => (
                                    <Transaction
                                        key={index}
                                        icon={transaction.type === 'holly_points_exchange' 
                                            ? require('../../../assets/root/hollyPuanLogo.png') // Holly Points ikonu
                                            : require('../../../assets/root/pay.png') // Normal ödeme ikonu
                                        }
                                        description={transaction.description}
                                        date={transaction.createdAt}
                                        amount={transaction.amount}
                                        type={transaction.type}
                                        cashback={transaction.cashback}
                                    />
                                ))}
                            </ScrollView>
                        ) : (
                            <Text style={styles.noTransactionsText}>Geçmiş işleminiz bulunmamaktadır.</Text>
                        )}
                    </View>
                </View>
            ) : (
                <View >
                     <WalletSkeleton />   
                </View>
            )}
            {statusToast && <Toast type={typeToast} subtitle={subtitleToast} status={false} successColor={''} />}
            <Modal
    animationType="slide"
    transparent={true}
    visible={showModal}
    onRequestClose={() => {
        setShowModal(false);
    }}
>
    <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
        <ScrollView>
        {contractHTML && (
    <RenderHTML
        contentWidth={windowWidth * 0.9}
        source={{ html: contractHTML }}
        tagsStyles={{
            body: { 
                backgroundColor: 'white',  // Arka plan siyah
                color: 'black'             // Metin rengi beyaz
            }
        }}
        defaultTextProps={{
            style: { color: 'black' }   // Varsayılan metin rengi beyaz
        }}
    />
)}
                        </ScrollView>
            <View style={styles.modalButtonContainer}>
                <TouchableOpacity
                    style={styles.createWalletButton}
                    onPress={() => {
                        setShowModal(false);
                        createWallet(); // Cüzdan oluşturma işlevi
                    }}
                >
                    <Text style={styles.modalButtonText}>Kabul Et</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                        setShowModal(false);
                        navigation.goBack(); // Geri dön
                    }}
                >
                    <Text style={styles.modalButtonText}>İptal</Text>
                </TouchableOpacity>
            </View>
        </View>
    </View>
</Modal>

        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    skeletonContainer: {
        right: 20,
        left: 20,
     },
     leftTouch: {
        flexDirection: "row", // Horizontal alignment
        justifyContent: "space-between", // Back on left, Menu on right
        alignItems: "center",
        width: '100%',
        backgroundColor: pink
      },
    skeletonHeader: {
        alignItems: 'flex-start',
        marginTop: 10,
        marginBottom: 5,
    },
    skeletonButtons: {
        flexDirection: "row",
        justifyContent: "space-around",
        marginVertical: 10,
        right: 20
    },
    container: {
        flex: 1,
        backgroundColor: kremrengi,
    },
    gecis: {
        top: -50,
        width: '100%',
        justifyContent: 'flex-start',
        paddingLeft: 15,
        paddingRight: 15,
    },
    touch: {

        padding: 20,
    },
    specialTop: {
        width: 40,
        height: 40,
        borderRadius: 5,
    },
    gecistext: {
        paddingTop: 15,
        paddingBottom: 15,
        paddingLeft: 10,
        fontSize: 16,
        fontWeight: '700',
        zIndex: -1,
    },
    bottombar: {
        zIndex: -110,
        position: 'absolute',
        bottom: 0,
    },
    main: {
        alignItems: 'center',
        flex:1
    },
    card: {
        zIndex: 0,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        width: '100%',
        height: '100%'

    },
    loadingGif: {
        width: 200,
        height: 50,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 10,
        width: '90%',
        height: '70%',
    },
    contractScroll: {
        flex: 1,
    },
    contractText: {
        fontSize: 14,
        color: 'black',
        textAlign: 'justify',
    },
    loadingText: {
        textAlign: 'center',
        fontSize: 16,
        color: 'gray',
    },
    modalButtonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    createWalletButton: {
        backgroundColor: pink,
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        marginRight: 10,
        width: "45%",
    },
    cancelButton: {
        backgroundColor: '#6c757d',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        width: "45%",
    },
    modalButtonText: {
        color: white,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    textPosition: {
        textAlign: 'left',
        color: white,
        left: 0,
        top: 0,
        position: 'absolute',
    },
    iconeyeHide: {
        top: 6,
        left: 150,
        opacity: 0.5,
        height: 24,
        width: 24,
        position: 'absolute',
        overflow: 'hidden',
    },
    amount: {
        top: 27,
        left: 0,
        fontSize: 19,
    },
    saldoBalance: {
        top: 60,
        height: 63,
        left: 0,
    },
    info: {
        height: 123,
        left: 24,
        top: 0,
    },
    scroll: {
        maxHeight: windowHeight / 1.6,
        zIndex: -100,
      
    },
    noTransactionsText: {
        textAlign: 'center',
        marginVertical: 20,
        fontSize: 16,
        color: 'gray',
    }
});

export default WalletPage;

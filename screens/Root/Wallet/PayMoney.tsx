// PayMoney.js

import React, { useRef, useState, useEffect } from 'react';
import { Text, TouchableOpacity, View, StyleSheet, Alert, Modal, ActivityIndicator, StatusBar } from 'react-native';
import Background from '../../../components/Wallet/Background';
import NumericPad from 'react-native-numeric-pad';
import { black, white } from '../../../constants/Color';
import { BackIcon } from "../../../components/Svgs";
import { Camera, useCameraDevice, useCodeScanner } from 'react-native-vision-camera';
import { post } from '../../../networking/Server';
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView } from 'react-native-safe-area-context';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { Platform } from 'react-native';

const PayMoney = () => {
    const [amount, setAmount] = useState('0.00');
    const [showQRScanner, setShowQRScanner] = useState(false);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [qrContent, setQrContent] = useState('');
    const [loading, setLoading] = useState(false);
    const [hasPermission, setHasPermission] = useState(false);
    const navigation = useNavigation();
    const device = useCameraDevice('back');
    const camera = useRef(null);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    const numpadRef = useRef(null);

    useEffect(() => {
        checkCameraPermission();
    }, []);

    const checkCameraPermission = async () => {
        try {
            console.log('Kamera izni kontrolü başlatılıyor...');
            
            // Platform'a göre doğru izni seç
            const cameraPermission = Platform.select({
                ios: PERMISSIONS.IOS.CAMERA,
                android: PERMISSIONS.ANDROID.CAMERA,
                default: PERMISSIONS.ANDROID.CAMERA,
            });
            
            // İzin durumunu kontrol et
            const result = await check(cameraPermission);
            console.log('Kamera izni durumu:', result);
            
            switch (result) {
                case RESULTS.GRANTED:
                    console.log('Kamera izni verilmiş');
                    setHasPermission(true);
                    break;
                    
                case RESULTS.DENIED:
                    console.log('Kamera izni reddedilmiş, yeniden isteniyor...');
                    const requestResult = await request(cameraPermission);
                    console.log('İzin isteme sonucu:', requestResult);
                    
                    if (requestResult === RESULTS.GRANTED) {
                        setHasPermission(true);
                    } else {
                        setHasPermission(false);
                        showCameraPermissionAlert();
                    }
                    break;
                    
                case RESULTS.BLOCKED:
                case RESULTS.UNAVAILABLE:
                    console.log('Kamera izni engellendi veya kullanılamıyor');
                    setHasPermission(false);
                    showCameraPermissionAlert();
                    break;
                    
                default:
                    setHasPermission(false);
                    break;
            }
        } catch (error) {
            console.error('İzin kontrol hatası:', error);
            Alert.alert('Hata', 'Kamera izni kontrolü sırasında bir hata oluştu');
        }
    };
    
    const showCameraPermissionAlert = () => {
        Alert.alert(
            'Kamera Erişimi Gerekli',
            'QR kodu tarayabilmek için kamera erişimine izin vermelisiniz.',
            [
                {
                    text: 'Ayarlara Git',
                    onPress: () => Linking.openSettings(),
                },
                {
                    text: 'İptal',
                    style: 'cancel',
                },
            ]
        );
    };

    const handleAmountChange = (value: string) => {
        setAmount(value);
    };

    const handleButtonPress = async () => {
        if (amount !== '') {
            if (hasPermission) {
                setShowQRScanner(true);
            } else {
                checkCameraPermission();
            }
        }
    };

    const handleQRCodeScanned = (codes) => {
        if (codes && codes.length > 0 && codes[0].value) {
            const qrCodeContent = codes[0].value;
            setQrContent(qrCodeContent);
            setShowQRScanner(false);
            setShowConfirmationModal(true);
        }
    };

    const codeScanner = useCodeScanner({
        codeTypes: ['qr'],
        onCodeScanned: handleQRCodeScanned
    });

    const handleConfirmPayment = async () => {
        const amountValue = parseFloat(amount).toFixed(2);
        const requestData = {amount: amountValue, qrCodeContent: qrContent};

        setLoading(true);
        try {
            const response: any = await post('wallet/pay', requestData);

            if (response.type === 'success') {
                Alert.alert('Ödeme Başarılı', 'Ödeme işlemi başarıyla tamamlandı.', [
                    {
                        text: 'Tamam',
                        onPress: () => navigation.goBack()
                    }
                ]);
            } else {
                if (response.message === 'Yetersiz bakiye.') {
                    Alert.alert('Hata', 'Yetersiz bakiye.');
                } else {
                    Alert.alert('Hata', 'Ödeme işlemi sırasında bir hata oluştu.');
                }
            }
        } catch (error) {
            console.error('Ödeme işlemi sırasında bir hata oluştu:', error);
            Alert.alert('Hata', 'Ödeme işlemi sırasında bir hata oluştu.');
        } finally {
            setLoading(false);
            setShowConfirmationModal(false);
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar backgroundColor="#61dafb" />

            {/* Background bileşenine gerekli prop'ları ileterek çağırma */}
            <Background amount={amount} />
            <NumericPad
                numLength={8}
                buttonSize={60}
                activeOpacity={0.1}
                onValueChange={handleAmountChange}
                allowDecimal={false}
                ref={numpadRef}
                rightBottomButton={<BackIcon size={28} color={black} />}
                onRightBottomButtonPress={() => {numpadRef.current?.clear()}}
            />
            <View style={styles.butonlar}>
                <TouchableOpacity
                    style={[styles.button, { opacity: amount === '' ? 0.5 : 1 }]}
                    onPress={handleButtonPress}
                    disabled={amount === ''}
                >
                    <Text style={styles.buttonText}>Ödeme Yap</Text>
                </TouchableOpacity>
            </View>

            <Modal
                animationType="slide"
                transparent={true}
                visible={showQRScanner}
                onRequestClose={() => setShowQRScanner(false)}
            >
                <View style={styles.modalContainer}>
                    <TouchableOpacity style={styles.closeButton} onPress={() => setShowQRScanner(false)}>
                        <Text style={styles.closeButtonText}>×</Text>
                    </TouchableOpacity>
                    
                    {device && hasPermission ? (
                        <View style={styles.cameraContainer}>
                            <Camera
                                ref={camera}
                                style={styles.camera}
                                device={device}
                                isActive={showQRScanner}
                                codeScanner={codeScanner}
                                enableZoomGesture={false}
                                photo={false}
                                video={false}
                                audio={false}
                            />
                            <View style={styles.scannerOverlay}>
                                <View style={styles.scannerFrame} />
                            </View>
                        </View>
                    ) : (
                        <View style={styles.cameraPlaceholder}>
                            <Text style={styles.cameraPlaceholderText}>Kamera kullanılamıyor</Text>
                        </View>
                    )}
                </View>
            </Modal>

            <Modal
                animationType="slide"
                transparent={true}
                visible={showConfirmationModal}
                onRequestClose={() => setShowConfirmationModal(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={[styles.modalText, { fontWeight: 'bold' }]}>Ödemeyi Onaylıyor musunuz?</Text>
                        <Text style={styles.modalText}>{qrContent}</Text>
                        <Text style={styles.modalText}>Tutar: <Text style={styles.boldText}>{amount} ₺</Text></Text>

                        <TouchableOpacity style={styles.button} onPress={handleConfirmPayment}>
                            <Text style={styles.buttonText}>Onayla</Text>
                        </TouchableOpacity>

                    </View>
                    {loading && (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator size="large" color="#dc1354" />
                            <Text style={styles.loadingText}>İşlem yapılıyor...</Text>
                        </View>
                    )}
                </View>
            </Modal>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
    },
    button: {
        backgroundColor: "#dc1354",
        padding: 15,
        top: 40,
        alignItems: 'center',
        alignContent: 'center',
        borderRadius: 12,
        width: '100%'
    },
    butonlar: {
        flexDirection: 'row',
        width: '80%',
        justifyContent: 'center'
    },
    buttonText: {
        color: white,
        fontWeight: '700',
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        flexDirection: 'row'
    },
    cameraContainer: {
        height: '40%',
        width: '80%',
        borderRadius: 12,
        overflow: 'hidden',
        position: 'relative'
    },
    camera: {
        flex: 1,
        width: '100%',
        height: '100%'
    },
    cameraPlaceholder: {
        height: '40%',
        width: '80%',
        backgroundColor: 'gray',
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center'
    },
    cameraPlaceholderText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold'
    },
    scannerOverlay: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scannerFrame: {
        width: 200,
        height: 200,
        borderWidth: 2,
        borderColor: '#FFF',
        backgroundColor: 'transparent',
    },
    modalContent: {
        backgroundColor: 'white',
        width: '80%',
        padding: 20,
        borderRadius: 12,
        alignItems: 'center',
    },
    modalText: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 10
    },
    boldText: {
        fontWeight: 'bold',
    },
    loadingContainer: {
        position: 'absolute',
        top: '30%',
        width: '80%',
        height: '40%',
        alignItems: 'center',
        backgroundColor: white,
        borderRadius: 20,
        justifyContent: 'center',
    },
    loadingText: {
        marginTop: 10,
        color: '#dc1354',
    },
    closeButton: {
        position: 'absolute',
        top: 20,
        right: 20,
        zIndex: 1,
        backgroundColor: 'white',
        borderRadius: 15,
        width: 30,
        height: 30,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButtonText: {
        fontSize: 20,
        fontWeight: 'bold',
        color: 'black',
        textAlignVertical: 'center',
        textAlign: 'center',
    },
});

export default PayMoney;
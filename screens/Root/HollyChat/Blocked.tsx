import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFive from "../../../components/HeaderFive";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Menu from "../../../components/Menu";
import { Spinner } from "native-base";
import { green_t4, blue_t4, white, blue_t1, kremrengi } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import { get, post, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import BottomBar from "../../../components/BottomBar";
import { MainStore } from "../../../stores/MainStore";

const Blocked: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingButton, setLoadingButton] = React.useState(false);

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- BLOCKED INFO -- //
    const [blockedList, setBlockedList] = React.useState([]);

    const getBlockedList = () => {
        get('chat/blocked-list').then((res: any) => {
            if (res.type == "success") {
                setBlockedList(res.blockedList);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getBlockedList();
    }, []);

    const removeBlock = (id: number) => {
        setLoadingButton(true);
        post("chat/remove-block", {
            id
        }).then((res: any) => {
            if (res.type == "success") {
                startToast(MainStore.language.removed_ban, "success");
                setLoading(true);
                getBlockedList();
            } else {
                startToast(res.error, "error");
            }
            setLoadingButton(false);
        })
    }

    return (
        <View
            style={styles.main}
        >

            {/* MENU */}
            <Menu
                menuColor={blue_t4}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* HEADER */}
                <HeaderFive
                    logoStyle={styles.logoStyle}
                    searchIconColor={blue_t4}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menuBlue.png')}
                    backIcon={blue_t1}
                    navigation={navigation}
                    logo={require('../../../assets/root/hollyChat.png')}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                />
                <SafeAreaView>
                    <View style={styles.toastView}>
                        <Toast
                            type={typeToast}
                            subtitle={subtitleToast}
                            status={statusToast}
                            successColor={green_t4}
                        />
                    </View>
                    {
                        loading ? < Spinner color={blue_t4} size={18} style={{ marginTop: 70 }} /> :
                            blockedList.length < 1 ?
                                <Text style={styles.noVariable}>{MainStore.language.no_ban_variable}</Text>
                                :
                                blockedList.map((item: any, index: number) => {
                                    return (
                                        <ImageBackground
                                            key={index}
                                            source={require('../../../assets/root/blockedBack.png')}
                                            style={styles.newFriendBack}
                                        >
                                            {/* PROFILE IMAGE */}
                                            <Image
                                                source={{ uri: getImageURL(item.image) }}
                                                style={styles.profile}
                                            />

                                            {/* NAME SURNAME */}
                                            <View style={styles.rightView}>
                                                <Text style={styles.rightText}>{`${item.firstName} ${item.lastName}`}</Text>
                                                <View style={styles.buttonsView}>

                                                    {/* ACCEPT */}
                                                    {loadingButton ? <Spinner color={white} size={18} mt={5} /> : (
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                removeBlock(item.id);
                                                            }}
                                                            style={styles.acceptButton}
                                                        >
                                                            <Text style={styles.accept}>{MainStore.language.remove_ban}</Text>
                                                        </TouchableOpacity>
                                                    )}
                                                </View>
                                            </View>
                                        </ImageBackground>
                                    )
                                })
                    }
                </SafeAreaView>
            </ScrollView>
            <BottomBar navigation={navigation} type={2} />
        </View>
    )
}
export default Blocked;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi},
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    noVariable:{
        textAlign: 'center',
        fontSize: 18,
        color: blue_t1,
        marginTop: 70,
        fontWeight: 'bold'
    },
    logoStyle: {
        height: 28,
        position: 'absolute',
        left: 45,
        width: 126,
        top: 22
    },
    newFriendBack: {
        marginTop: 70,
        width: Layout.screen.width / 1.1,
        height: Layout.screen.width / 4.5,
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center'
    },
    profile: {
        height: Layout.screen.width / 4.5,
        borderRadius: 100,
        left: -5,
        width: Layout.screen.width / 4.5,
    },
    rightView: {
        width: Layout.screen.width / 1.8,
        alignItems: 'center',
        height: Layout.screen.width / 6,
        justifyContent: 'space-between'
    },
    rightText: {
        fontSize: 15,
        fontWeight: 'bold',
        color: white
    },
    buttonsView: { flexDirection: 'row' },
    acceptButton: {
        borderRadius: 10,
        marginBottom: Layout.screen.width / 50,
        alignItems: 'center',
        justifyContent: 'center'
    },
    accept: {
        color: white,
        fontSize: 15,
        fontWeight: 'bold'
    }
});
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ImageBackground, FlatList } from 'react-native';
import { Button } from 'native-base';
import dayjs from 'dayjs';
import { blue_t1, white, yellow_t1, black_t2, gray_t1, red_t1, red_t2 } from '../../../constants/Color';


interface ChatPlusModalProps {
    isVisible: boolean;
    onClose: () => void;
    gifts: any[];
    onSelectGift: (gift: any) => void; // onSelectGift propunu ekledik
}

interface GiftType {
    id: number;
    giftType: number;
    type: number;
    name: string;
    quantity: number;
    date: string;
}

const ChatPlusModal: React.FC<ChatPlusModalProps> = ({ isVisible, onClose, gifts, onSelectGift }) => {
    const [selectedGift, setSelectedGift] = useState<GiftType | null>(null); // Seçilen hediye için state

    if (!isVisible) return null;


    const renderGiftItem = ({ item }: { item: GiftType }) => {
        if (item.quantity === 0) {
            return null;
        }
    
        const isGiftCard = item.giftType === 0;
        const giftName = isGiftCard
            ? item.type === 1 ? "Gold Card" 
            : item.type === 2 ? "Silver Card"
            : "Bronze Card"
            : item.name;
    
        const isSelected = selectedGift?.id === item.id;
    
        // Determine the image based on the type
        const cardImage =
            item.type === 1 ? require('../../../assets/gold_card.png') :
            item.type === 2 ? require('../../../assets/silver_card.png') :
            require('../../../assets/bronze_card.png');
    
        return (
            <TouchableOpacity 
                onPress={() => setSelectedGift({ ...item, quantity: 1 })} 
                style={[
                    styles.giftContainer,
                    isSelected && styles.selectedGiftContainer
                ]}
            >
                {isGiftCard ? (
                    <ImageBackground
                        source={cardImage} // Use the card image
                        style={styles.giftCardImage}
                    >
                        <View style={styles.giftCardContent}>
                            {/* Display gift name */}
                            <Text style={styles.giftCardName}>{giftName}</Text>
                            {/* Display value */}
                            <Text style={styles.giftCardValue}>{item.value}₺</Text>
                            {/* Display code */}
                            <Text style={styles.giftCardCode}>{item.code}</Text>
                        </View>
                    </ImageBackground>
                ) : (
                    <View style={styles.giftMessages}>
                        <ImageBackground 
                            source={require('../../../assets/root/muratmehmet.png')} 
                            style={styles.concertItemx1}
                        >
                            <Text style={styles.concertItemx1Text}>x 1</Text>
                        </ImageBackground>
                        <View style={styles.giftBubbleContainer}>
                            <Text style={styles.giftText}>
                                {giftName.length > 19 ? giftName.substring(0, 20) + "..." : giftName}
                            </Text>
                            <Text style={styles.giftDate}>{dayjs(item.date).format("DD.MM.YYYY")}</Text>
                            <Image 
                                style={styles.concertItemHollyTicketLogo} 
                                source={require('../../../assets/root/hollyticket.png')} 
                            />
                        </View>
                    </View>
                )}
            </TouchableOpacity>
        );
    };
    

    // ChatPlusModal.tsx

// Normalde: onSelectGift prop'u var
// Seçilen hediyeyi handleSendGift fonksiyonuyla iletirsin

const handleSendGift = () => {
    if (selectedGift) {
      onSelectGift(selectedGift); // <--- Bu ChatTwo'ya callback
      setSelectedGift(null);
      onClose();
    }
  };
  

    return (
        <View style={styles.modalBackground}>
            <View style={styles.modalContainer}>
                
                <Text style={styles.modalTitle}>Hediyeler</Text>
                {gifts.length < 1 ? (
                    <View style={styles.noGiftsView}>
                        <Text style={styles.noGiftText}>Mevcut hediyeniz bulunamadı.</Text>
                        <Button style={styles.sendButtonTrhee} onPress={onClose}>
                    <Text style={styles.sendButtonTextTwo}>İptal</Text>
                   </Button>
                    </View>
                    
                ) : (
                    <>
                        <FlatList
                            data={gifts}
                            horizontal={true}
                            showsHorizontalScrollIndicator={false}
                            renderItem={renderGiftItem}
                            keyExtractor={(item) => item.id.toString()}
                            contentContainerStyle={styles.giftList}
                        />
                        <Button 
                                style={[styles.sendButton, !selectedGift && styles.disabledButton]} 
                                onPress={handleSendGift} 
                                disabled={!selectedGift}
                            >
                                <Text style={styles.sendButtonText}>Gönder</Text>
                            </Button>
                        <Button style={styles.sendButtonTwo} onPress={onClose}>
                                <Text style={styles.sendButtonTextTwo}>İptal</Text>
                        </Button>
                    </>
                )}
            </View>
        </View>
    );
};


const styles = StyleSheet.create({
    modalBackground: {
        position: 'absolute',
        zIndex: 100,
        flex: 1,
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    disabledButton: {
        backgroundColor: gray_t1,
    },
    sendButton: {
        position: 'absolute',
        marginTop: 165,
        padding: 10,
        width: 130,
        left: 30,
        backgroundColor: blue_t1,
        borderRadius: 10,
    },
    sendButtonTwo: {
        position: 'absolute',
        right: 30,
        width: 130,
        marginTop: 165,
        padding: 10,
        backgroundColor: red_t1,
        borderRadius: 10,
    },
    sendButtonTrhee: {
        position: 'absolute',
        right: 50,
        width: 120,
        marginTop: 110,
        padding: 10,
        backgroundColor: red_t1,
        borderRadius: 10,
    },
    
    selectedGiftContainer: {
        borderWidth: 2,
        borderColor: blue_t1,
        borderRadius: 10,
        padding: 5
    },
    selectedGiftSendButton: {
        marginTop: 10,
        backgroundColor: blue_t1,
        borderRadius: 10,
        width: '100%',
        padding: 5
    },
    sendButtonText: {
        color: white,
        textAlign: 'center',
        fontSize: 16,
        fontWeight: 'bold',
    },
    sendButtonTextTwo: {
        color: white,
        textAlign: 'center',
        fontSize: 16,
        fontWeight: 'bold',
    },
    modalContainer: {
        width: '85%',
        backgroundColor: 'white',
        borderRadius: 10,
        height: 175,
        padding: 10,
        alignItems: 'center',
    },
    modalTitle: {
        fontSize: 18,
        marginBottom: 10,
        fontFamily: 'MADE TOMMY',
        color: gray_t1,
    },
    closeButton: {
        backgroundColor: blue_t1,
        top: -15,
        padding: 5,
        width: 35,
        height: 35,
        borderRadius: 50,
        position: 'absolute',
        right: -10,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButtonText: {
        color: 'white',
        fontSize: 16,
    },
    noGiftsView: {
        alignItems: 'center',
        marginBottom: 20,
    },
    noGiftText: {
        fontSize: 16,
        color: gray_t1,
    },
    giftList: {
        paddingVertical: 10,
    },
    giftContainer: {
        marginHorizontal: 10,
    },
    giftItemView: {
        padding: 10,
        borderRadius: 5,
        height: 90,
        width: 130,
        justifyContent: 'center',
    },
    giftItemText: {
        color: white,
        fontSize: 15,
        fontFamily: 'MADE TOMMY',
        textAlign: 'center',
    },
    concertItemx1: {
        width: 18.93,
        height: 24.75,
        position: 'absolute',
        top: 1,
        right: 10,
        alignItems: 'center',
    },
    concertItemx1Text: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 10,
    },
    concertItemHollyTicketLogo: {
        width: 70,
        height: 10,
        position: 'absolute',
        left: -24,
        transform: [{ rotate: '270deg' }],
    },
    giftMessages: {
        flexDirection: 'row',
        width: 145,
    },
    giftBubbleContainer: {
        borderColor: '#ffcc80',
        borderWidth: 1,
        borderRadius: 10,
        padding: 10,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 90,
    },
    giftText: {
        top: 6,
        fontSize: 11,
        textAlign: 'center',
        paddingLeft: 15,
        fontFamily: 'MADE TOMMY',
        color: gray_t1,
    },
    giftDate: {
        top: 12,
        fontSize: 11,
        textAlign: 'center',
        paddingLeft: 15,
        fontFamily: 'MADE TOMMY',
        color: gray_t1,
    },
    giftButtonsView: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginTop: 20,
    },
    giftCardButton: {
        backgroundColor: yellow_t1,
        padding: 10,
        borderRadius: 5,
        marginHorizontal: 10,
    },
    giftConcertView: {
        backgroundColor: blue_t1,
        padding: 10,
        borderRadius: 5,
        marginHorizontal: 10,
    },
    giftCardText: {
        color: 'white',
        fontSize: 14,
        fontFamily: 'MADE TOMMY',
    },
    giftCardImage: {
        width: 130,
        height: 90,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        overflow: 'hidden',
    },
    giftCardContent: {
        flex: 1,
        left: 10,
        top: 5,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 5,
    },
    giftCardCode: {
        fontSize: 10,
        color: white,
        marginBottom: 0,
        right: 10,
        textAlign: 'center',
    },
    giftCardName: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white,
        textAlign: 'center',
    },
    giftCardValue: {
        fontSize: 15,
        color: white,
        fontFamily: 'MADE TOMMY',
        marginTop: 5,
        marginBottom: 5,
        textAlign: 'center',
    },
    
});

export default ChatPlusModal;

import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView, View, TouchableOpacity, Text, Image, ImageBackground, Alert, KeyboardAvoidingView, Platform } from "react-native";
import { Bubble, GiftedChat, IMessage } from "react-native-gifted-chat";
import { socket } from "../../../networking/Socket";
import { HollyChatStoreInstance } from "../../../stores/HollyChatStore";
import { observer } from "mobx-react-lite";
import { black, blue_t1, white, yellow_t2 } from "../../../constants/Color";
import { BackIcon } from "../../../components/Svgs";
import { get, getImageURL } from "../../../networking/Server";
import Svg, { Path } from "react-native-svg";
import SnapView from "../../../components/SnapView"; // SnapView bileşenini import edin
import 'dayjs/locale/tr'

interface ChatTwoProps {
  route: {
    params?: {
      chatroomId?: string | number;
      partner?: {
        _id: number | string;
        name?: string;
        avatar?: string;
      };
    };
  };
  navigation: any;
}

const EmojiIcon = () => (
  <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
    <Path
      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-4 8c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm8 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-4 7c-2.33 0-4.31-1.46-5.11-3.5h10.22c-.8 2.04-2.78 3.5-5.11 3.5z"
      fill={blue_t1}
    />
  </Svg>
);

const SendIcon = () => (
  <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
    <Path
      d="M2 21l21-9L2 3v7l15 2-15 2v7z"
      fill={blue_t1}
    />
  </Svg>
);

const SingleTickIcon = ({ color = "#999" }) => (
  <Svg width={15} height={9} viewBox="0 0 480 270">
    <Path
      d="M472.62,28.62c-1.25,1.1-2.56,2.15-3.74,3.32-78.53,78.37-157.05,156.75-235.55,235.15-1.28,1.28-2.23,2.91-3.16,4.14-38.33-38.3-75.92-75.86-113.01-112.91,8.91-8.9,18.18-18.17,27.55-27.53,27.35,27.32,55.56,55.5,84.12,84.02C300.29,142.83,371.13,71.46,442.05,0c10.38,9.41,20.47,18.55,30.56,27.7v.92Z"
      fill="white"
      stroke={color}
    />

  </Svg>
);

const DoubleTickIcon = ({ color = "#ff0000" }) => (
  <Svg width={16} height={9} viewBox="0 0 480 270">
    <Path
      d="M472.62,28.62c-1.25,1.1-2.56,2.15-3.74,3.32-78.53,78.37-157.05,156.75-235.55,235.15-1.28,1.28-2.23,2.91-3.16,4.14-38.33-38.3-75.92-75.86-113.01-112.91,8.91-8.9,18.18-18.17,27.55-27.53,27.35,27.32,55.56,55.5,84.12,84.02C300.29,142.83,371.13,71.46,442.05,0c10.38,9.41,20.47,18.55,30.56,27.7v.92Z"
      fill="white"
      stroke={color}
    />
    <Path
      d="M0,157.62c9.2-9.44,18.39-18.89,27.28-28.02,38.38,38.31,76.07,75.93,113.49,113.29-8.86,8.83-18.16,18.09-27.7,27.59-.92-.87-2.25-2.09-3.53-3.37C74.3,231.96,39.07,196.8,3.82,161.66,2.66,160.51,1.28,159.58,0,158.54c0-.31,0-.61,0-.92Z"
      fill="white"
      stroke={color}
    />
    <Path
      d="M229.12,155.04c-9.2-9.2-18.42-18.41-27.29-27.28,42.22-42.14,84.59-84.44,126.52-126.29,8.77,8.75,18.14,18.1,27.31,27.26-41.75,41.67-84.13,83.98-126.54,126.31Z"
      fill="white"
      stroke={color}
    />
  </Svg>
);

// Saat simgesi (pending için)
const ClockIcon = ({ color = "#999" }) => (
  <Svg width={13} height={13} viewBox="0 0 13 13">
    <Path
      d="M6.5,0 C2.91,0 0,2.91 0,6.5 C0,10.09 2.91,13 6.5,13 C10.09,13 13,10.09 13,6.5 C13,2.91 10.09,0 6.5,0 Z M6.5,11.7 C3.63,11.7 1.3,9.37 1.3,6.5 C1.3,3.63 3.63,1.3 6.5,1.3 C9.37,1.3 11.7,3.63 11.7,6.5 C11.7,9.37 9.37,11.7 6.5,11.7 Z M6.5,3.25 C6.13,3.25 5.85,3.53 5.85,3.9 L5.85,6.5 C5.85,6.71 5.95,6.91 6.11,7.04 L8.19,8.45 C8.48,8.65 8.88,8.58 9.08,8.29 C9.28,8 9.21,7.6 8.92,7.4 L7.15,6.18 L7.15,3.9 C7.15,3.53 6.87,3.25 6.5,3.25 Z"
      fill={color}
    />
  </Svg>
);

// Özel gönderme butonunu render eden bileşen
const RenderSend = (props: any) => {
  const { text, onSend } = props;
  return (
    <View style={styles.sendContainer}>
      <TouchableOpacity onPress={() => console.log("Emoji pressed")} style={styles.emojiButton}>
        <EmojiIcon />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          if (text && text.trim().length > 0) {
            onSend({ text: text.trim() }, true);
          }
        }}
        style={styles.sendButton}
      >
        <SendIcon />
      </TouchableOpacity>
    </View>
  );
};

const ChatTwo: React.FC<ChatTwoProps> = observer(({ route, navigation }) => {
  const { chatroomId, partner } = route.params || {};
  const currentUser = HollyChatStoreInstance.user?.id ;

  console.log('gelen',chatroomId)

  const [text, setText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);

  const [partnerSnaps, setPartnerSnaps] = useState<any[]>([]);
  const [snapViewVisible, setSnapViewVisible] = useState(false);

  const currentConversation = HollyChatStoreInstance.conversations.find(
    (conv) => conv.id.toString() === chatroomId?.toString()
  );

  const giftedChatMessages: IMessage[] = currentConversation?.messages.map((msg: any) => {
    const messageId = msg._id || msg.id || Math.random().toString();
    const senderId = msg.senderId || msg.user?._id;

    // GiftedChat için status bayraklarını ayarla
    // messageStatus değerine göre doğru durum bayrağını ata
    let sent = false;
    let received = false;
    let pending = false;

    switch(msg.messageStatus) {
      case 'pending':
        pending = true;
        break;
      case 'sent':
        sent = true;
        break;
      case 'delivered':
        sent = true;
        received = true;
        break;
      case 'read':
        sent = true;
        received = true;
        break;
      default:
        sent = true; // Varsayılan olarak en azından gönderildi kabul et
    }

    return {
      _id: messageId,
      text: msg.text || "",
      createdAt: new Date(msg.createdAt || Date.now()),
      user: {
        _id: senderId?.toString(),
        name: msg.senderName || "",
      },
      // GiftedChat tarafından beklenen durum bayrakları
      sent: sent,
      received: received,
      pending: pending,
      // Özel durum alanını da tut (özel renderTicks için)
      messageStatus: msg.messageStatus,
    };
  }) || [];

  useEffect(() => {
    socket.on("chat_room_created", ({ roomId, senderId, receiverId }) => {
      console.log('soketdevrede', roomId, senderId, receiverId  )
      // Eğer biz göndericiyse ve ID'ler eşleşiyorsa
      if (senderId?.toString() === currentUser?.toString() && 
      receiverId?.toString() === partner?._id?.toString()) {
        
        console.log(`Yeni oluşturulan oda id: ${roomId}`);
        
        // Yeni oluşturulan odaya katıl
        socket.emit("join room", roomId);
        
        // Eğer route params'ta chatroomId yoksa (ilk mesaj durumu)
        if (!chatroomId) {
          // Bu noktada state'i güncelleyebilirsiniz
          // Ya da navigation.setParams ile route parametrelerini güncelleyebilirsiniz
          navigation.setParams({ chatroomId: roomId });
        }
      }
    });

    if (chatroomId) {
      socket.emit("join room", chatroomId);
    }
    return () => {
      if (chatroomId) {
        socket.emit("leave room", chatroomId);
      }
      socket.off("chat_room_created");
    };
  
  }, [chatroomId, currentUser, partner,]);

  const handleInputTextChanged = (inputText: string) => {
    setText(inputText);

    if (!isTyping && inputText.length > 0) {
      setIsTyping(true);
      socket.emit("is_typing", {
        roomId: chatroomId,
        userId: currentUser
      });
    } else if (isTyping && inputText.length === 0) {
      setIsTyping(false);
      socket.emit("stop_typing", {
        roomId: chatroomId,
        userId: currentUser
      });
    }
  };

  useEffect(() => {

    

    const handleUserTyping = ({ userId, roomId }: { userId: string | number, roomId: string | number }) => {
      if (userId?.toString() !== currentUser?.toString() && roomId?.toString() === chatroomId?.toString()) {
        setOtherUserTyping(true);
      }
    };

    const handleUserStopped = ({ userId, roomId }: { userId: string | number, roomId: string | number }) => {
      if (userId?.toString() !== currentUser?.toString() && roomId?.toString() === chatroomId?.toString()) {
        setOtherUserTyping(false);
      }
    };

    socket.on("user_typing", handleUserTyping);
    socket.on("user_stopped", handleUserStopped);

    return () => {
      socket.off("user_typing", handleUserTyping);
      socket.off("user_stopped", handleUserStopped);
    };
  }, [currentUser, chatroomId]);

  const onSend = (newMessages: IMessage[] = []) => {
    newMessages.forEach((msg) => {
      socket.emit("new message", {
        senderId: currentUser,
        receiverId: partner?._id,
        chatRoomId: chatroomId,
        text: msg.text,
        messageStatus: "sent",
        sent: "sent",
      });
    });
    setText("");
    setIsTyping(false);
    socket.emit("stop_typing", { roomId: chatroomId, userId: currentUser });
  };


  const renderAvatar = () => null;

  useEffect(() => {
    if (partner && partner._id) {
      get(`chat/snap-getir?id=${partner._id}`)
        .then((response: any) => {
          if (response.type === "success") {
            setPartnerSnaps(response.snaps);
          } else {
            console.error("Snap'ler alınamadı:", response.error);
          }
        })
        .catch((err) => {
          console.error("Snap'ler getirilirken hata:", err);
        });
    }
  }, [partner]);


  const renderTicks = (currentMessage: any) => {
    // Sadece kendi gönderdiğimiz mesajlara uygula
    if (currentMessage.user._id !== currentUser?.toString()) {
      return null;
    }

    // Gri renk
    const greyColor = "#999";

    // Mesaj durumuna göre ilgili ikonu göster
    if (currentMessage.pending) {
      return (
        <View style={styles.tickContainer}>
          <ClockIcon color={greyColor} />
        </View>
      );
    } else if (currentMessage.sent && !currentMessage.received) {
      // Gönderildi durumu - tek tik
      return (
        <View style={styles.tickContainer}>
          <SingleTickIcon color={greyColor} />
        </View>
      );
    } else if (currentMessage.received) {
      // İletildi veya okundu durumu - çift tik
      return (
        <View style={styles.tickContainer}>
          <DoubleTickIcon color={greyColor} />
        </View>
      );
    }

    // Hiçbir durum yoksa null döndür
    return null;
  };


  const renderBubble = (props: any) => {
    return (
      <Bubble
        {...props}
        onLongPress={() => {
          if (props.currentMessage.user._id === currentUser?.toString()) {
            Alert.alert(
              "Mesajı Sil",
              "Bu mesajı silmek istediğinizden emin misiniz?",
              [
                { text: "İptal", style: "cancel" },
                {
                  text: "Sil",
                  onPress: () => {
                    socket.emit("delete message", {
                      messageId: props.currentMessage._id,
                      chatRoomId: chatroomId,
                      userId: currentUser,
                    });
                  },
                  style: "destructive",
                },
              ]
            );
          }
        }}
      />
    );
  };

  function markMessagesAsDelivered() {
    if (!currentConversation || !currentUser) return;

    const messagesToMark = currentConversation.messages.filter(msg => {
      const senderId = msg.senderId || msg.user?._id;
      return (
        senderId?.toString() !== currentUser.toString() &&
        msg.messageStatus === 'sent'
      );
    });

    if (messagesToMark.length > 0) {
      messagesToMark.forEach(msg => {
        socket.emit('message_delivered', {
          messageId: msg._id || msg.id,
          chatRoomId: chatroomId,
          receiverId: currentUser
        });
      });
    }
  }

  // Mesajları okundu olarak işaretle
  function markMessagesAsRead() {
    if (!currentConversation || !currentUser) return;

    const unreadMessages = currentConversation.messages.filter(msg => {
      const senderId = msg.senderId || msg.user?._id;
      return (
        senderId?.toString() !== currentUser.toString() &&
        msg.messageStatus !== 'read'
      );
    });

    if (unreadMessages.length > 0) {

      const messageIds = unreadMessages.map(msg => msg._id || msg.id);

      socket.emit('message_read', {
        messageIds,
        chatRoomId: chatroomId,
        receiverId: currentUser
      });
    }
  }

  // Mesaj durumlarını güncelle
  useEffect(() => {
    if (currentConversation && chatroomId) {
      markMessagesAsDelivered();
      markMessagesAsRead();
    }
  }, [currentConversation?.messages.length, chatroomId]);


  return (
    <SafeAreaView style={styles.mainContainer}>
      <ImageBackground
        source={require("../../../assets/peakpx.jpg")}
        style={styles.backgroundImage}
      >
        {/* Header Component */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <BackIcon size={25} color={blue_t1} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (partnerSnaps.length > 0) {
                setSnapViewVisible(true);
              }
            }}
          >
            <Image
              source={{ uri: getImageURL(partner?.avatar ? partner.avatar : "defaultUser.webp") }}
              style={[
                styles.headerAvatar,
                partnerSnaps.length > 0 && { borderColor: yellow_t2, borderWidth: 2 }
              ]}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{partner?.name}</Text>
        </View>
        
        <GiftedChat
          messages={giftedChatMessages}
          onSend={onSend}
          user={{ _id: currentUser?.toString() || '' }}
          text={text}
          onInputTextChanged={handleInputTextChanged}
          isTyping={otherUserTyping}
          placeholder="Mesaj yaz..."
          timeFormat="HH:mm"
          messagesContainerStyle={{ backgroundColor: "transparent" }}
          showAvatarForEveryMessage={true}
          renderAvatar={renderAvatar}
          renderAvatarOnTop={false}
          // @ts-ignore - renderTicks is a custom prop
          renderTicks={renderTicks}
          renderBubble={renderBubble}
          renderSend={(props) => <RenderSend {...props} />}
        />

        
        {snapViewVisible && (
          <SnapView
            snaps={partnerSnaps}
            isVisible={snapViewVisible}
            onClose={() => setSnapViewVisible(false)}
          />
        )}
      </ImageBackground>
    </SafeAreaView>
  );
});

export default ChatTwo;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: white,
  },
  backgroundImage: {
    flex: 1,
    resizeMode: "cover",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: white,
  },
  tickContainer: {
    marginRight: 6,
    marginBottom: 8,
    alignSelf: 'flex-end',
  },
  headerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginLeft: 15,
  },
  headerTitle: {
    marginLeft: 15,
    fontSize: 16,
    color: blue_t1,
    fontWeight: "bold",
  },
  sendContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 10,
    paddingBottom: 5,
  },
  emojiButton: {
    marginRight: 8,
  },
  sendButton: {
    padding: 5,
  },
});
import React from "react";
import { StyleSheet, View, ImageBackground, ScrollView, Image, Text, TouchableOpacity, KeyboardAvoidingView, AppState, Platform, FlatList } from "react-native";
import { shadow } from "../../../constants/Shadow";
import { black, kremrengi, black_t3, black_t4, blue_t1, blue_t4, green_t5, gray_t4, green_t1, red_t2, white, yellow_t1, yellow_t2, gray_t1, black_t2 } from "../../../constants/Color";
import HeaderFive from "../../../components/HeaderFive";
import { useNavigation } from "@react-navigation/native";
import Menu from "../../../components/Menu";
import Layout from "../../../constants/Layout";
import { Button, Input, Spinner } from "native-base";
import { MotiView } from "moti";
import { screens } from "../../../navigation";
import { SafeAreaView } from "react-native-safe-area-context";
import { get, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import GiftCardItem from "../../../components/GiftCardItem";
import { socket } from "../../../networking/Socket";
import HollySnapModal from "../../../components/HollySnapModal";
import { MainStore } from "../../../stores/MainStore";
const dayjs = require('dayjs');

const Chat: React.FC = (props: any) => {

    socket.connect();

    const [socketControl, setSocketControl] = React.useState(false);

    // -- APP STATE STATUS -- // IS OTHER APPS OR PHONE HOME PAGE LK.
    const [appState, setAppState] = React.useState("active");

    const [userId] = React.useState(props.route?.params?.id);

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- SCROLL REF -- //
    const scrollRef: any = React.useRef();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- ON LONG PRESS SENDER TOUCH VIEW -- //
    const [deleteAnswer, setDeleteAnswer] = React.useState(false);
    const [reply, setReply] = React.useState("");
    const [replyId, setReplyId] = React.useState(null);

    // -- PLUS DESIGN -- //
    const [plus, setPlus] = React.useState(false);

    // -- MESSAGES -- //
    const [messages, setMessages]: any = React.useState([{ messageType: '', messageContent: '', isSender: true, isDeleted: false, image: '' }]);
    const [sender, setSender]: any = React.useState({});
    const [receiver, setReceiver] = React.useState({ id: 0, image: '' });
    const [messageContent, setMessageContent]: any = React.useState(null);
    const [gifts, setGifts]: any = React.useState([]);
    const [copySelectedGiftId, setCopySelectedGiftId] = React.useState({ id: 0, giftType: 0 })
    const [selectedGiftId, setSelectedGiftId]: any = React.useState();

    // -- DELETE / RECEIVE -- //
    const [indexDR, setIndexDR] = React.useState(0);

    // -- HOLLY SNAP -- //
    const [snapModal, setSnapModal] = React.useState(false);
    const [snapArray, setSnapArray] = React.useState([]);
    const [friends, setFriends] = React.useState([]);

    // -- CONTROL PLUS BUTTON -- //
    const [controlPlus, setControlPlus] = React.useState(false);

    // -- SCROLL VIEW CONTROL HEADER STATE -- //
    const [header, setHeader] = React.useState(false);
    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    // -- SOCKET IS LISTENING WHEN SEND MESSAGE -- //
    React.useEffect(() => {
        if (socket.connected && !socketControl) {
            setSocketControl(true);
        }
    }, [socket.connected]);

    const handleGetMessage = (object: any) => {
        setMessages([...messages, object]);
    }

    React.useEffect(() => {
        getConversation();
        getChatInfo();
    }, [])

    React.useEffect(() => {
        const listener = AppState.addEventListener("change", handleAppStateChange);

        socket.on("message received", (res) => {
            if (res.receiverId == sender.id || res.senderId == sender.id) {
                let object = res;
                if (object.senderId == sender.id) {
                    object.isSender = true;
                    object.image = sender.image;
                } else {
                    object.isSender = false;
                    object.image = receiver.image;
                }
                handleGetMessage(object);
            }
        })

        socket.on("message deleted", (res: any) => {
            const deletedMessageIndex = messages.findIndex((message: any) => message.id === res.id);

            if (deletedMessageIndex !== -1) {
                const updatedMessages = [...messages];
                updatedMessages[deletedMessageIndex].isDeleted = true;
                setMessages(updatedMessages);
            }
        });

        socket.on("gift rejected", (giftReply) => {
            const { giftId } = giftReply;
    
            // Hediye reddedildiyse geri ekle
            setGifts((prevGifts) => {
                const giftExists = prevGifts.some((gift) => gift.id === giftId);
                if (!giftExists) {
                    return [...prevGifts, giftReply.messageContent];
                }
                return prevGifts;
            });
        });
    

        socket.on("gift replied", (res: any) => {
            const deletedMessageIndex = messages.findIndex((message: any) => message.id === res.id);

            if (deletedMessageIndex !== -1) {
                const updatedMessages = [...messages];
                updatedMessages[deletedMessageIndex].giftStatus = res.reply;
                setMessages(updatedMessages);
            }
        });

        return () => {
            listener.remove();
            socket.off('message received');
            socket.off('message deleted');
            socket.off('gift replied');
            socket.off("gift rejected");
        }
    });

    // -- APP STATE STATUS ( WHEN CLOSE APPLICATION WILL WORK ) -- //
    const handleAppStateChange = (nextAppState: any) => {
        setAppState(nextAppState);
    }

    // -- GET ALL CONVERSATION -- //
    const getConversation = () => {
        setLoading(true);
        if (!userId) {
            navigation.pop();
        }
        get(`chat/conversation?id=${userId}`).then((res: any) => {
            if (res.type == "success") {
                setLoading(false);
                setGifts(res.gifts);
                setMessages(res.messages);
                setSender(res.sender);
                setReceiver(res.receiver);
                setTimeout(() => {
                    scrollRef.current.scrollTo({
                        x: 0,
                        y: res.messages.length * 150,
                        animated: true
                    })
                }, 500)
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        });
    }


    const sendMessage = (messageType: any, messageContent: any) => {
        setPlus(false);
        setControlPlus(false);
        setMessageContent(null);
        setCopySelectedGiftId({ id: 0, giftType: 0 });    
        socket.emit("new message", {
            senderId: sender.id,
            receiverId: receiver.id,
            messageType,
            messageContent,
            parentId: replyId
        });
        setMessageContent(null);
        setReplyId(null);
        setReply("");
        setTimeout(() => {
            scrollRef.current.scrollToEnd({ animated: true });
        }, 1000);
    }

    // -- REPLY GIFT ( CONCERT OR GIFT CARD ) -- //
    const replyGift = (id: number, reply: string) => {
    
        socket.emit("reply gift", {
            id,
            reply
        });
    };
    

    // -- DELETE MESSAGE -- //
    const deleteMessage = (id: any) => {
        socket.emit("delete message", {
            id
        });
        setDeleteAnswer(false);
    }

    const getChatInfo = () => {
        get('chat').then((res: any) => {
            if (res.type == "success") {
                setFriends(res.friends);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }



    // -- LOADING STATUS ( WHEN CLOSE APPLICATION OR GETTING VARIABLES) -- //
    if (loading || appState != "active") {
        socket.disconnect();
    }

    return (
        <View
            style={styles.main}
        >
            {/* MENU */}
            <Menu
                menuColor={blue_t4}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />

            {/* HOLLY SNAP MODAL */}
            {
                snapModal ?
                    <View style={styles.snapModal}>
                        <HollySnapModal
                            close={() => {
                                setSnapModal(false);
                                getChatInfo();
                            }}
                            array={snapArray}
                        />
                    </View>
                    :
                    <></>
            }

            {/* KEYBOARD VIEW */}
            <KeyboardAvoidingView
                behavior={Platform.OS == "ios" ? "height" : "padding"}
                enabled={Platform.OS == "android" ? false : true}
                style={{ flex: 1 }}
                onLayout={() => {
                    setTimeout(() => {
                        scrollRef.current.scrollTo({
                            x: 0,
                            y: messages.length * 150,
                            animated: true
                        })
                    }, 500)
                }}
                keyboardVerticalOffset={0}
            >
                {/* HEADER */}
                <HeaderFive
                    headerControl={header}
                    logoStyle={styles.logoStyle}
                    searchIconColor={blue_t4}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menuBlue.png')}
                    backIcon={blue_t1}
                    navigation={navigation}
                    logo={require('../../../assets/root/hollyChat.png')}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                />
                

                <ScrollView
                    ref={scrollRef}
                    onScroll={handleScroll}
                    decelerationRate={"fast"}
                    alwaysBounceHorizontal={true}
                    automaticallyAdjustContentInsets={true}
                    contentOffset={{ y: messages.length * 150, x: 0 }}
                >
                {
                            loading || appState != "active" ?

                            <View style={styles.spinnerview}>
                                <Image source={require('../../../assets/root/hollychatyeni.gif')} style={styles.loadingGif} />
                            </View>
                            
                                 

                               
                                :
                    <SafeAreaView>
                        <View style={styles.toastView}>
                            <Toast
                                type={typeToast}
                                subtitle={subtitleToast}
                                status={statusToast}
                                successColor={green_t1}
                            />
                        </View>
                                <View style={styles.form}>
                                    {
                                        friends.length < 1 ?
                                            <></>
                                            :
                                            <View style={styles.topView}>

                                                {/* HOLLY SNAP AND OTHER FRIENDS */}
                                                <ScrollView horizontal>
                                                    {
                                                        friends?.map((item: any, index: React.Key) => {
                                                            return (
                                                                <TouchableOpacity
                                                                    key={index}
                                                                    style={styles.ppView}
                                                                    onPress={() => {
                                                                        if (item.snaps.length > 0) {
                                                                            setSnapArray(item.snaps);
                                                                            setSnapModal(true);
                                                                        }
                                                                    }}
                                                                >
                                                                    <View style={shadow}>
                                                                        <Image
                                                                            source={{ uri: getImageURL(item.image) }}
                                                                            style={
                                                                                [
                                                                                    styles.pp,
                                                                                    {
                                                                                        borderWidth: item.snaps.length > 0 ? 2 : 0,
                                                                                        borderColor: item.snaps.find((item_: any) => item_.seen == "false") ? yellow_t2 : gray_t1
                                                                                    }
                                                                                ]
                                                                            }
                                                                        />
                                                                    </View>
                                                                    <Text style={styles.ppName}>{item.firstName}</Text>
                                                                </TouchableOpacity>
                                                            )
                                                        })
                                                    }
                                                </ScrollView>
                                            </View>
                                    }

                                    {/* CHAT LIST */}
                                    <View style={styles.chatListView}>
                                        {
                                            messages.map((item: any, index: number) => {

                                                // FOR REPLY TEXT
                                                const message = messages.find((message: any) => message.id === item.parentId);
                                                return (
                                                    <View key={index} style={{ marginBottom: messages.length - 1 == index ? Layout.screen.height > 700 ? 70 : 70 : 0 }}>
                                                        {
                                                            // SENDER
                                                            item.isSender ?
                                                                <View style={styles.senderView}>
                                                                    {
                                                                        item.messageType == "text" ?
                                                                            <MotiView
                                                                                transition={{
                                                                                    type: 'timing',
                                                                                    duration: 200
                                                                                }}
                                                                                from={{
                                                                                    right: -50
                                                                                }}
                                                                                animate={{
                                                                                    width: deleteAnswer && indexDR == index ? 90 : 0,
                                                                                    right: deleteAnswer && indexDR == index ? 30 : -50
                                                                                }}
                                                                            >
                                                                                <View style={[styles.deleteAnswerView, shadow]}>
                                                                                    <TouchableOpacity
                                                                                        onPress={() => {
                                                                                            deleteMessage(item.id);
                                                                                        }}
                                                                                        style={styles.chatTrashTouch}
                                                                                    >
                                                                                        <Image
                                                                                            source={require('../../../assets/root/chattrash.png')}
                                                                                            style={styles.chatTrashIcon}
                                                                                        />
                                                                                    </TouchableOpacity>
                                                                                    <TouchableOpacity
                                                                                        onPress={() => {
                                                                                            setReply(item.messageContent);
                                                                                            setReplyId(item.id)
                                                                                        }}
                                                                                        style={[styles.chatAnswerTouch, shadow]}
                                                                                    >
                                                                                        <Image
                                                                                            source={require('../../../assets/root/chatanswer.png')}
                                                                                            style={styles.chatAnswer}
                                                                                        />
                                                                                    </TouchableOpacity>
                                                                                </View>
                                                                            </MotiView>
                                                                            :
                                                                            <></>
                                                                    }

                                                                    {
                                                                        item.messageType == 'text' ?
                                                                            <>
                                                                                <TouchableOpacity
                                                                                    activeOpacity={1}
                                                                                    onPress={() => {
                                                                                        setDeleteAnswer(!deleteAnswer)
                                                                                        setIndexDR(index)
                                                                                    }}
                                                                                    style={[styles.senderTouch, shadow]}
                                                                                >
                                                                                    {/* REPLY VIEW */}
                                                                                    {
                                                                                        
                                                                                        message?.messageContent ?
                                                                                            <View style={styles.replyView_}>
                                                                                                <Text style={styles.replyText_}>{message?.messageContent?.toString().substring(0, 20)}...</Text>
                                                                                            </View>
                                                                                            :
                                                                                            <></>
                                                                                    }
                                                                                    {
                                                                                        item.isDeleted ? <Text style={[styles.senderText, { color: gray_t4 }]}>Mesaj silindi</Text> :
                                                                                            <Text style={styles.senderText}>{item.messageContent}</Text>
                                                                                    }
                                                                                </TouchableOpacity>
                                                                            </>
                                                                            :
                                                                            <View style={styles.senderAltView}>
                                                                                <View style={styles.senderAltViewRow}>
                                                                                    {
                                                                                        item.giftStatus == "expired" ?
                                                                                            <Text style={[styles.giftStatusText, { color: blue_t4, marginRight: 10 }]}>Hediyenin süresi geçti.</Text>
                                                                                            :
                                                                                            item.giftStatus == "pending" ?
                                                                                                <Text style={[styles.giftStatusText, { color: blue_t4, marginRight: 10 }]}>Cevap bekleniyor.</Text>
                                                                                                :
                                                                                                item.giftStatus == "accepted" ?
                                                                                                    <Text style={[styles.giftStatusText, { color: green_t5, marginRight: 10 }]}>Hediye kabul edildi.</Text>
                                                                                                    :
                                                                                                    <Text style={[styles.giftStatusText, { color: red_t2, marginRight: 10 }]}>Hediye reddedildi.</Text>
                                                                                    }
                                                                                    {
                                                                                        item?.messageContent?.type == "giftCard" ?
                                                                                            <GiftCardItem
                                                                                                fromChat={true}
                                                                                                type={item.messageContent.cardType}
                                                                                                value={item.messageContent.value}
                                                                                                nameLastName={item.messageContent.firstName + " " + item.messageContent.lastName}
                                                                                                code={item.messageContent.code}
                                                                                            />
                                                                                            :
                                                                                            // CONCERT
                                                                                            <View style={[styles.concertItemView, { backgroundColor: white }]}>

                                                                                                {/* X1 IMAGE VIEW */}
                                                                                                <ImageBackground
                                                                                                    source={require('../../../assets/root/muratmehmet.png')}
                                                                                                    style={styles.concertItemx1}
                                                                                                >
                                                                                                    <Text style={styles.concertItemx1Text}>x1</Text>
                                                                                                </ImageBackground>
                                                                                                <Text style={styles.concertItemArtistName}>{item?.messageContent?.concertName?.length > 19 ? item?.messageContent?.concertName?.substring(0, 20) + "..." : item?.messageContent?.concertName}</Text>

                                                                                                <Text style={{
                                                                                                    position: 'absolute',
                                                                                                    bottom: 4,
                                                                                                    alignSelf: 'center',
                                                                                                    fontSize: 8,
                                                                                                    fontWeight: 'bold'
                                                                                                }}>{dayjs(item?.messageContent?.date).format("DD.MM.YYYY")}</Text>
                                                                                                {/* HOLLY TICKET LOGO */}
                                                                                                <Image
                                                                                                    style={styles.concertItemHollyTicketLogo}
                                                                                                    source={require('../../../assets/root/hollyticket.png')}
                                                                                                />
                                                                                            </View>
                                                                                    }
                                                                                </View>
                                                                            </View>
                                                                    }

                                                                    <Image
                                                                        source={{ uri: getImageURL(item.image) }}
                                                                        style={styles.senderImg}
                                                                    />
                                                                </View>
                                                                :
                                                                // RECEIVER
                                                                <View style={styles.chatReceiver}>
                                                                    <Image
                                                                        source={{ uri: getImageURL(item.image) }}
                                                                        style={styles.profileImg}
                                                                    />

                                                                    {/* REPLY BUTTON ON ANIMATION VIEW */}
                                                                    <MotiView
                                                                        transition={{
                                                                            type: 'timing',
                                                                            duration: 200
                                                                        }}
                                                                        from={{
                                                                            left: 5
                                                                        }}
                                                                        animate={{
                                                                            width: deleteAnswer && indexDR == index ? 90 : 0,
                                                                            left: deleteAnswer && indexDR == index ? 25 : 5
                                                                        }}
                                                                    >
                                                                        <View style={[styles.deleteAnswerView, shadow]}>
                                                                            <TouchableOpacity
                                                                                activeOpacity={0.9}
                                                                                onPress={() => {
                                                                                    setReply(item.messageContent);
                                                                                    setReplyId(item.id);
                                                                                }}
                                                                                style={[styles.chatAnswerTouch, shadow, { left: -15, backgroundColor: blue_t1 }]}
                                                                            >
                                                                                <Image
                                                                                    source={require('../../../assets/root/chatanswerwhite.png')}
                                                                                    style={styles.chatAnswer}
                                                                                />
                                                                            </TouchableOpacity>
                                                                        </View>
                                                                    </MotiView>
                                                                    {
                                                                        item.messageType == 'text' ?
                                                                            /* TEXT MESSAGE */
                                                                            <TouchableOpacity
                                                                                activeOpacity={1}
                                                                                onPress={() => {
                                                                                    setDeleteAnswer(!deleteAnswer)
                                                                                    setIndexDR(index)
                                                                                }}
                                                                                style={styles.receiverView}
                                                                            >
                                                                                {
                                                                                    message?.messageContent ?
                                                                                        <View style={[styles.replyView_, { backgroundColor: blue_t4 }]}>
                                                                                            <Text style={[styles.replyText_, { color: white }]}>{message?.messageContent?.toString().substring(0, 20)}...</Text>
                                                                                        </View>
                                                                                        :
                                                                                        <></>
                                                                                }
                                                                                {
                                                                                    item.messageType == 'text' ?
                                                                                        item.isDeleted ? <Text style={[styles.receiverText, { color: white }]}>Mesaj silindi</Text> :
                                                                                            <Text style={styles.receiverText}>{item.messageContent}</Text>

                                                                                        :
                                                                                        <></>
                                                                                }
                                                                            </TouchableOpacity>
                                                                            :
                                                                            <View style={styles.receiverAltView}>
                                                                                {
                                                                                    item?.messageContent?.type == "giftCard" ?
                                                                                        <View style={styles.receiverGiftView}>
                                                                                            <GiftCardItem
                                                                                                fromChat={true}
                                                                                                type={item.messageContent.cardType}
                                                                                                value={item.messageContent.value}
                                                                                                nameLastName={item.messageContent.firstName + " " + item.messageContent.lastName}
                                                                                                code={item.messageContent.code}
                                                                                            />

                                                                                            {/* GIFT ACCEPT OR REJECT BUTTONS ( GIFT WHEN PENDING ) */}
                                                                                            {
                                                                                                item.giftStatus == "pending" ?
                                                                                                    <View style={[styles.receiverGiftButtonsView, { marginTop: 15 }]}>
                                                                                                        {/* ACCEPT */}
                                                                                                        <TouchableOpacity
                                                                                                            onPress={() => {
                                                                                                                replyGift(item.id, "accepted")
                                                                                                            }}
                                                                                                            style={styles.receiverGiftButtonAccept}
                                                                                                        >
                                                                                                            <Text style={styles.receiverGiftButtonTexts}>{MainStore.language.accept}</Text>
                                                                                                        </TouchableOpacity>

                                                                                                        {/* REJECT */}
                                                                                                        <TouchableOpacity
                                                                                                            onPress={() => {
                                                                                                                replyGift(item.id, "rejected")
                                                                                                            }}
                                                                                                            style={styles.receiverGiftButtonReject}
                                                                                                        >
                                                                                                            <Text style={styles.receiverGiftButtonTexts}>{MainStore.language.denied}</Text>
                                                                                                        </TouchableOpacity>
                                                                                                    </View>
                                                                                                    :
                                                                                                    item.giftStatus == "expired" ?
                                                                                                        <Text style={[styles.giftStatusText, { color: red_t2 }]}>Hediyenin süresi geçti.</Text>
                                                                                                        :
                                                                                                        item.giftStatus == "accepted" ?
                                                                                                            <Text style={[styles.giftStatusText, { color: green_t5 }]}>{MainStore.language.accept_gift}</Text>
                                                                                                            :
                                                                                                            <Text style={[styles.giftStatusText, { color: red_t2 }]}>{MainStore.language.denied_gift}</Text>
                                                                                            }
                                                                                        </View>
                                                                                        :
                                                                                        // CONCERT
                                                                                        <View style={[styles.concertItemView, { backgroundColor: white }]}>

                                                                                            {/* X1 IMAGE VIEW */}
                                                                                            <ImageBackground
                                                                                                source={require('../../../assets/root/muratmehmet.png')}
                                                                                                style={styles.concertItemx1}
                                                                                            >
                                                                                                <Text style={styles.concertItemx1Text}>x1</Text>
                                                                                            </ImageBackground>
                                                                                            <Text style={styles.concertItemArtistName}>{item?.messageContent?.concertName?.length > 19 ? item?.messageContent?.concertName?.substring(0, 20) + "..." : item?.messageContent?.concertName}</Text>
                                                                                            <Text style={{
                                                                                                position: 'absolute',
                                                                                                bottom: 4,
                                                                                                alignSelf: 'center',
                                                                                                fontSize: 8,
                                                                                                fontWeight: 'bold'
                                                                                            }}>{dayjs(item?.messageContent?.date).format("DD.MM.YYYY")}</Text>
                                                                                            {/* HOLLY TICKET LOGO */}
                                                                                            <Image
                                                                                                style={styles.concertItemHollyTicketLogo}
                                                                                                source={require('../../../assets/root/hollyticket.png')}
                                                                                            />
                                                                                        </View>
                                                                                }

                                                                            </View>
                                                                    }


                                                                </View>
                                                        }
                                                        {
                                                            index == messages.length - 1 ?
                                                                item.messageStatus == "seen" ?
                                                                    <Text style={{
                                                                        fontFamily: 'bold',
                                                                        color: gray_t1,
                                                                        alignSelf: 'center',
                                                                        marginTop: 10,
                                                                    }}>Görüldü</Text>
                                                                    :
                                                                    item.messageStatus == "delivered" ?
                                                                        <Text style={{
                                                                            fontFamily: 'bold',
                                                                            color: gray_t1,
                                                                            alignSelf: 'center',
                                                                            marginTop: 10,
                                                                        }}>İletildi.</Text>
                                                                        :
                                                                        <Text style={{
                                                                            fontFamily: 'bold',
                                                                            color: gray_t1,
                                                                            alignSelf: 'center',
                                                                            marginTop: 10,
                                                                        }}>Gönderildi.</Text>
                                                                :
                                                                <></>
                                                        }
                                                    </View>
                                                )
                                            })
                                        }
                                    </View>
                                </View>
                        
                    </SafeAreaView>
                    }
                </ScrollView>
                


                {/* REPLY VIEW */}
                <MotiView
                    animate={{
                        bottom: plus ? 110 : reply ? 145 : 80,
                        height: plus ? 0 : reply ? "auto" : 0
                    }}
                    style={styles.replyMotiView}
                >
                    
                </MotiView>

                {/* GIFTS (CONCERT AND GIFT CARDS) VIEW */}
                {
    !loading && (
        <MotiView
            animate={{
                bottom: plus ? 120 : 80,
                height: plus ? 100 : 0,
                width: plus ? 200 : 0
            }}
            style={styles.giftMotiView}
        >
            <View
                style={
                    [
                        styles.giftView,
                        {
                            height: controlPlus ? 160 : 0,
                            width: controlPlus ? Layout.screen.width / 1.2 : 0,
                        }
                    ]
                }
            >

                {/* GIFT ICON AND TITLE */}
                <View style={styles.giftHeaderView}>
                    <Image
                        source={require('../../../assets/root/gift.png')}
                        style={{
                            width: controlPlus ? 39 : 0,
                            height: controlPlus ? 36 : 0
                        }}
                    />
                    <Text style={styles.giftText}>{MainStore.language.sendGift}</Text>
                </View>
                {
                    gifts?.length < 1 ?
                        // NO GIFT VARIABLES
                        <View style={styles.giftAltView}>

                            {/* NO GIFT VARIABLES */}
                            <Text style={styles.noGift}>{MainStore.language.no_has_gift}</Text>

                            {/* GIFT BUTTONS */}
                            <View style={styles.giftButtonsView}>
                                <Button
                                    onPress={() => {
                                        navigation.navigate("HollyShop");
                                    }}
                                    style={styles.giftCardButton}
                                >
                                    <Text style={styles.giftCardText}>{MainStore.language.buy_gify_card}</Text>
                                </Button>
                                <Button
                                    onPress={() => {
                                        navigation.navigate("Concerts");
                                    }}
                                    style={styles.giftConcertView}
                                >
                                    <Text style={styles.giftConcertText}>{MainStore.language.buy_concert}</Text>
                                </Button>
                            </View>
                        </View>
                        :
                        controlPlus && gifts.length < 2 ?
                            <View style={{
                                flexDirection: 'row',
                                marginTop: 10
                            }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        setCopySelectedGiftId({
                                            id: gifts[0]?.id,
                                            giftType: gifts[0]?.giftType
                                        })
                                        setSelectedGiftId({
                                            type: gifts[0]?.giftType == 0 ? "giftCard" : "concert",
                                            id: gifts[0]?.id,
                                            cardType: gifts[0]?.giftType == 0 ? gifts[0]?.type : null,
                                            value: gifts[0]?.giftType == 0 ? gifts[0]?.value : null,
                                            code: gifts[0]?.giftType == 0 ? gifts[0]?.code : null,
                                            firstName: sender?.firstName,
                                            lastName: sender?.lastName,
                                            concertName: gifts[0]?.giftType == 0 ? null : gifts[0]?.name,
                                            date: gifts[0]?.date
                                        })
                                    }}
                                    style={
                                        [
                                            styles.giftTouch,
                                            {
                                                borderWidth: gifts[0]?.id == copySelectedGiftId.id && gifts[0]?.giftType == copySelectedGiftId.giftType ? 1 : 0
                                            }
                                        ]
                                    }
                                >
                                    {
                                        gifts[0]?.giftType == 0 ?
                                            <View
                                                style={
                                                    [
                                                        styles.giftItemView,
                                                        {
                                                            backgroundColor: yellow_t1,
                                                            height: 70,
                                                            width: 102
                                                        }
                                                    ]
                                                }>

                                                <Text style={styles.giftItemText}>{gifts[0]?.type == 1 ? "Gold Card" : gifts[0]?.type == 2 ? "Silver Card" : "Bronz Card"}</Text>
                                            </View>
                                            :
                                            <View style={styles.concertItemView}>

                                                {/* X1 IMAGE VIEW */}
                                                <ImageBackground
                                                    source={require('../../../assets/root/muratmehmet.png')}
                                                    style={styles.concertItemx1}
                                                >
                                                    <Text style={styles.concertItemx1Text}>x{gifts[0]?.quantity}</Text>
                                                </ImageBackground>
                                                <Text style={styles.concertItemArtistName}>{gifts[0]?.name.length > 19 ? gifts[0]?.name.substring(0, 20) + "..." : gifts[0]?.name}</Text>
                                                <Text style={{
                                                    position: 'absolute',
                                                    bottom: 4,
                                                    alignSelf: 'center',
                                                    fontSize: 8,
                                                    fontWeight: 'bold'
                                                }}>{dayjs(gifts[0]?.date).format("DD.MM.YYYY")}</Text>
                                                {/* HOLLY TICKET LOGO */}
                                                <Image
                                                    style={styles.concertItemHollyTicketLogo}
                                                    source={require('../../../assets/root/hollyticket.png')}
                                                />
                                            </View>
                                    }
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        setCopySelectedGiftId({
                                            id: gifts[1]?.id,
                                            giftType: gifts[1]?.giftType
                                        })
                                        setSelectedGiftId({
                                            type: gifts[1]?.giftType == 0 ? "giftCard" : "concert",
                                            id: gifts[1]?.id,
                                            cardType: gifts[1]?.giftType == 0 ? gifts[1]?.type : null,
                                            value: gifts[1]?.giftType == 0 ? gifts[1]?.value : null,
                                            code: gifts[1]?.giftType == 0 ? gifts[1]?.code : null,
                                            firstName: sender?.firstName,
                                            lastName: sender?.lastName,
                                            concertName: gifts[1]?.giftType == 0 ? null : gifts[1]?.name,
                                            date: gifts[1]?.date
                                        })
                                    }}
                                    style={
                                        [
                                            styles.giftTouch,
                                            {
                                                borderWidth: gifts[1]?.id == copySelectedGiftId.id && gifts[1]?.giftType == copySelectedGiftId.giftType ? 1 : 0,
                                                marginLeft: 20,
                                            }
                                        ]
                                    }
                                >
                                    {
                                        gifts[1]?.giftType == 0 ?
                                            <View
                                                style={
                                                    [
                                                        styles.giftItemView,
                                                        {
                                                            backgroundColor: yellow_t1,
                                                        }
                                                    ]
                                                }>

                                                <Text style={styles.giftItemText}>{gifts[1]?.type == 1 ? "Gold Card" : gifts[1]?.type == 2 ? "Silver Card" : "Bronz Card"}</Text>
                                            </View>
                                            :
                                            <View style={styles.concertItemView}>

                                                {/* X1 IMAGE VIEW */}
                                                <ImageBackground
                                                    source={require('../../../assets/root/muratmehmet.png')}
                                                    style={styles.concertItemx1}
                                                >
                                                    <Text style={styles.concertItemx1Text}>x{gifts[1]?.quantity}</Text>
                                                </ImageBackground>
                                                <Text style={styles.concertItemArtistName}>{gifts[1]?.name.length > 19 ? gifts[1].name.substring(0, 20) + "..." : gifts[1]?.name}</Text>
                                                <Text style={{
                                                    position: 'absolute',
                                                    bottom: 4,
                                                    alignSelf: 'center',
                                                    fontSize: 8,
                                                    fontWeight: 'bold'
                                                }}>{dayjs(gifts[1]?.date).format("DD.MM.YYYY")}</Text>
                                                {/* HOLLY TICKET LOGO */}
                                                <Image
                                                    style={styles.concertItemHollyTicketLogo}
                                                    source={require('../../../assets/root/hollyticket.png')}
                                                />
                                            </View>
                                    }
                                </TouchableOpacity>
                            </View>
                            :
                            <FlatList
                                contentContainerStyle={styles.giftsView}
                                data={gifts}
                                horizontal={true}
                                renderItem={({ item, index }) => {
                                    return (
                                        <TouchableOpacity
                                            onPress={() => {
                                                setCopySelectedGiftId({
                                                    id: item.id,
                                                    giftType: item.giftType
                                                })
                                                setSelectedGiftId({
                                                    type: item.giftType == 0 ? "giftCard" : "concert",
                                                    id: item.id,
                                                    cardType: item.giftType == 0 ? item.type : null,
                                                    value: item.giftType == 0 ? item.value : null,
                                                    code: item.giftType == 0 ? item.code : null,
                                                    firstName: sender?.firstName,
                                                    lastName: sender?.lastName,
                                                    concertName: item.giftType == 0 ? null : item.name,
                                                    date: item?.date
                                                })
                                            }}
                                            style={
                                                [
                                                    styles.giftTouch,
                                                    {
                                                        borderWidth: item.id == copySelectedGiftId.id && item.giftType == copySelectedGiftId.giftType ? 1 : 0,
                                                        marginRight: index == gifts.length - 1 ? 50 : 0
                                                    }
                                                ]
                                            }
                                            key={index}
                                        >
                                            {
                                                item.giftType == 0 ?
                                                    <View
                                                        style={
                                                            [
                                                                styles.giftItemView,
                                                                {
                                                                    backgroundColor: yellow_t1,
                                                                }
                                                            ]
                                                        }>

                                                        <Text style={styles.giftItemText}>{item.type == 1 ? "Gold Card" : item.type == 2 ? "Silver Card" : "Bronz Card"}</Text>
                                                    </View>
                                                    :
                                                    <View style={styles.concertItemView}>

                                                        {/* X1 IMAGE VIEW */}
                                                        <ImageBackground
                                                            source={require('../../../assets/root/muratmehmet.png')}
                                                            style={styles.concertItemx1}
                                                        >
                                                            <Text style={styles.concertItemx1Text}>x{item.quantity}</Text>
                                                        </ImageBackground>
                                                        <Text style={styles.concertItemArtistName}>{item.name.length > 19 ? item.name.substring(0, 20) + "..." : item.name}</Text>
                                                        <Text style={{
                                                            position: 'absolute',
                                                            bottom: 4,
                                                            alignSelf: 'center',
                                                            fontSize: 8,
                                                            fontWeight: 'bold'
                                                        }}>{dayjs(item?.date).format("DD.MM.YYYY")}</Text>
                                                        {/* HOLLY TICKET LOGO */}
                                                        <Image
                                                            style={styles.concertItemHollyTicketLogo}
                                                            source={require('../../../assets/root/hollyticket.png')}
                                                        />
                                                    </View>
                                            }
                                        </TouchableOpacity>
                                    )
                                }}
                            />
                }
            </View>
        </MotiView>
    )
}
{
loading ? <></>
                        :
                <View style={styles.sendMessageView}>
                        <View style={styles.chatPlusInputView}>
                        {/* CHAT PLUS */}
                        <TouchableOpacity
                            onPress={() => {
                                setPlus(!plus);

                                if (controlPlus == true) {
                                    setControlPlus(!controlPlus);
                                } else {
                                    setTimeout(() => {
                                        setControlPlus(!controlPlus);
                                    }, 250);
                                }
                                setMessageContent(null);
                                setCopySelectedGiftId({ id: 0, giftType: 0 });
                            }}
                        >
                            <Image
                                source={require('../../../assets/root/chatPlus.png')}
                                style={styles.chatPlusIcon}
                            />
                        </TouchableOpacity>

                        {/* SEND GIFT BUTTON AREA */}
                        {
                            copySelectedGiftId.id != 0 || copySelectedGiftId.giftType != 0 ?
                                <TouchableOpacity
                                    onPress={() => {
                                        sendMessage("gift", selectedGiftId);
                                    }}
                                    style={styles.sendGiftButton}
                                >
                                    <Text style={styles.sendGiftButtonText}>{MainStore.language.send}</Text>
                                </TouchableOpacity>

                                :
                                /* INPUT */
                                <Input
                                    ml={2}
                                    borderWidth={0}
                                    color={white}
                                    placeholderTextColor={white}
                                    value={messageContent}
                                    onChange={(event) => {
                                        setMessageContent(event.nativeEvent.text)
                                    }}
                                    fontWeight={'bold'}
                                    fontSize={14}
                                    placeholder={MainStore.language.text_some}
                                    w={Layout.screen.width / 1.5}
                                    backgroundColor={'transparent'}
                                />
                        }
                    </View>
                    {/* SEND BUTTON MESSAGE AREA*/}
                    {
                        messageContent ? // Eğer messageContent değeri varsa (doluysa)
                            <TouchableOpacity
                                onPress={() => {
                                    sendMessage("text", messageContent);
                                }}
                                style={styles.sendButtonTouch}
                            >
                                <Image
                                    source={require('../../../assets/root/sendButton.png')}
                                    style={styles.sendButtonIcon}
                                />
                            </TouchableOpacity>
                        : // Değilse (boşsa)
                            <></> // Hiçbir şey yapma, boş görünüm döndür
                    }
                </View>
                }
            </KeyboardAvoidingView>
        </View >
    )
}
export default Chat;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi 
    },
    snapModal: {
        position: 'absolute',
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: 300

    },
    senderView: {
        flexDirection: 'row',
        marginTop: 10,
        alignItems: 'center',
        alignSelf: 'flex-end'
    },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    logoStyle: {
        height: 28,
        position: 'absolute',
        left: 45,
        width: 126,
        top: 22
    },
    form: { marginTop: 70 },
    topView: { height: 80 },
    ppView: {
        alignItems: 'center',
        height: 52,
        width: 52,
        marginLeft: 20
    },
    pp: {
        height: 52,
        borderRadius: 26,
        width: 52
    },
    ppName: {
        fontWeight: 'bold',
        fontSize: 8,
        marginTop: 5
    },
    chatListView: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
    },
    chatReceiver: {
        flexDirection: 'row',
        marginTop: 10
    },
    profileImg: {
        height: 30,
        width: 30,
        borderRadius: 15,
        alignSelf: 'flex-end'
    },
    receiverView: {
        width: Layout.screen.width / 2,
        padding: 10,
        backgroundColor: blue_t4,
        marginLeft: 10,
        borderRadius: 10
    },
    receiverAltView: { marginLeft: 10 },
    receiverText: {
        color: white,
        fontSize: 12
    },
    receiverGiftView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    receiverGiftButtonsView: { marginLeft: 10 },
    receiverGiftButtonAccept: {
        width: 102,
        height: 30,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: green_t5
    },
    receiverGiftButtonReject: {
        width: 102,
        height: 30,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: red_t2,
        marginTop: 5
    },
    receiverGiftButtonTexts: {
        fontSize: 16,
        fontWeight: 'bold',
        color: white
    },
    giftStatusText: {
        fontWeight: 'bold',
        marginLeft: 10,
        fontSize: 12
    },
    spinnerview:{
        height: Layout.screen.height,
        width: Layout.screen.width,
        alignItems: 'center',
        alignContent: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        zIndex: 1000,
    },
    loadingGif: {
        width: 230,
        height: 51,
    },
    senderTouch: {
        width: Layout.screen.width / 2.4,
        padding: 10,
        backgroundColor: white,
        marginRight: 15,
        borderRadius: 10
    },
    senderAltView: { marginRight: 10 },
    senderAltViewRow: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    replyView_: {
        marginTop: -10,
        height: 20,
        borderTopLeftRadius: 10,
        zIndex: -1,
        borderTopRightRadius: 10,
        width: Layout.screen.width / 2.7,
        alignSelf: 'center',
        paddingLeft: 10,
    },
    replyText_: {
        fontSize: 10,
        color: gray_t4,
    },
    senderText: {
        color: black_t4,
        fontSize: 12
    },
    senderImg: {
        height: 30,
        width: 30,
        borderRadius: 15,
        alignSelf: 'flex-end'
    },
    replyMotiView: {
        zIndex: 0,
        marginBottom: 10,
    },
    replyView: {
        padding: 15,
        paddingBottom: 10,
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        position: 'absolute',
        zIndex: -2,
        backgroundColor: white,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
    },
    replyText: {
        fontSize: 12,
        color: black,
    },
    giftMotiView: {
        zIndex: 0,
        position: 'absolute',
        alignSelf: 'center',
    },
    giftView: {
        padding: 0,
        paddingBottom: 10,
        alignSelf: 'center',
        position: 'absolute',
        zIndex: -2,
        borderRadius: 30,
        backgroundColor: white,
    },
    giftHeaderView: {
        alignSelf: 'center',
        marginTop: 10,
        flexDirection: 'row',
        alignItems: 'center'
    },
    giftIcon: {
        width: 39,
        height: 36
    },
    giftText: {
        fontSize: 18,
        color: black_t3,
        fontWeight: 'bold'
    },
    giftItemView: {
        width: 102,
        borderRadius: 5,
        height: 70,
        justifyContent: 'center',
        alignItems: 'center'
    },
    giftItemText: {
        color: white,
        fontSize: 15,
        fontFamily: 'MADE TOMMY',
    },
    concertItemView: {
        width: 102,
        height: 70,
        borderRadius: 5,
        backgroundColor: 'rgba(53, 85, 150, 0.01)',
        justifyContent: 'center',
        alignItems: 'center'
    },
    concertItemx1: {
        width: 14.93,
        height: 18.75,
        position: 'absolute',
        top: 0,
        right: 10,
        alignItems: 'center',
    },
    concertItemx1Text: {
        fontWeight: 'bold',
        color: white,
        fontSize: 8,
        marginTop: 2
    },
    concertItemArtistName: {
        fontFamily: 'MADE TOMMY',
        fontSize: 10,
        width: 60,
        textAlign: 'center'
    },
    concertItemHollyTicketLogo: {
        width: 55,
        height: 9.95,
        position: 'absolute',
        left: -18,
        transform: [{ rotate: '270deg' }]
    },
    giftAltView: {
        borderWidth: 1,
        borderColor: blue_t4,
        height: '30%',
        marginTop: 5,
        width: '90%',
        alignSelf: 'center',
        borderRadius: 10,
        alignItems: 'center'
    },
    noGift: {
        marginTop: 5,
        fontWeight: 'bold',
        color: gray_t4
    },
    giftButtonsView: {
        position: 'absolute',
        bottom: -15,
        flexDirection: 'row'
    },
    giftCardButton: {
        backgroundColor: yellow_t1,
        width: Layout.screen.width / 3
    },
    giftCardText: {
        textAlign: 'center',
        fontSize: 10,
        fontWeight: 'bold',
        color: white
    },
    giftConcertView: {
        marginLeft: 10,
        width: Layout.screen.width / 3,
        backgroundColor: green_t1
    },
    giftConcertText: {
        fontSize: 10,
        fontWeight: 'bold',
        color: white,
        textAlign: 'center'
    },
    giftsView: { marginTop: 10 },
    giftTouch: {
        marginLeft: 10,
        borderRadius: 5,
        borderColor: blue_t4,
        height: 72,
    },
    sendMessageView: {
        position: 'absolute',
        bottom: 20,
        alignSelf: 'center',
        borderRadius: 30,
        height: 50,
        width: Layout.screen.width / 1.1,
        backgroundColor: blue_t4,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    chatPlusInputView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 10,
        zIndex: 2,
    },
    chatPlusIcon: {
        height: 24,
        width: 24
    },
    sendGiftButton: {
        width: Layout.screen.width / 1.4,
        alignItems: 'center',
        justifyContent: 'center',
        height: 70
    },
    sendGiftButtonText: {
        color: white,
        fontSize: 22,
        fontWeight: 'bold'
    },
    sendButtonTouch: { paddingRight: 10 },
    sendButtonIcon: {
        height: 24,
        width: 24
    },
    deleteAnswerView: {
        height: 30,
        width: '90%',
        backgroundColor: white,
        right: -20,
        zIndex: -1,
        borderRadius: 20,
        flexDirection: 'row',
        alignItems: 'center'
    },
    chatTrashTouch: { marginLeft: 15 },
    chatTrashIcon: {
        width: 15,
        height: 15
    },
    chatAnswerTouch: {
        marginLeft: 15,
        width: '110%',
        height: 30,
        justifyContent: 'center',
        borderRadius: 20,
        backgroundColor: white
    },
    chatAnswer: {
        width: 29,
        marginLeft: 15,
        marginTop: 2,
        height: 23
    },
});
import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFive from "../../../components/HeaderFive";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Menu from "../../../components/Menu";
import { Spinner } from "native-base";
import { blue_t1, blue_t4, green_t4, kremrengi, red_t2, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import { get, post, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";

const NewFriend: React.FC = () => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingButton, setLoadingButton] = React.useState(false);

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- REQUEST INFO -- //
    const [requestList, setRequestList] = React.useState([]);

    const getRequestList = () => {
        get('chat/request-list').then((res: any) => {
            if (res.type == "success") {
                setRequestList(res.requestList);
                setLoading(false);
            } else {
                startToast(res.error, "error");
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    React.useEffect(() => {
        getRequestList();
    }, []);

    const respondToRequest = (id: number, response: string) => {
        setLoadingButton(true);
        post("chat/respond-to-request", {
            id,
            response
        }).then((res: any) => {
            if (res.type == "success") {
                startToast(response == "accepted" ? "İsteği kabul ettiniz!" : "İsteği reddettiniz!", "success");
                if (response === "accepted") {
                    setTimeout(() => {
                        navigation.navigate("HollyChat"); // "Chat" sayfasına yönlendirme
                    }, 1500); // Toast mesajının bitmesini bekleyin ve yönlendirme yapın
                } else {
                    setLoading(true);
                    getRequestList();
                }
            } else {
                startToast(res.error, "error");
            }
            setLoadingButton(false);
        })
    }

    return (
        <View
            style={styles.main}
        >

            {/* MENU */}
            <Menu
                menuColor={blue_t4}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* HEADER */}
                <HeaderFive
                    logoStyle={styles.logoStyle}
                    searchIconColor={blue_t4}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menuBlue.png')}
                    backIcon={blue_t1}
                    navigation={navigation}
                    logo={require('../../../assets/root/hollyChat.png')}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                />
                <SafeAreaView>
                    <View style={styles.toastView}>
                        <Toast
                            type={typeToast}
                            subtitle={subtitleToast}
                            status={statusToast}
                            successColor={green_t4}
                        />
                    </View>
                    {
                        loading ? < Spinner color={blue_t4} size={18} style={{ marginTop: 70 }} /> : requestList.map((item: any, index: number) => {
                            return (
                                <ImageBackground
                                    source={require('../../../assets/root/newFriendBack.png')}
                                    style={styles.newFriendBack}
                                >

                                    {/* PROFILE IMAGE */}
                                    <Image
                                        source={{ uri: getImageURL(item.image) }}
                                        style={styles.profile}
                                    />

                                    {/* NAME SURNAME */}
                                    <View style={styles.rightView}>
                                        <Text style={styles.rightText}>{`${item.firstName} ${item.lastName}`}</Text>
                                        <View style={styles.buttonsView}>

                                            {loadingButton ? <Spinner color={white} size={18} mt={5} /> : (
                                                <>
                                                    {/* ACCEPT */}
                                                    <TouchableOpacity
                                                    onPress={() => {
                                                        respondToRequest(item.id, 'accepted');
                                                    }}
                                                        style={styles.acceptButton}
                                                    >
                                                        <Text style={styles.accept}>Kabul et</Text>
                                                    </TouchableOpacity>

                                                    {/* REJECT */}
                                                    <TouchableOpacity
                                                    onPress={() => {
                                                        respondToRequest(item.id, 'rejected');
                                                    }}
                                                        style={styles.rescueButton}
                                                    >
                                                        <Text style={styles.rescue}>Reddet</Text>
                                                    </TouchableOpacity>
                                                </>
                                            )}
                                        </View>
                                    </View>
                                </ImageBackground>
                            )
                        })
                    }
                </SafeAreaView>
            </ScrollView>
        </View >
    )
}
export default NewFriend;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi },
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    logoStyle: {
        height: 28,
        position: 'absolute',
        left: 45,
        width: 126,
        top: 22
    },
    newFriendBack: {
        marginTop: 70,
        width: Layout.screen.width / 1.1,
        height: Layout.screen.width / 4.5,
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center'
    },
    profile: {
        height: Layout.screen.width / 4.5,
        borderRadius: 100,
        left: -5,
        width: Layout.screen.width / 4.5,
    },
    rightView: {
        width: Layout.screen.width / 1.5,
        alignItems: 'center',
        height: Layout.screen.width / 6.4,
        justifyContent: 'space-between'
    },
    rightText: {
        fontSize: 15,
        fontWeight: 'bold',
        color: white
    },
    buttonsView: { flexDirection: 'row' },
    acceptButton: {
        height: 28,
        width: 102,
        backgroundColor: green_t4,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    accept: {
        color: white,
        fontSize: 15,
        fontWeight: 'bold'
    },
    rescueButton: {
        height: 28,
        marginLeft: 10,
        width: 102,
        backgroundColor: red_t2,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    rescue: {
        color: white,
        fontSize: 15,
        fontWeight: 'bold'
    },
});
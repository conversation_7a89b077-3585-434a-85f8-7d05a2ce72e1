import React from "react";
import { View, StyleSheet } from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

const HollyChatSkeleton = () => {
  return (
    <SkeletonPlaceholder>
      <View style={styles.header} />
      <View style={styles.tabContainer}>
        <View style={styles.tab} />
        <View style={styles.tab} />
      </View>
      <View style={styles.memberContainer} />
      <View style={styles.blockedContainer}>
        <View style={styles.blockedTitle} />
        <View style={styles.blockedItem}>
          <View style={styles.profileImage} />
          <View style={styles.textContainer}>
            <View style={styles.nameText} />
            <View style={styles.descriptionText} />
          </View>
          <View style={styles.teamText} />
          <View style={styles.invalidDate} />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};

const styles = StyleSheet.create({
  header: { width: "60%", height: 30, borderRadius: 5, alignSelf: "center", marginVertical: 10 },
  tabContainer: { flexDirection: "row", justifyContent: "space-around", marginVertical: 10 },
  tab: { width: "40%", height: 30, borderRadius: 5 },
  memberContainer: { width: "90%", height: 50, borderRadius: 10, alignSelf: "center", marginVertical: 10 },
  blockedContainer: { padding: 10 },
  blockedTitle: { width: "30%", height: 20, borderRadius: 5, marginBottom: 10 },
  blockedItem: { flexDirection: "row", alignItems: "center", marginTop: 10 },
  profileImage: { width: 40, height: 40, borderRadius: 20, marginRight: 10 },
  textContainer: { flex: 1 },
  nameText: { width: "50%", height: 15, borderRadius: 5, marginBottom: 5 },
  descriptionText: { width: "80%", height: 10, borderRadius: 5 },
  teamText: { width: "20%", height: 15, borderRadius: 5, marginLeft: "auto" },
  invalidDate: { width: "25%", height: 10, borderRadius: 5, marginLeft: 5 }
});

export default HollyChatSkeleton;

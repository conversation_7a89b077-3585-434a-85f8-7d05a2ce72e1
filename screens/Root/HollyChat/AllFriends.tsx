import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFive from "../../../components/HeaderFive";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Menu from "../../../components/Menu";
import { Spinner } from "native-base";
import { green_t4, blue_t4, white, blue_t1, red_t1, gray_t1, kremrengi } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import { get, post, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import BottomBar from "../../../components/BottomBar";

const AllFriends: React.FC = (props: any) => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);


    React.useEffect(() => {

    }, []);

    return (
        <View
            style={styles.main}
        >

            {/* MENU */}
            <Menu
                menuColor={blue_t4}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* HEADER */}
                <HeaderFive
                    logoStyle={styles.logoStyle}
                    searchIconColor={blue_t4}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menuBlue.png')}
                    backIcon={blue_t1}
                    navigation={navigation}
                    logo={require('../../../assets/root/hollyChat.png')}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                />
                <SafeAreaView style={{ marginTop: 70 }}>
                    {
                        props.route.params.friends.length > 1 ? props.route.params.friends?.map((item: any, index: React.Key) => {
                            return (
                                <View key={index} style={{ marginBottom: props.route.params.friends.length - 1 == index ? 100 : 10 }}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            //@ts-ignore
                                            navigation.navigate("Chat", { id: item.userId })
                                        }}
                                        style={{
                                            flexDirection: 'row',
                                            marginVertical: 10,
                                            alignItems: 'center',
                                            marginHorizontal: 25,
                                        }}
                                    >
                                        <Image
                                            style={{
                                                height: 60,
                                                width: 60,
                                                borderRadius: 30
                                            }}
                                            source={{ uri: getImageURL(item.image) }}
                                        />
                                        <View style={{
                                            marginLeft: 20
                                        }}>
                                            <Text style={{
                                                fontWeight: 'bold',
                                                fontSize: 16
                                            }}>{item.firstName} {item.lastName}</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <View style={{ height: 1, width: Layout.screen.width / 1.1, alignSelf: 'center', backgroundColor: gray_t1, opacity: 0.2 }} />
                                </View>
                            )
                        })
                            :
                            <></>
                    }

                </SafeAreaView>
            </ScrollView>
            <BottomBar navigation={navigation} type={2} />
        </View>
    )
}
export default AllFriends;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi},
    logoStyle: {
        height: 28,
        position: 'absolute',
        left: 45,
        width: 126,
        top: 22
    },

});
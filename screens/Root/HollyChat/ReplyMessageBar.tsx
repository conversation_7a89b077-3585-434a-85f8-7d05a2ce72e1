import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Image } from 'react-native';
import { IMessage } from 'react-native-gifted-chat';
import Animated, { FadeInDown, FadeOutDown } from 'react-native-reanimated';
import { gray_t1, green_t1, green_t2, green_t3 } from '../../../constants/Color';

type ReplyMessageBarProps = {
  clearReply: () => void;
  message: IMessage | null;
};

const ReplyMessageBar = ({ clearReply, message }: ReplyMessageBarProps) => {
  return (
    <>
      {message !== null && (
        <Animated.View
          style={styles.container}
          entering={FadeInDown}
          exiting={FadeOutDown}
        >
          <View style={styles.highlight} />
          <View style={styles.messageContent}>
            <Text style={styles.userName}>
              {message?.user.name}
            </Text>
            <Text style={styles.messageText}>
              {message!.text.length > 40 ? message?.text.substring(0, 40) + '...' : message?.text}
            </Text>
          </View>
          <View style={styles.clearButton}>
            <TouchableOpacity onPress={clearReply}>
              <Image
                style={styles.crossButtonIcon}
                source={require('../../../assets/cross-button.png')}
              />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 70,
    flexDirection: 'row',
    backgroundColor: '#E4E9EB',
  },
  highlight: {
    height: 70,
    width: 6,
    backgroundColor: green_t3,
  },
  messageContent: {
    flexDirection: 'column',
  },
  userName: {
    color: green_t3,
    paddingLeft: 10,
    paddingTop: 5,
    fontWeight: '600',
    fontSize: 15,
  },
  messageText: {
    color: gray_t1,
    paddingLeft: 10,
    paddingTop: 5,
  },
  clearButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: 10,
  },
  crossButtonIcon: {
    width: 25, // ikonun genişliğini ve yüksekliğini ayarlayın
    height: 25,
  },
});

export default ReplyMessageBar;

import React, { useEffect, useState } from "react";
import { FlatList, Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View, Alert  } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderFive from "../../../components/HeaderFive";
import { useFocusEffect, useIsFocused, useNavigation } from "@react-navigation/native";
import Menu from "../../../components/Menu";
import { kremrengi, black, black_t3, blue_t4, brown_t3, gray_t13, gray_t16, red_t2, red_t5, white, yellow_t2, green_t1, gray_t1, blue_t1 } from "../../../constants/Color";
import { Spinner } from "native-base";
import Layout from "../../../constants/Layout";
import { shadow } from "../../../constants/Shadow";
import { get, post, getImageURL } from "../../../networking/Server";
import Toast from "../../../components/Toast";
import dayjs from "dayjs";
import HollySnapModal from "../../../components/HollySnapModal";
import BottomBar from "../../../components/BottomBar";
import { MainStore } from "../../../stores/MainStore";
import { Swipeable } from 'react-native-gesture-handler';
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { HollyChatStoreInstance } from "../../../stores/HollyChatStore";
import { observer } from "mobx-react-lite";
import { socket } from "../../../networking/Socket";



const HollyChat: React.FC = observer(() => {
    const isFocused = useIsFocused();

    useEffect(() => {
        if (!isFocused) {
            socket.emit("user disconnected", senderId);
        }
      }, [isFocused]);


    // -- NAVIGATION -- //
    const navigation = useNavigation<any>();

    useEffect(() => {
        // senderId'yi MainStore'dan al
        setSenderId(MainStore.user.id);
    }, []);





    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    useEffect(() => {
        HollyChatStoreInstance.getChatInfo();
        HollyChatStoreInstance.getConversations();
        HollyChatStoreInstance.fetchUserInfo ();

      }, []);

      console.log('arkadaşlar böyle geliyor',HollyChatStoreInstance.friends)


    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingButton, setLoadingButton] = React.useState(false);

    const HollyChatSkeleton = () => {
        return (
          <SkeletonPlaceholder>
            <View style={styles.header} />


            <View style={styles.memberContainer} />
            <View style={styles.blockedContainer}>
              <View style={styles.blockedTitle} />
              <View style={styles.blockedItem}>
                <View style={styles.profileImage} />
                <View style={styles.textContainer}>
                  <View style={styles.nameText} />
                  <View style={styles.descriptionText} />
                </View>
                <View style={styles.teamText} />
              </View>
              <View style={styles.blockedItem}>
                <View style={styles.profileImage} />
                <View style={styles.textContainer}>
                  <View style={styles.nameText} />
                  <View style={styles.descriptionText} />
                </View>
                <View style={styles.teamText} />
              </View>
              <View style={styles.blockedItem}>
                <View style={styles.profileImage} />
                <View style={styles.textContainer}>
                  <View style={styles.nameText} />
                  <View style={styles.descriptionText} />
                </View>
                <View style={styles.teamText} />
              </View>
              <View style={styles.blockedItem}>
                <View style={styles.profileImage} />
                <View style={styles.textContainer}>
                  <View style={styles.nameText} />
                  <View style={styles.descriptionText} />
                </View>
                <View style={styles.teamText} />
              </View>
            </View>
          </SkeletonPlaceholder>
        );
      };


    // -- MENU -- //
    const [menu, setMenu] = React.useState(false);

    // -- TOP ACTIVE -- //
    const [active, setActive] = React.useState(false);

    // -- MODAL SETTINGS -- //
    const [modal, setModal] = React.useState(false);
    const [isFriend, setIsFriend] = React.useState(false);

    // -- CHAT INFO -- //
    const [activeUsers, setActiveUsers] = React.useState([]);
    const [friends, setFriends] = React.useState([]);
    const [selectedUser, setSelectedUser] = React.useState({ userId: 0, id: 0, image: '', firstName: '', lastName: '', isFriend: false });
    const [requestCount, setRequestCount] = React.useState(0);
    const [conversations, setConversations] = React.useState([]);



    const [conversations_, setConversations_] = React.useState([]);
    const [senderId, setSenderId] = useState<number | null>(null);
    const [snapData, setSnapData] = React.useState({}); // Kullanıcı ID'ye göre snap'leri saklayacağız



    const trimMessage = (input: string) => {
        if (input.length <= 40) {
            return input;
        } else {
            return input.substring(0, 37) + '...';
        }
    }


    // Function to update chat information by refreshing the HollyChatStore
    const updateChatInfo = () => {
        // Set loading state

        // Call the store methods to refresh data
        Promise.all([
            HollyChatStoreInstance.getChatInfo(),
            HollyChatStoreInstance.getConversations()
        ])
        .then(() => {
            // Data refreshed successfully
        })
        .catch((error) => {
            console.error("Chat info update error:", error);
            startToast("Sohbet bilgileri güncellenirken hata oluştu.", "error");
        });
    };

    const deleteConversation = (conversationPartnerId: number | string, startToastFn: (message: string, type: string) => void, updateChatInfoFn: () => void) => {
        Alert.alert(
          "Sohbeti Sil",
          "Bu sohbeti silmek istediğinize emin misiniz? Bu işlem yalnızca sizin tarafınızdan silinecektir.",
          [
            {
              text: "İptal",
              style: "cancel"
            },
            {
              text: "Sil",
              style: "destructive",
              onPress: () => {
                post("chat/delete-conversation", { conversationPartnerId })
                  .then((res: any) => {
                    if (res.type === "success") {
                      startToastFn(res.message, "success");
                      updateChatInfoFn(); // Sohbet listesini güncelle
                    } else {
                      startToastFn(res.error, "error");
                    }
                  })
                  .catch((error: any) => {
                    console.error("Sohbet silme hatası:", error);
                    startToastFn("Bir hata oluştu.", "error");
                  });
              }
            }
          ],
          { cancelable: true }
        );
      };


    const block = (id: number) => {
        setLoadingButton(true);
        post("chat/block", {
            id
        }).then((res: any) => {
            if (res.type == "success") {
                setModal(false);
                startToast(MainStore.language.banned, "success");
                setLoading(true);
            } else {
                startToast(res.error, "error");
            }
            setLoadingButton(false);
        })
    }

    const removeFriend = (id: number) => {
        setLoadingButton(true);
        post("chat/remove-friend", {
            id
        }).then((res: any) => {
            if (res.type == "success") {
                setModal(false);
                startToast(MainStore.language.offended, "success");
                setLoading(true);
            } else {
                startToast(res.error, "error");
            }
            setLoadingButton(false);
        })
    }

    const addFriend = (id: number) => {
        setLoadingButton(true);
        post("chat/add-friend", {
            id
        }).then((res: any) => {
            if (res.type == "success") {
                setModal(false);
                startToast(MainStore.language.sent_request, "success");
                setLoading(true);
            } else {
                startToast(res.error, "error");
            }
            setLoadingButton(false);
        })
    }

    const onSearch = (search: string) => {
        if (search) {
            const search_ = search.toLowerCase();
            let filterConDai = conversations_.filter((item: any) => {
                return (
                    (item.firstName.toLowerCase()).match(search_)
                );
            })
            setConversations(filterConDai);
        } else {
            setConversations(conversations_);
        }
    }



    return (
        <View
            style={styles.main}
        >
             {/* TOAST */}
             <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>

{
                        HollyChatStoreInstance.loading ?
                        <View style={styles.skelton}>
                            <HollyChatSkeleton />
                        </View>
                     :  <>




            {/* MODAL */}
            {
                modal ?
                    <View style={styles.modal}>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseTopTouch}
                        />
                        <View style={styles.modalAltView}>
                            <Image
                                style={styles.modalImg}
                                source={{ uri: getImageURL(selectedUser.image) }}
                            />
                            <View style={styles.modalTextsView}>
                                <Text style={styles.modalNameSurname}>{`${selectedUser.firstName} ${selectedUser.lastName}`}</Text>
                                <Text style={styles.modalDesc}>{MainStore.language.warnFriend}</Text>
                                {
                                    loadingButton ? <Spinner color={blue_t4} size={18} mt={5} /> :
                                        selectedUser.isFriend ?
                                            <View style={{
                                                flexDirection: 'row',
                                            }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        block(selectedUser.userId);
                                                    }}
                                                    style={[styles.modalButton, { backgroundColor: red_t2, width: 100 }]}
                                                >
                                                    <Text style={styles.modalButtonText}>{MainStore.language.ban} 😶</Text>
                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        removeFriend(selectedUser.userId);
                                                    }}
                                                    style={[styles.modalButton, { marginLeft: 10, width: 100 }]}
                                                >
                                                    <Text style={styles.modalButtonText}>😒 {MainStore.language.offen}</Text>
                                                </TouchableOpacity>
                                            </View>
                                            :
                                            <TouchableOpacity
                                                onPress={() => {
                                                    addFriend(selectedUser.userId);
                                                }}
                                                style={styles.modalButton}
                                            >
                                                <Text style={styles.modalButtonText}>{MainStore.language.add_friend}</Text>
                                            </TouchableOpacity>
                                }
                                <Image
                                    source={require('../../../assets/root/hollyChat.png')}
                                    style={styles.modalLogo}
                                />
                            </View>
                        </View>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseBottomTouch}
                        />
                    </View>
                    :
                    <></>
            }

            {/* MENU */}
            <Menu
                menuColor={blue_t4}
                navigation={navigation}
                menuStatus={menu}
                onMenu={(set: boolean) => {
                    setMenu(set);
                }}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* HEADER */}
                <HeaderFive
                    logoStyle={styles.logoStyle}
                    searchIconColor={blue_t4}
                    onSearch={(set: string) => {
                        onSearch(set)
                    }}
                    searchIcon={require('../../../assets/header/search.png')}
                    menuIcon={require('../../../assets/header/menuBlue.png')}
                    backIcon={blue_t1}
                    navigation={navigation}
                    logo={require('../../../assets/root/hollyChat.png')}
                    onMenu={(set: boolean | ((prevState: boolean) => boolean)) => {
                        setMenu(set);
                    }}
                    menuStatus={menu}
                />
                <SafeAreaView style={{ marginTop: 70 }}>
                    {
                        HollyChatStoreInstance.loading ? <View style={styles.spinnerview}>
                        <Image source={require('../../../assets/root/hollychatyeni.gif')} style={styles.loadingGif} />
                    </View> : (
                            <>
                                {/* NEW FRIEND NOTIFICATION */}
                                {
                                    HollyChatStoreInstance.requestCount > 0 ?
                                        <TouchableOpacity
                                            onPress={() => {
                                                navigation.navigate("NewFriend");
                                            }}
                                            style={styles.newFriendView}
                                        >
                                            <Text style={styles.newFriendText}>{MainStore.language.new_friend}</Text>
                                            <View style={styles.newFriendRightView}>
                                                <Text style={styles.newFriendRightText}>{requestCount}</Text>
                                            </View>
                                        </TouchableOpacity>
                                        :
                                        <></>
                                }


                                <View
                                    style={[
                                        styles.topBack,
                                        { backgroundColor: active ? gray_t16 : blue_t4, }
                                    ]}
                                >
                                    {
                                        active ?
                                            <Image
                                                source={require('../../../assets/root/friends.png')}
                                                style={styles.actOrUnacView}
                                            />
                                            :
                                            <Image
                                                source={require('../../../assets/root/activeMember.png')}
                                                style={styles.actOrUnacView}
                                            />
                                    }
                                    <View style={styles.topHeader}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                setActive(false);
                                            }}
                                            style={styles.actUncTouch}
                                        >
                                            <Text style={styles.activeText}>{MainStore.language.active_members}</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            onPress={() => {
                                                setActive(true);
                                            }}
                                            style={styles.actUncTouch}
                                        >
                                            <Text style={styles.friendsText}>{MainStore.language.friends}</Text>
                                        </TouchableOpacity>
                                    </View>
                                    <View style={styles.topInView}>
                                        <View style={styles.topInAltView}>
                                        {/* Sadece aktif kullanıcılar için mesaj */}
        {!active && HollyChatStoreInstance.activeUsers.length > 0 && HollyChatStoreInstance.activeUsers[0]?.message && (
            <Text
                style={[
                    styles.messageText,
                    { color: active ? 'white' : 'black' } // Aktif değilse siyah, aktifse beyaz
                ]}
            >
                {HollyChatStoreInstance.activeUsers[0].message}
            </Text>
        )}

                                            {
                                                active ?
                                                    <FlatList
                                                        scrollEnabled={false}
                                                        columnWrapperStyle={styles.columnWrapperStyle}
                                                        data={HollyChatStoreInstance.friends}
                                                        numColumns={5}
                                                        renderItem={({ item, index }: any) => {
                                                            if (index > 9) return (<></>)
                                                            return (
                                                                <View style={styles.ppView} key={index}>
                                                                    {/* AFTER NINE STATUS IN PROFILE PICTURE */}
                                                                    {
                                                                        index == 9 ?
                                                                            <TouchableOpacity
                                                                                onPress={() => {
                                                                                    navigation.navigate("AllFriends", { friends });
                                                                                }}
                                                                                style={styles.ppAfterNineView}
                                                                            >
                                                                                <Text style={styles.ppAfterNineText}>+{friends.length - 10}</Text>
                                                                            </TouchableOpacity>
                                                                            :
                                                                            <></>
                                                                    }
                                                                    <TouchableOpacity
                                                                        onLongPress={() => {
                                                                            setSelectedUser(item);
                                                                            setModal(true);
                                                                            setIsFriend(false);
                                                                        }}
                                                                        onPress={() => {
                                                                            const existingConversation = HollyChatStoreInstance.conversations.find(
                                                                                (conv) => conv.partner && conv.partner._id && conv.partner._id.toString() === item.userId.toString()
                                                                            );


                                                                            if (existingConversation) {
                                                                                navigation.navigate("ChatTwo", {
                                                                                    chatroomId: existingConversation._id || existingConversation.id,
                                                                                    messages: existingConversation.messages || [],
                                                                                    partner: {
                                                                                        _id: item.userId,
                                                                                        name: `${item.firstName} ${item.lastName}`,
                                                                                        avatar: item.image
                                                                                    },
                                                                                });
                                                                            } else {
                                                                                navigation.navigate("ChatTwo", {
                                                                                    chatroomId: null, // Server will create a new chatroom
                                                                                    messages: [], // No messages yet
                                                                                    partner: {
                                                                                        _id: item.userId,
                                                                                        name: `${item.firstName} ${item.lastName}`,
                                                                                        avatar: item.image
                                                                                    },
                                                                                });
                                                                            }
                                                                        }}
                                                                        style={shadow}
                                                                    >
                                                                        <Image
                                                                            source={{ uri: getImageURL(item.image) }}
                                                                            style={
                                                                                [
                                                                                    styles.pp,
                                                                                    {
                                                                                        borderWidth: item.snaps.length > 0 ? 2 : 0,
                                                                                        borderColor: item.snaps.find((item_: any) => item_.seen == "false") ? yellow_t2 : gray_t1
                                                                                    }
                                                                                ]
                                                                            }
                                                                        />
                                                                    </TouchableOpacity>
                                                                    <Text style={[styles.ppName, { color: active ? white : black }]}>{item.firstName}</Text>
                                                                </View>
                                                            )
                                                        }}
                                                    />
                                                    :
                                                    <FlatList
                                                        scrollEnabled={false}
                                                        columnWrapperStyle={styles.columnWrapperStyle}
                                                        data={HollyChatStoreInstance.activeUsers}
                                                        numColumns={5}
                                                        renderItem={({ item, index }: any) => {
                                                            if (index > 9) return (<></>)
                                                            return (
                                                                <View key={index} style={styles.ppView}>
                                                                    <TouchableOpacity
                                                                        onPress={() => {
                                                                            // Check if there's an existing conversation with this active user
                                                                            console.log('Debug => Looking for active user with ID:', item.userId);

                                                                            // Find conversation where partner._id matches the active user's userId
                                                                            const existingConversation = HollyChatStoreInstance.conversations.find(
                                                                                (conv) => conv.partner && conv.partner._id && conv.partner._id.toString() === item.userId.toString()
                                                                            );

                                                                            if (existingConversation) {
                                                                                // If there's an existing conversation, navigate to chat
                                                                                navigation.navigate("ChatTwo", {
                                                                                    chatroomId: existingConversation._id || existingConversation.id,
                                                                                    messages: existingConversation.messages || [],
                                                                                    partner: {
                                                                                        _id: item.userId,
                                                                                        name: `${item.firstName} ${item.lastName}`,
                                                                                        avatar: item.image
                                                                                    },
                                                                                });
                                                                            } else {
                                                                                // If no existing conversation, show the modal
                                                                                setSelectedUser(item);
                                                                                setModal(true);
                                                                                setIsFriend(true);
                                                                            }
                                                                        }}
                                                                        style={shadow}
                                                                    >
                                                                        <Image
                                                                            source={{ uri: getImageURL(item.image) }}
                                                                            style={styles.pp}
                                                                        />
                                                                    </TouchableOpacity>
                                                                    <Text style={[styles.ppName, { color: active ? white : black }]}>{item.firstName}</Text>
                                                                </View>
                                                            )
                                                        }}
                                                    />
                                            }
                                        </View>
                                    </View>

                                    {/* BLOCKED LIST */}
                                    <TouchableOpacity
                                        onPress={() => {
                                            navigation.navigate("Blocked");
                                        }}
                                        style={styles.blockedListTouch}
                                    >
                                        <Text style={styles.blockedTitleText}>{MainStore.language.blocked}</Text>
                                        <Image
                                            source={require('../../../assets/root/rightWhiteIcon.png')}
                                            style={styles.rightWhiteIcon}
                                        />
                                    </TouchableOpacity>

                                </View>
                                <View style={styles.messageMainListView}>
                                        {/* MESSAGE LIST */}
                                        {HollyChatStoreInstance.conversations.length < 1 ? (
                                            <Text style={styles.noChatVariable}>{MainStore.language.start_chat}</Text>
                                        ) : (
                                            HollyChatStoreInstance.conversations.map((item: any, index: number) => {
                                                const unreadCount = HollyChatStoreInstance.getUnreadMessageCount(item.id);
                                                const partnerId = item.partner._id; // Düzeltme burada
                                                const isOnline = HollyChatStoreInstance.onlineUsers.includes(partnerId.toString());
                                                const isTyping = item.isTyping;



                                                return (
                                                    // Burada her sohbet öğesini Swipeable ile sarmalıyoruz
                                                    <Swipeable
                                                        key={index}
                                                        renderLeftActions={(_progress, _dragX) =>
                                                            // Sağ aksiyon olarak trash butonu
                                                            <TouchableOpacity
                                                                onPress={() =>
                                                                    deleteConversation(item.id, startToast, updateChatInfo)
                                                                }
                                                                style={styles.trashButton}
                                                            >
                                                                <Image
                                                                    source={require('../../../assets/root/delete.png')}
                                                                    style={styles.trashIcon}
                                                                />
                                                            </TouchableOpacity>
                                                        }
                                                    >
                                                        <View>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    navigation.navigate("ChatTwo", {
                                                                        chatroomId: item._id,
                                                                        messages: item.messages,
                                                                        partner: {
                                                                            _id: item.partner._id,
                                                                            name: item.partner.name,
                                                                            avatar: item.partner.avatar
                                                                        },
                                                                    });
                                                                }}
                                                                style={styles.messageList}
                                                            >

                                                                <Image
                                                                    source={{ uri: getImageURL(item.partner.avatar) }}
                                                                    style={styles.pp}
                                                                />
                                                                <View style={styles.prfNameView}>
                                                                <View
      style={[
        styles.statusIndicator,
        { backgroundColor: isOnline ? 'green' : 'gray' }
      ]}
    />

                                                                    <Text style={styles.nameSurname}>{`${item.partner.name}`}</Text>
                                                                    <Text style={styles.message}>
  {/* Yazıyor göstergesi */}
  {isTyping ? (
                                <Text style={[styles.message, { fontStyle: 'italic', color: '#666' }]}>
                                    yazıyor...
                                </Text>
                            ) : (
                                <Text style={styles.message}>
                                    {item.messages && item.messages.length > 0 ? item.messages[0].text : ""}
                                </Text>
                            )}
</Text>

                                                                </View>
                                                                <View style={styles.statusView}>
                                                                    <Text style={styles.status}>{item.mood}</Text>
                                                                    <Text style={styles.time}>{dayjs(item.updatedAt).format("HH:mm")}</Text>
                                                                </View>
                                                                {unreadCount > 0 && (
                                <View style={styles.unreadBadge}>
                                    <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
                                </View>
                            )}
                                                            </TouchableOpacity>
                                                            <View style={styles.grayLine} />
                                                        </View>
                                                    </Swipeable>
                                                );
                                            })
                                        )}
                                    </View>
                                </>
                            )}
                        </SafeAreaView>
            </ScrollView>
            <BottomBar type={2} navigation={navigation} />
            </>
}
        </View>
    )
});
export default HollyChat;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi},
    toastView: {
        top: 0,
        alignSelf: 'center',
        zIndex: 20
    },
    statusIndicator: {
        position: 'absolute',
        top: 0,
        left: -23,
        width: 12,
        height: 12,
        borderRadius: 6,
        borderWidth: 2,
        borderColor: '#fff', // beyaz kenarlık
      },
    skelton:{
        flex: 1,
    },
    snapModal: {
        position: 'absolute',
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: 1000
    },
    spinnerview:{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingGif: {
        width: 230,
        height: 51,
    },
    unreadBadge: {
        backgroundColor: 'red',
        borderRadius: 50,
        width: 20,
        height: 20,
        position: 'absolute',
        right: 5,
        top: 30,
        justifyContent: 'center',
        alignItems: 'center'
    },
    header: { width: "40%", height: 40, borderRadius: 5, alignSelf: "flex-start", marginVertical: 10, left: 20, },
  tabContainer: { flexDirection: "row", justifyContent: "space-around", marginVertical: 10 },
  tab: { width: "40%", height: 30, borderRadius: 5 },
  memberContainer: { width: "90%", height: 250, borderRadius: 10, alignSelf: "center", marginTop: 15 },
  blockedContainer: { padding: 10 },
  blockedTitle: { width: "30%", height: 20, borderRadius: 5, marginBottom: 10 },
  blockedItem: { flexDirection: "row", alignItems: "center", marginTop: 20, paddingRight: 10 },
  profileImage: { width: 40, height: 40, borderRadius: 20, marginRight: 20, left: 10, },
  textContainer: { flex: 1 },
  nameText: { width: "50%", height: 15, borderRadius: 5, marginBottom: 5 },
  descriptionText: { width: "80%", height: 10, borderRadius: 5 },
  teamText: { width: "20%", height: 15, borderRadius: 5, marginLeft: "auto", top: -12 },
  invalidDate: { width: "25%", height: 10, borderRadius: 5, marginLeft: 5 },
    trashButton: {
        backgroundColor: red_t2,
        justifyContent: "center",
        alignItems: "center",
        width: 60,
        marginVertical: 20,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        left:10,
        height: 72
    },
    trashIcon: {
        width: 25,
        height: 25,
        tintColor: white,
    },
    unreadBadgeText: {
        color: white,
        fontWeight: 'bold',
        fontSize: 10
    },
    modal: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalAltView: {
        width: Layout.screen.width / 1.1,
        paddingVertical: 0,
        backgroundColor: "rgba(256,256,256,256)",
        borderRadius: 14,
        minHeight: 300
    },
    modalImg: {
        height: 143,
        width: 143,
        borderRadius: 71.5,
        alignSelf: 'center',
        borderWidth: 4,
        borderColor: white,
        top: -71.5
    },
    modalTextsView: {
        alignItems: 'center',
        top: -70
    },
    modalNameSurname: {
        color: black,
        fontSize: 20,
        fontWeight: 'bold'
    },
    modalDesc: {
        textAlign: 'center',
        fontSize: 14,
        margin: 20,
        color: black,
        marginTop: 10,
        fontWeight: 'bold'
    },
    modalButton: {
        height: 40,
        paddingHorizontal: 10,
        backgroundColor: blue_t4,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalButtonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: white
    },
    modalLogo: {
        position: 'absolute',
        bottom: -60,
        height: 28,
        width: 126
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    newFriendView: {
        borderRadius: 16,
        height: 45,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        backgroundColor: blue_t4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    newFriendText: {
        fontWeight: 'bold',
        color: white,
        marginRight: 45,
    },
    newFriendRightView: {
        backgroundColor: red_t5,
        height: 45,
        position: 'absolute',
        right: 0,
        width: 60,
        alignItems: 'center',
        justifyContent: 'center',
        borderTopRightRadius: 16,
        borderBottomRightRadius: 16
    },
    newFriendRightText: {
        fontSize: 18,
        color: white,
        fontWeight: 'bold'
    },
    logoStyle: {
        height: 28,
        position: 'absolute',
        left: 45,
        width: 126,
        top: 22
    },
    topBack: {
        height: (Layout.screen.width / 1.1) / 1.5,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20,
        borderRadius: 20
    },
    actOrUnacView: {
        height: (Layout.screen.width / 1.1) / 1.5,
        width: Layout.screen.width / 1.1,
        borderRadius: 18,
        position: 'absolute',
        zIndex: -1
    },
    topHeader: {
        width: Layout.screen.width / 1.1,
        flexDirection: 'row',
        height: ((Layout.screen.width / 1.1) / 1.5) / 8,
    },
    actUncTouch: {
        width: '50%',
        alignItems: 'center',
        justifyContent: 'center'
    },
    activeText: {
        color: black,
        fontSize: 14,
        fontWeight: 'bold'
    },
    friendsText: {
        color: white,
        fontSize: 14,
        fontWeight: 'bold'
    },
    topInView: {
        width: Layout.screen.width / 1.1,
        height: (Layout.screen.width / 1.1) / 1.75,
        padding: 10
    },
    topInAltView: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    columnWrapperStyle: {
        paddingVertical: 14,

    },
    ppView: {
        alignItems: 'center',
        height: 52,
        width: 52,
        marginHorizontal: 5,
    },
    ppAfterNineView: {
        position: 'absolute',
        height: 52,
        zIndex: 2,
        width: 52,
        borderRadius: 26,
        backgroundColor: blue_t4,
        opacity: 0.8,
        alignItems: 'center',
        justifyContent: 'center'
    },
    ppAfterNineText: {
        fontSize: 16,
        color: white,
        fontWeight: 'bold'
    },
    pp: {
        height: 52,
        borderRadius: 26,
        width: 52
    },
    ppName: {
        fontWeight: 'bold',
        fontSize: 8,
        marginTop: 5
    },
    blockedListTouch: {
        backgroundColor: black,
        height: 39,
        zIndex: -2,
        width: Layout.screen.width / 1.14,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        alignSelf: 'center',
        top: -10,
        alignItems: 'flex-end',
        paddingHorizontal: 15,
        justifyContent: 'space-between',
        paddingBottom: 4,
        flexDirection: 'row'
    },
    blockedTitleText: {
        color: white,
        fontWeight: 'bold'
    },
    rightWhiteIcon: {
        width: 13,
        height: 18
    },
    messageMainListView: { marginTop: 40 },
    noChatVariable: {
        textAlign: 'center',
        fontWeight: 'bold',
        color: blue_t4,
        margin: 20
    },
    messageList: {
        marginVertical: 20,
        flexDirection: 'row',
        width: Layout.screen.width / 1.15,
        alignSelf: 'center'
    },
    prfNameView: { marginLeft: 10 },
    nameSurname: {
        fontWeight: 'bold',
        color: black_t3,
    },
    message: {
        marginTop: 5,
        fontSize: 12,
        color:"gray"
    },
    messageSeen: {
        fontWeight: 'bold',
        color: green_t1
    },
    messageDelivered: {
        fontWeight: 'bold',
        color: green_t1
    },
    statusView: {
        position: 'absolute',
        right: 0,
        top: -20,
        alignItems: 'flex-end'
    },
    status: {
        marginTop: 5,
        fontSize: 12,
        color: brown_t3,
        fontWeight: 'bold'
    },
    time: {
        fontSize: 10,
        marginTop: 5,
        color: "gray"
    },
    grayLine: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        height: 2,
        backgroundColor: gray_t13
    },
    messageText: {
        alignSelf: 'center',
        alignContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 100,
        borderRadius: 10,
        paddingHorizontal: 10,
        paddingVertical: 10,
        marginTop: 10,
        textAlign: 'center',
        textAlignVertical: 'center',
        color: black_t3
    }
});
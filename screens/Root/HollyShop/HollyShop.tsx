import React from "react";
import { Platform, SafeAreaView, ScrollView, StyleSheet, View } from "react-native";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import HollyShopTopList from "../../../components/HollyShopTopList";
import OtherProducts from "../../../components/OtherProducts";
import SpecialProducts from "../../../components/SpecialProducts";
import { kremrengi } from "../../../constants/Color";
import BottomBar from "../../../components/BottomBar";
import Layout from "../../../constants/Layout";
import { HollyShopStoreInstance as HollyShopStore } from "../../../stores/HollyShopStore"; // HollyShopStore'u import et
import { observer } from "mobx-react-lite"; // MobX observer

const HollyShop: React.FC = observer(() => {

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();


    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };


    return (
        <View
            style={styles.main}
        >
                     
            <SafeAreaView>

                {/* HEADER */}
                <SafeAreaView style={{ 
                    zIndex: 10,
                    position:'absolute',
                    width:Layout.screen.width,
                    height: 70,
                     }}>
                    <HeaderThree
                        basket={true}
                        search={true}
                        headerStatus={header}
                        navigation={navigation}
                    />
                </SafeAreaView>

                <ScrollView
                    onScroll={handleScroll}
                    style={{ marginBottom: Platform.OS == "android" ? 120 : 40, marginTop: 0 }}
                    showsVerticalScrollIndicator={false}
                >

                   
                                    {/* CATEGORIES */}
                                    <HollyShopTopList categories={HollyShopStore.categories} />

                                    {/* FIRST TWO PRODUCT */}
                                    <SpecialProducts products={[HollyShopStore.products[0], HollyShopStore.products[1]]} navigation={navigation} />

                                    {/* OTHER PRODUCTS */}
                                    <OtherProducts isSearch={false} products={HollyShopStore.products} navigation={navigation} />
                                
                           
                </ScrollView>
            </SafeAreaView>
            <BottomBar navigation={navigation} type={4} />
            
        </View >
);
});
export default HollyShop;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1, 
    backgroundColor: kremrengi},
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingGif: {
        width: 250,
        height: 30,
    },
});
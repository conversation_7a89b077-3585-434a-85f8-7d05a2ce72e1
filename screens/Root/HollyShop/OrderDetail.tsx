import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Layout from "../../../constants/Layout";
import { black_t3, brown_t2, gray_t4, orange_t1, white, yellow_t1 } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { get, getImageURL } from "../../../networking/Server";
import { Spinner } from "native-base";
import dayjs from "dayjs";

const OrderDetail: React.FC = (props: any) => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    const [loading, setLoading] = React.useState(true);
    const [details, setDetails]: any = React.useState([]);

    const getOrderDetails = () => {
        get(`/shop/order-details?orderId=${props?.route?.params?.orderId}`).then((res: any) => {
            if (res.type == "success") {
                setDetails(res.data);
                setLoading(false);
            } else {
                navigation.pop();
            }
        });
    }

    React.useEffect(() => {
        getOrderDetails();
    }, []);

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >
            <SafeAreaView>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <HeaderThree
                        basket={false}
                        search={false}
                        navigation={navigation}
                    />

                    {
                        loading ? <Spinner /> : (
                            <>
                                {/* ORDER DETAIL */}
                                <View style={[styles.topView, shadow]}>
                                    <View style={styles.productTitleFollowView}>
                                        <Text style={styles.productTitle}>{details?.carrier}</Text>
                                        <TouchableOpacity>
                                            <Text style={[styles.productTitle, { color: brown_t2, textDecorationLine: 'underline' }]}>takip et</Text>
                                        </TouchableOpacity>
                                    </View>
                                    <View style={styles.topAltView}>
                                        <Text style={styles.topAltLeftText}>Sipariş no:</Text>
                                        <Text style={styles.topAltRightText}>{details?.trackingNumber}</Text>
                                    </View>
                                    <View style={[styles.topAltView, { marginTop: 5 }]}>
                                        <Text style={styles.topAltLeftText}>Sipariş tarihi:</Text>
                                        <Text style={styles.topAltRightText}>{dayjs(details?.orderDate).format('DD MMM YYYY')}</Text>
                                    </View>
                                    <View style={[styles.topAltView, { marginTop: 5 }]}>
                                        <Text style={styles.topAltLeftText}>Tahmini teslimat: </Text>
                                        <Text style={styles.topAltRightText}>{details?.estimatedDeliveryDate ? dayjs(details?.estimatedDeliveryDate).format('DD MMM YYYY') : ""}</Text>
                                    </View>
                                    <View style={[styles.topAltView, { marginTop: 5 }]}>
                                        <Text style={styles.topAltLeftText}>Takip no: </Text>
                                        <Text style={styles.topAltRightText}>{details?.deliveryTrackingNumber}</Text>
                                    </View>
                                    <View style={[styles.topAltView, { marginTop: 5 }]}>
                                        <Text style={styles.topAltLeftText}>Sipariş özeti:</Text>
                                        <Text style={[styles.topAltRightText, { color: brown_t2 }]}>{
                                            details?.orderStatus == 1 ?
                                                "Hazırlanıyor"
                                                :
                                                details?.orderStatus == 2 ?
                                                    "Teslim Edildi"
                                                    :
                                                    details?.orderStatus == 3 ?
                                                        "Kargoda"
                                                        :
                                                        details?.orderStatus == 4 ?
                                                            ""
                                                            :
                                                            "İade Edildi"
                                        }</Text>
                                    </View>
                                    <View style={[styles.topAltView, { marginTop: 5 }]}>
                                        <Text style={styles.topAltLeftText}>Toplam:</Text>
                                        <Text style={[styles.topAltRightText, { color: brown_t2 }]}>{details?.price}₺</Text>
                                    </View>
                                </View>

                                {/* PRODUCT DETAIL */}
                                {
                                    details?.products?.map((item: any, index: number) => {
                                        return (
                                            <View key={index} style={[styles.topView, shadow, { marginTop: 20 }]}>
                                                {/* PRODUCT NAME */}
                                                <Text style={styles.productName}>{item?.name}</Text>

                                                {/* PRODUCT ID */}
                                                <Text style={styles.productId}>({item?.code})</Text>

                                                {/* PRODUCT IMAGE */}
                                                <Image
                                                    source={{ uri: getImageURL(item.images[0]) }}
                                                    style={styles.productImage}
                                                    resizeMode="contain"
                                                />

                                                <View style={styles.productAttributesView}>

                                                    {/* PRODUCT ATTRIBUTES */}
                                                    <View>
                                                        {/* PIECE */}
                                                        <Text style={styles.productPiece}>adet: {item?.quantity}</Text>

                                                        {/* SIZE */}
                                                        <Text style={styles.productSize}>beden: {item?.selectedOption}</Text>

                                                        {/* PRICE */}
                                                        <Text style={styles.productPrice}>{item?.price}₺</Text>
                                                    </View>

                                                    {/* RIGHT BUTTONS */}
                                                    <View>
                                                        {/* EXTRADITION REQUEST BUTTON */}
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                navigation.navigate("Return");
                                                            }}
                                                            style={styles.extraditionButton}
                                                        >
                                                            <Text style={styles.extraditionText}>İade Talebi</Text>
                                                        </TouchableOpacity>

                                                        {/* EVALUATE */}
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                navigation.navigate("Evaluate");
                                                            }}
                                                            style={styles.evaluateButton}
                                                        >
                                                            <Text style={styles.evaluate}>Değerlendir</Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                            </View>
                                        )
                                    })
                                }

                                {/* ADDRESS INFORMATION */}
                                <View style={[styles.topView, shadow, { marginTop: 20 }]}>
                                    <Text style={styles.addressTitle}>Teslimat Adresi</Text>
                                    <View style={[styles.addressView, shadow]}>
                                        <Text style={styles.addressViewTitle}>{details?.addressTitle}</Text>
                                        <Text style={styles.address}>{details?.fullAddress?.length > 40 ? details?.fullAddress?.slice(0, 40) + '...' : details?.fullAddress}</Text>
                                    </View>
                                </View>

                                {/* PAYMENT INFORMATION */}
                                <View style={[styles.topView, shadow, { marginTop: 20 }]}>
                                    <View style={styles.paymentView}>
                                        <Text style={styles.paymentTitle}>Ödeme Bilgileri</Text>
                                        <Text style={styles.paymentOption}>Tek Çekim</Text>
                                    </View>
                                    <View style={[styles.paymentAltView, shadow]}>
                                        <View>
                                            <Text style={styles.paymentAltName}>{details?.cName}</Text>
                                            <Text style={styles.paymentAltCardNo}>**** **** **** {details?.cLastFour}</Text>
                                        </View>
                                        <Image
                                            style={{
                                                width: 20,
                                                height: 12
                                            }}
                                            source={details?.cSchema == "MASTERCARD" ?
                                                require('../../../assets/exp/masterCard.png')
                                                :
                                                details?.cSchema == "VISA" ?
                                                    require('../../../assets/exp/visa.png')
                                                    :
                                                    require('../../../assets/exp/cardIcon.png')}
                                        />
                                    </View>
                                </View>

                                {/* CONTRACT */}
                                <TouchableOpacity style={[styles.contractView, shadow]}>
                                    <Text style={styles.contractText}>Ön Bilgilendirme Koşulları’nı ve Mesafeli Satış Sözleşmesi</Text>
                                </TouchableOpacity>
                            </>
                        )
                    }
                </ScrollView>
            </SafeAreaView>
        </ImageBackground>
    )
}
export default OrderDetail;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1 },
    topView: {
        borderRadius: 10,
        padding: 10,
        width: Layout.screen.width / 1.1,
        backgroundColor: white,
        alignSelf: 'center',
        paddingHorizontal: 15
    },
    topAltView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    topAltLeftText: {
        color: black_t3,
        fontSize: 15,
    },
    topAltRightText: {
        marginLeft: 5,
        fontWeight: 'bold',
        fontSize: 15,
        color: black_t3
    },
    productTitleFollowView: {
        marginBottom: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    productTitle: {
        fontWeight: 'bold',
        color: black_t3
    },
    productAltView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8
    },
    productLeftText: {
        fontSize: 15,
        color: black_t3
    },
    productRightText: {
        fontWeight: 'bold',
        fontSize: 15,
        color: black_t3
    },
    productName: {
        alignSelf: 'center',
        marginTop: 15,
        fontSize: 22,
        fontWeight: 'bold',
        color: black_t3
    },
    productId: {
        alignSelf: 'center',
        marginTop: 2,
        fontSize: 14,
        color: black_t3
    },
    productImage: {
        width: 150,
        height: 150,
        marginTop: 10,
        alignSelf: 'center'
    },
    productAttributesView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 15
    },
    productPiece: {
        color: black_t3,
        fontSize: 14
    },
    productSize: {
        marginTop: 5,
        fontSize: 14,
        color: black_t3
    },
    productPrice: {
        color: orange_t1,
        fontSize: 14,
        fontWeight: 'bold',
        marginTop: 5
    },
    extraditionButton: {
        width: 120,
        backgroundColor: gray_t4,
        alignItems: 'center',
        justifyContent: 'center',
        height: 34,
        borderRadius: 10
    },
    extraditionText: {
        fontSize: 14,
        color: white,
        fontWeight: 'bold'
    },
    evaluateButton: {
        width: 120,
        marginTop: 10,
        backgroundColor: brown_t2,
        alignItems: 'center',
        justifyContent: 'center',
        height: 34,
        borderRadius: 10
    },
    evaluate: {
        fontSize: 14,
        color: white,
        fontWeight: 'bold'
    },
    addressTitle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: black_t3
    },
    addressView: {
        padding: 10,
        borderWidth: 1,
        borderRadius: 8,
        marginTop: 8,
        borderColor: gray_t4,
        backgroundColor: white
    },
    addressViewTitle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: black_t3
    },
    address: {
        fontSize: 12,
        color: gray_t4
    },
    paymentView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    paymentTitle: {
        fontSize: 12,
        fontWeight: 'bold',
        color: black_t3
    },
    paymentOption: {
        color: black_t3,
        fontSize: 9,
    },
    paymentAltView: {
        padding: 10,
        borderWidth: 1,
        borderRadius: 8,
        marginTop: 8,
        borderColor: brown_t2,
        backgroundColor: white,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    paymentAltName: {
        fontSize: 10,
        color: black_t3
    },
    paymentAltCardNo: {
        fontSize: 10,
        color: gray_t4,
        fontWeight: 'bold',
        marginTop: 2
    },
    cardIcon: {
        width: 28,
        height: 17
    },
    contractView: {
        backgroundColor: yellow_t1,
        borderRadius: 10,
        padding: 10,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginVertical: 20
    },
    contractText: {
        color: white,
        fontSize: 12,
        fontWeight: 'bold'
    }
});
//@ts-nocheck
import React from "react";
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { black, black_t3, blue_t2, brown_t2, gray_t1, gray_t12, green_t2, gray_t3, gray_t4, white, yellow_t1 } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import { shadow } from "../../../constants/Shadow";
import { Button, CheckIcon, Input, Select, Spinner, TextArea } from "native-base";
import Toast from "../../../components/Toast";
import { get, post } from "../../../networking/Server";
import WebView from "react-native-webview";
import { MainStore } from "../../../stores/MainStore";
import AddCardModal from "../Concert/AddCardModal"; // ✅ MODAL ENTEGRE EDİLDİ


const AddressCard: React.FC = (props: any) => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);
    const [loadingTwo, setLoadingTwo] = React.useState(true);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- SELECTED ADDRESS -- //
    const [addAddress, setAddAddress] = React.useState(false);
    const [addAddressTwo, setAddAddressTwo] = React.useState(false);
    const [city, setCity] = React.useState("");
    const [district, setDistrict] = React.useState("");
    const [neighborhood, setNeighborhood] = React.useState("");
    const [addressTitle, setAddressTitle] = React.useState("");
    const [postalCode, setPostalCode] = React.useState("");
    const [fullAddress, setFullAddress] = React.useState("");
    const [identityNumber, setIdentityNumber] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [phoneNumber, setPhoneNumber] = React.useState("");
    const [userAddresses, setUserAddresses] = React.useState([]);
    const [userBillingAddresses, setUserBillingAddresses] = React.useState([]);

    // -- SELECTED ADDRESSES -- //
    const [selectedAddress, setSelectedAddress] = React.useState(0);
    const [selectedBillingAddress, setSelectedBillingAddress] = React.useState(0);

    // -- CONSTAINTS -- //
    const [cities, setCities] = React.useState([]);
    const [districts, setDistricts] = React.useState([]);
    const [neighborhoods, setNeighborhoods] = React.useState([]);

    // -- SAME WHERE -- //
    const [same, setSame] = React.useState(false);

    // -- CARDS -- //
    const [addCard, setAddCard] = React.useState(false);

    // -- CONDITION -- // 
    const [condition, setCondition] = React.useState(false);

    // -- TAX -- //
    const [tax, setTax] = React.useState(0); // 0 -> OTH | 1 -> IND
    const [vkn, setVkn] = React.useState("");
    const [vDepartment, setVDepartment] = React.useState("");
    const [companyName, setCompanyName] = React.useState("");
    const [electronic, setElectronic] = React.useState(false);


    // -- CARD INFOS -- //
    const [cards, setCards] = React.useState([]);
    const [cardNo, setCardNo] = React.useState("");
    const [cardName, setCardName] = React.useState("");
    const [cardDate, setCardDate] = React.useState("");
    const [cvv, setCvv] = React.useState("");
    const [cardsLoading, setCardsLoading] = React.useState(true);
    const [saveCardsLoading, setSaveCardsLoading] = React.useState(false);
    const [selectedCard, setSelectedCard] = React.useState({});
    const [paytrLink, setPaytrLink] = React.useState("");
    const [localCards, setLocalCards] = React.useState([]); // Kullanıcının eklediği yeni kartlar
    const [isModalVisible, setIsModalVisible] = React.useState(false);


    const webview: any = React.useRef(null);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const getCards = () => {
        try {
            get("payment/card-list").then((res: any) => {
                setCardsLoading(false);
                if (res.type == "success") {
                    setCards(res.list);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    navigation.pop();
                }
            })
        } catch (e) {
            setCardsLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
            navigation.pop();
        }
    }

    const saveCard = () => {
        const date = (cardDate.split("/"));
        setSaveCardsLoading(true);
        try {
            post("/payment/store-card", {
                cc_owner: cardName,
                card_number: cardNo,
                expiry_month: date[0],
                expiry_year: date[1],
                cvv: cvv
            }).then((res: any) => {
                setSaveCardsLoading(false);
                if (res.type == "success") {
                    setPaytrLink(res.paymentLink);
                    startToast("Ödemeye aktarılıyorsunuz.", "success");
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    navigation.pop();
                }
            });
        } catch (e) {
            setSaveCardsLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
            navigation.pop();
        }
    }

    const addNewCard = (card) => {
        setLocalCards([...localCards, card]); // Yeni kartı ekleyerek listeyi güncelle
        setSelectedCard(card); // Yeni kartı seçili olarak ata
        setIsModalVisible(false); // Modalı kapat
    };

    const allCards = [...cards, ...localCards];




    // -- GET CITIES -- //
    const getCities = () => {
        try {
            get("/cities").then((res: any) => {
                if (res.type == "success") {
                    setCities(res.cities);
                    setDistricts([]);
                    setDistrict("");
                    setNeighborhoods([]);
                    setNeighborhood("");
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- GET DISTRICTS -- //
    const getDistricts = (cityKey: number) => {
        try {
            get("/districts?cityKey=" + cityKey).then((res: any) => {
                if (res.type == "success") {
                    setDistricts(res.districts);
                    setDistrict("");
                    setNeighborhoods([]);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- GET NEIGHBORHOODS -- //
    const getNeighborhoods = (districtKey: number) => {
        try {
            get("/neighborhoods?districtKey=" + districtKey).then((res: any) => {
                if (res.type == "success") {
                    setNeighborhoods(res.neighborhoods);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- GET ADDRESS -- //
    const getAddress = () => {
        try {
            get("/users/addresses").then((res: any) => {
                if (res.type == "success") {
                    setUserAddresses(res.userAddresses);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }
                setLoading(false);
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- GET BILLING ADDRESS -- //
    const getBillingAddress = () => {
        try {
            get("/users/billing-addresses").then((res: any) => {
                if (res.type == "success") {
                    setUserBillingAddresses(res.userBillingAddresses);
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }
                setLoadingTwo(false);
            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    // -- ADD USER BILLING ADDRESS -- //
    const addUserBillingAddress = () => {
        setLoading(true);
        try {
            post("users/add-billing-address", {
                title: addressTitle,
                city,
                district,
                neighborhood,
                postalCode,
                fullAddress,
                identityNumber,
                email,
                phoneNumber
            }).then((res: any) => {
                setLoading(false);
                if (res.type == "success") {
                    startToast("Yeni fatura adresi kaydı başarılı!", "success");
                    setUserBillingAddresses(res.address);
                } else {
                    startToast(res.error, "error");
                    setLoading(false);
                }

            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setLoading(false);
            return false;
        }
    }

    // -- ADD USER ADDRESS -- //
    const addUserAddress = () => {
        setLoading(true);
        try {
            post("users/add-address", {
                title: addressTitle,
                city,
                district,
                neighborhood,
                postalCode,
                fullAddress,
                identityNumber,
                email,
                phoneNumber
            }).then((res: any) => {
                setLoading(false);
                if (res.type == "success") {
                    startToast("Yeni adres kaydı başarılı!", "success");
                    setUserAddresses(res.address);
                } else {
                    startToast(res.error, "error");
                }
            })
        } catch (e) {
            setLoading(false);
            startToast("Bir şeyler ters gitti.", "error");
            return false;
        }
    }

    // -- DELETE ALL STATES WHEN ADDRESS ADDRESSTWO -- //
    React.useEffect(() => {
        setAddressTitle("");
        setCity("");
        setDistrict("");
        setNeighborhood("");
        setPostalCode("");
        setFullAddress("");
        setIdentityNumber("");
        setEmail("");
        setPhoneNumber("");
    }, [addAddress, addAddressTwo]);

    React.useEffect(() => {
        getCards();
        getAddress();
        getBillingAddress();
        getCities();
    }, []);



    // -- WEBVIEW PAYTR -- //
    const handleWebViewNavigationStateChange = (params: { url: any; }) => {
        const { url } = params;
        if (url?.includes('success')) {
            //webview.current.stopLoading();
            //webview.current = null;
            setPaytrLink("");
            getCards();
            startToast("Kartınız başarıyla kayıt edilmiştir.", "success")
        } else if (url?.includes('fail')) {
            startToast("İşlem reddedildi.", "error")
            setPaytrLink("");
        }
    }

    if (paytrLink) {
        return (
            <WebView
                ref={webview}
                style={styles.containerPayTr}
                source={{ uri: paytrLink }}
                onNavigationStateChange={handleWebViewNavigationStateChange}
            />
        )
    }

    return (
        <View style={styles.main}>
            <SafeAreaView style={styles.safeAreaView}>
                {/* TOAST */}
                <View style={styles.toastView}>
                    <Toast
                        type={typeToast}
                        subtitle={subtitleToast}
                        status={statusToast}
                        successColor={brown_t2}
                    />
                </View>

                <SafeAreaView style={{
                    zIndex: 10,
                    position: 'absolute',
                    width: Layout.screen.width
                }}>
                    <HeaderThree
                        headerStatus={header}
                        basket={false}
                        search={false}
                        navigation={navigation}
                    />
                </SafeAreaView>

                <ScrollView
                    onScroll={handleScroll}
                    showsVerticalScrollIndicator={false}
                >

                    {
                        loading || loadingTwo || cardsLoading ?
                            <Spinner size={18} mt={20} color={brown_t2} />
                            :
                            <>

                                {/* ADDRESS */}
                                <View style={[styles.addressView, { marginTop: 80 }]}>
                                    <Text style={styles.addressTitle}>Teslimat Adres Bilgisi</Text>
                                    <View style={styles.addressAltView}>
                                        <Text style={styles.addressAltTitle}>kayıtlı adreslerim</Text>
                                        {
                                            userAddresses?.map((item: any, index: number) => {
                                                return (
                                                    <TouchableOpacity
                                                        onPress={() => {
                                                            setSelectedAddress(item.id)
                                                        }}
                                                        key={index}
                                                        style={[styles.addressItemView, shadow, { borderColor: item.id == selectedAddress ? gray_t1 : white }]}
                                                    >
                                                        <Text style={styles.addressItemTitle}>{item?.title}</Text>
                                                        <Text style={styles.addressItemDesc}>{item?.fullAddress}</Text>
                                                    </TouchableOpacity>
                                                )
                                            })
                                        }
                                    </View>
                                    {
                                        addAddress ?
                                            <View style={styles.addressAltView}>
                                                <Text style={styles.addressAltTitle}>farklı adresteyim</Text>

                                                {/* ADDRESS NAME */}
                                                <Input
                                                    mt={2}
                                                    placeholderTextColor={black}
                                                    borderColor={gray_t3}
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholder="Adres Adı"
                                                    value={addressTitle}
                                                    onChange={(event) => setAddressTitle(event.nativeEvent.text)}
                                                />
                                                {/* IDENTITY NUMBER */}
                                                <Input
                                                    mt={2}
                                                    placeholderTextColor={black}
                                                    borderColor={gray_t3}
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholder="Kimlik Numarası"
                                                    value={identityNumber}
                                                    onChange={(event) => setIdentityNumber(event.nativeEvent.text)}
                                                />
                                                {/* PHONE NUMBER */}
                                                <Input
                                                    mt={2}
                                                    placeholderTextColor={black}
                                                    borderColor={gray_t3}
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholder="Telefon Numarası"
                                                    value={phoneNumber}
                                                    onChange={(event) => setPhoneNumber(event.nativeEvent.text)}
                                                />
                                                {/* EMAIL */}
                                                <Input
                                                    mt={2}
                                                    placeholderTextColor={black}
                                                    borderColor={gray_t3}
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholder="E-mail"
                                                    value={email}
                                                    onChange={(event) => setEmail(event.nativeEvent.text)}
                                                />
                                                <View style={styles.selectView}>
                                                    {/* ADDRESS CITY */}
                                                    <Select
                                                        selectedValue={city}
                                                        minWidth={Layout.screen.width / 2.5}
                                                        placeholder="Şehir"
                                                        placeholderTextColor={black}
                                                        borderRadius={8}
                                                        shadow={0}
                                                        _selectedItem={{
                                                            bg: "teal.600",
                                                            endIcon: <CheckIcon size="5" />
                                                        }} mt={1}
                                                        onValueChange={(itemValue: any) => {
                                                            getDistricts(itemValue);
                                                            setCity(itemValue);
                                                        }}
                                                    >
                                                        {
                                                            cities.map((item: any, index: React.Key) => {
                                                                return (
                                                                    <Select.Item
                                                                        key={index}
                                                                        label={item?.title}
                                                                        value={item?.key}
                                                                    />
                                                                )
                                                            })
                                                        }
                                                    </Select>
                                                    {/* ADDRESS DISTRICT */}
                                                    <Select
                                                        shadow={0}
                                                        selectedValue={district}
                                                        minWidth={Layout.screen.width / 2.5}
                                                        placeholder="İlçe"
                                                        placeholderTextColor={black}
                                                        borderRadius={8}
                                                        _selectedItem={{
                                                            bg: "teal.600",
                                                            endIcon: <CheckIcon size="5" />
                                                        }} mt={1}
                                                        onValueChange={(itemValue: any) => {
                                                            getNeighborhoods(itemValue);
                                                            setDistrict(itemValue);
                                                        }}
                                                    >
                                                        {
                                                            districts.map((item: any, index: React.Key) => {
                                                                return (
                                                                    <Select.Item
                                                                        key={index}
                                                                        label={item?.title}
                                                                        value={item?.key}
                                                                    />
                                                                )
                                                            })
                                                        }
                                                    </Select>
                                                </View>

                                                <View style={styles.selectView}>
                                                    {/* ADDRESS NEIGHBOURHOOD */}
                                                    <Select
                                                        selectedValue={neighborhood}
                                                        minWidth={Layout.screen.width / 2.5}
                                                        placeholder="Mahalle / Köy"
                                                        borderRadius={8}
                                                        shadow={0}
                                                        placeholderTextColor={black}
                                                        _selectedItem={{
                                                            bg: "teal.600",
                                                            endIcon: <CheckIcon size="5" />
                                                        }} mt={1}
                                                        onValueChange={itemValue => setNeighborhood(itemValue)}
                                                    >
                                                        {
                                                            neighborhoods.map((item: any, index: React.Key) => {
                                                                return (
                                                                    <Select.Item
                                                                        key={index}
                                                                        label={item?.title}
                                                                        value={item?.key}
                                                                    />
                                                                )
                                                            })
                                                        }
                                                    </Select>
                                                    {/* ADDRESS POSTCODE */}
                                                    <Input
                                                        minW={Layout.screen.width / 2.5}
                                                        borderColor={gray_t3}
                                                        height={33}
                                                        mt={1}
                                                        placeholderTextColor={black}
                                                        borderRadius={8}
                                                        shadow={0}
                                                        value={postalCode}
                                                        onChange={(event) => setPostalCode(event.nativeEvent.text)}
                                                        placeholder="Posta Kodu"
                                                    />
                                                </View>

                                                {/* @ts-ignore ADDRESS */}
                                                <TextArea
                                                    borderWidth={1}
                                                    mt={2}
                                                    placeholder="Açık Adres"
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholderTextColor={black}
                                                    minHeight={75}
                                                    fontSize={14}
                                                    color={black}
                                                    value={fullAddress}
                                                    onChange={(event) => setFullAddress(event.nativeEvent.text)}
                                                />
                                            </View>
                                            :
                                            <></>
                                    }
                                    <Button
                                        onPress={() => {
                                            if (!addAddress) {
                                                setAddAddress(!addAddress);
                                                return;
                                            } else {
                                                if (
                                                    addressTitle === "" ||
                                                    city === "" ||
                                                    district === "" ||
                                                    neighborhood === "" ||
                                                    postalCode === "" ||
                                                    fullAddress === "" ||
                                                    phoneNumber.length !== 13 ||
                                                    identityNumber.length !== 11
                                                ) {
                                                    startToast("Tüm alanları doldurun ve doğru formatta bilgi girin.", "error");
                                                    return;
                                                }

                                                setAddAddress(!addAddress);
                                                addUserAddress();
                                            }
                                        }}
                                        minW={138}
                                        style={styles.differentAddressButton}
                                    >
                                        <Text style={styles.differentAddress}>
                                            {
                                                addAddress ?
                                                    "kaydet"
                                                    :
                                                    "farklı adresteyim"
                                            }
                                        </Text>
                                    </Button>
                                </View>

                                {/* SAME WHERE */}
                                <View style={styles.sameWhereView}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setSame(!same);
                                        }}
                                        style={styles.sameWhereButton}>
                                        {
                                            same ?
                                                <View style={styles.sameWhereButtonAlt} />
                                                :
                                                <></>
                                        }
                                    </TouchableOpacity>
                                    <Text style={styles.sameWhereText}>Faturamı aynı adrese gönder.</Text>
                                </View>

                                {/* DIFFERENT ADDRESS FOR TAX ADDRESS */}
                                {
                                    !same ?
                                        <View style={styles.addressView}>
                                            <Text style={styles.addressTitle}>Fatura Adres Bilgisi</Text>
                                            <View style={styles.addressAltView}>
                                                <Text style={styles.addressAltTitle}>kayıtlı adreslerim</Text>
                                                {
                                                    userBillingAddresses?.map((item: any, index: number) => {
                                                        return (
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    setSelectedBillingAddress(item.id)
                                                                }}
                                                                key={index}
                                                                style={[styles.addressItemView, shadow, { borderColor: item.id == selectedBillingAddress ? gray_t1 : white }]}
                                                            >
                                                                <Text style={styles.addressItemTitle}>{item?.title}</Text>
                                                                <Text style={styles.addressItemDesc}>{item?.fullAddress}</Text>
                                                            </TouchableOpacity>
                                                        )
                                                    })
                                                }
                                            </View>
                                            {
                                                addAddressTwo ?
                                                    <View style={styles.addressAltView}>
                                                        <Text style={styles.addressAltTitle}>farklı adresteyim</Text>

                                                        {/* ADDRESS NAME */}
                                                        <Input
                                                            mt={2}
                                                            placeholderTextColor={black}
                                                            borderColor={gray_t3}
                                                            borderRadius={8}
                                                            shadow={0}
                                                            placeholder="Adres Adı"
                                                            value={addressTitle}
                                                            onChange={(event) => setAddressTitle(event.nativeEvent.text)}
                                                        />
                                                        {/* IDENTITY NUMBER */}
                                                        <Input
                                                            mt={2}
                                                            placeholderTextColor={black}
                                                            borderColor={gray_t3}
                                                            borderRadius={8}
                                                            shadow={0}
                                                            placeholder="Kimlik Numarası"
                                                            value={identityNumber}
                                                            onChange={(event) => setIdentityNumber(event.nativeEvent.text)}
                                                        />
                                                        {/* PHONE NUMBER */}
                                                        <Input
                                                            mt={2}
                                                            placeholderTextColor={black}
                                                            borderColor={gray_t3}
                                                            borderRadius={8}
                                                            shadow={0}
                                                            placeholder="Telefon Numarası"
                                                            value={phoneNumber}
                                                            onChange={(event) => setPhoneNumber(event.nativeEvent.text)}
                                                        />
                                                        {/* EMAIL */}
                                                        <Input
                                                            mt={2}
                                                            placeholderTextColor={black}
                                                            borderColor={gray_t3}
                                                            borderRadius={8}
                                                            shadow={0}
                                                            placeholder="E-mail"
                                                            value={email}
                                                            onChange={(event) => setEmail(event.nativeEvent.text)}
                                                        />
                                                        <View style={styles.selectView}>
                                                            {/* ADDRESS CITY */}
                                                            <Select
                                                                selectedValue={city}
                                                                minWidth={Layout.screen.width / 2.5}
                                                                placeholder="Şehir"
                                                                placeholderTextColor={black}
                                                                borderRadius={8}
                                                                shadow={0}
                                                                _selectedItem={{
                                                                    bg: "teal.600",
                                                                    endIcon: <CheckIcon size="5" />
                                                                }} mt={1}
                                                                onValueChange={(itemValue: any) => {
                                                                    getDistricts(itemValue);
                                                                    setCity(itemValue);
                                                                }}
                                                            >
                                                                {
                                                                    cities.map((item: any, index: React.Key) => {
                                                                        return (
                                                                            <Select.Item
                                                                                key={index}
                                                                                label={item?.title}
                                                                                value={item?.key}
                                                                            />
                                                                        )
                                                                    })
                                                                }
                                                            </Select>
                                                            {/* ADDRESS DISTRICT */}
                                                            <Select
                                                                shadow={0}
                                                                selectedValue={district}
                                                                minWidth={Layout.screen.width / 2.5}
                                                                placeholder="İlçe"
                                                                placeholderTextColor={black}
                                                                borderRadius={8}
                                                                _selectedItem={{
                                                                    bg: "teal.600",
                                                                    endIcon: <CheckIcon size="5" />
                                                                }} mt={1}
                                                                onValueChange={(itemValue: any) => {
                                                                    getNeighborhoods(itemValue);
                                                                    setDistrict(itemValue);
                                                                }}
                                                            >
                                                                {
                                                                    districts.map((item: any, index: React.Key) => {
                                                                        return (
                                                                            <Select.Item
                                                                                key={index}
                                                                                label={item?.title}
                                                                                value={item?.key}
                                                                            />
                                                                        )
                                                                    })
                                                                }
                                                            </Select>
                                                        </View>

                                                        <View style={styles.selectView}>
                                                            {/* ADDRESS NEIGHBOURHOOD */}
                                                            <Select
                                                                selectedValue={neighborhood}
                                                                minWidth={Layout.screen.width / 2.5}
                                                                placeholder="Mahalle / Köy"
                                                                borderRadius={8}
                                                                shadow={0}
                                                                placeholderTextColor={black}
                                                                _selectedItem={{
                                                                    bg: "teal.600",
                                                                    endIcon: <CheckIcon size="5" />
                                                                }} mt={1}
                                                                onValueChange={itemValue => setNeighborhood(itemValue)}
                                                            >
                                                                {
                                                                    neighborhoods.map((item: any, index) => {
                                                                        return (
                                                                            <Select.Item
                                                                                label={item?.title}
                                                                                value={item?.key}
                                                                            />
                                                                        )
                                                                    })
                                                                }
                                                            </Select>
                                                            {/* ADDRESS POSTCODE */}
                                                            <Input
                                                                minW={Layout.screen.width / 2.5}
                                                                borderColor={gray_t3}
                                                                height={33}
                                                                mt={1}
                                                                placeholderTextColor={black}
                                                                borderRadius={8}
                                                                shadow={0}
                                                                value={postalCode}
                                                                onChange={(event) => setPostalCode(event.nativeEvent.text)}
                                                                placeholder="Posta Kodu"
                                                            />
                                                        </View>

                                                        {/* @ts-ignore ADDRESS */}
                                                        <TextArea
                                                            borderWidth={1}
                                                            mt={2}
                                                            placeholder="Açık Adres"
                                                            borderRadius={8}
                                                            shadow={0}
                                                            placeholderTextColor={black}
                                                            minHeight={75}
                                                            fontSize={14}
                                                            color={black}
                                                            value={fullAddress}
                                                            onChange={(event) => setFullAddress(event.nativeEvent.text)}
                                                        />
                                                    </View>
                                                    :
                                                    <></>
                                            }
                                            <Button
                                                onPress={() => {
                                                    if (!addAddressTwo) {
                                                        setAddAddressTwo(!addAddressTwo);
                                                        return;
                                                    } else {
                                                        if (
                                                            addressTitle === "" ||
                                                            city === "" ||
                                                            district === "" ||
                                                            neighborhood === "" ||
                                                            postalCode === "" ||
                                                            fullAddress === "" ||
                                                            phoneNumber.length !== 13 ||
                                                            identityNumber.length !== 11
                                                        ) {
                                                            startToast("Tüm alanları doldurun ve doğru formatta bilgi girin.", "error");
                                                            return;
                                                        }

                                                        setAddAddress(!addAddressTwo);
                                                        addUserBillingAddress();
                                                    }
                                                }}
                                                minW={138}
                                                style={styles.differentAddressButton}
                                            >
                                                <Text style={styles.differentAddress}>
                                                    {
                                                        addAddressTwo ?
                                                            "kaydet"
                                                            :
                                                            "farklı adresteyim"
                                                    }
                                                </Text>
                                            </Button>
                                        </View>
                                        :
                                        <></>
                                }

                                {/* INSTITUTIONAL AND INDIVIDUAL */}
                                <View style={styles.addressView}>
                                    <View style={styles.taxView}>
                                        {/* INDIVIDUAL */}
                                        <TouchableOpacity
                                            onPress={() => {
                                                setTax(0)
                                            }}
                                            style={[
                                                {
                                                    backgroundColor: tax == 0 ? brown_t2 : white,
                                                    borderWidth: tax == 0 ? 0 : 1,

                                                },
                                                styles.taxTouch
                                            ]}
                                        >
                                            <Text style={[
                                                {
                                                    color: tax == 0 ? white : black_t3,
                                                },
                                                styles.taxTouchText
                                            ]}>Bireysel</Text>
                                        </TouchableOpacity>

                                        {/* INSTITUTIONAL */}
                                        <TouchableOpacity
                                            onPress={() => {
                                                setTax(1)
                                            }}
                                            style={[
                                                {
                                                    backgroundColor: tax == 1 ? brown_t2 : white,
                                                    borderWidth: tax == 1 ? 0 : 1,

                                                },
                                                styles.taxTouch
                                            ]}
                                        >
                                            <Text style={[
                                                {
                                                    color: tax == 1 ? white : black_t3,
                                                },
                                                styles.taxTouchText
                                            ]}>Kurumsal</Text>
                                        </TouchableOpacity>
                                    </View>

                                    {/* INSTITUTIONAL ALT VIEW */}
                                    {
                                        tax == 1 ?
                                            <View>
                                                <View style={styles.taxAltView}>
                                                    {/* VKN */}
                                                    <Input
                                                        mt={2}
                                                        onChangeText={(text) => {
                                                            setVkn(text);
                                                        }}
                                                        w={'48%'}
                                                        backgroundColor={white}
                                                        placeholderTextColor={black}
                                                        borderColor={gray_t3}
                                                        borderRadius={8}
                                                        shadow={0}
                                                        placeholder="VKN*"
                                                    />

                                                    {/* VKN */}
                                                    <Input
                                                        onChangeText={(text) => {
                                                            setVDepartment(text);
                                                        }}
                                                        mt={2}
                                                        w={'48%'}
                                                        backgroundColor={white}
                                                        placeholderTextColor={black}
                                                        borderColor={gray_t3}
                                                        borderRadius={8}
                                                        shadow={0}
                                                        placeholder="Vergi Dairesi*"
                                                    />
                                                </View>
                                                <Input
                                                    onChangeText={(text) => {
                                                        setCompanyName(text);
                                                    }}
                                                    mt={2}
                                                    backgroundColor={white}
                                                    placeholderTextColor={black}
                                                    borderColor={gray_t3}
                                                    borderRadius={8}
                                                    shadow={0}
                                                    placeholder="Firma Adı*"
                                                />
                                                <View style={styles.taxButtonView}>
                                                    <TouchableOpacity
                                                        onPress={() => {
                                                            setElectronic(!electronic);
                                                        }}
                                                        style={styles.taxButton}>
                                                        {
                                                            electronic ?
                                                                <View style={styles.taxAltButton} />
                                                                :
                                                                <></>
                                                        }
                                                    </TouchableOpacity>
                                                    <Text style={styles.taxOrder}>e-fatura mükellefiyim.</Text>
                                                </View>
                                            </View>
                                            :
                                            <></>
                                    }
                                </View>
                                {/* CARD */}
                                <View style={styles.cardView}>
                                    <Text style={styles.cardTitle}>Kart Bilgisi Gir</Text>
                                    <Text style={styles.cardAltTitle}>kayıtlı kartlarım</Text>
                                    {
                                        allCards.map((item: any, index: React.Key) => (
                                            <TouchableOpacity
                                                key={index}
                                                onPress={() => setSelectedCard(item)}
                                                style={[
                                                    styles.concertInfoCardView,
                                                    { borderColor: green_t2, borderWidth: selectedCard?.id == item.id ? 1 : 0 },
                                                ]}
                                            >
                                                <View>
                                                    <Text style={styles.concertInfoCardName}>{item.c_name}</Text>
                                                    <Text style={styles.concertInfoCardNo}>**** **** **** {item.last_4}</Text>
                                                </View>
                                                <Image
                                                    source={
                                                        item?.schema === "MASTERCARD"
                                                            ? require('../../../assets/exp/masterCard.png')
                                                            : item?.schema === "VISA"
                                                            ? require('../../../assets/exp/visa.png')
                                                            : require('../../../assets/exp/cardIcon.png')
                                                    }
                                                    style={styles.concertInfoCardLogo}
                                                    resizeMode="contain"
                                                />
                                            </TouchableOpacity>
                                        ))
                                    }
                                    
                                    {/* ✅ KART EKLEME MODALI */}
                    <AddCardModal
                        visible={isModalVisible}
                        onClose={() => setIsModalVisible(false)}
                        onAddCard={addNewCard}
                    />

                    {/* KART EKLEME BUTONU */}
                    <TouchableOpacity style={styles.useDifferentCardButton} onPress={() => setIsModalVisible(true)}>
                        <Text style={styles.useDifferentCardButtonText}>Kart Ekle</Text>
                    </TouchableOpacity>
                                    
                                </View>



                                {/* CONDITIONS */}
                                <View style={styles.conditionsView}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setCondition(!condition)
                                        }}
                                        style={styles.conditionTouch}>
                                        {
                                            condition ?
                                                <View style={styles.conditionAltTouch} />
                                                :
                                                <></>
                                        }
                                    </TouchableOpacity>
                                    <Text style={styles.conditionText}>Ön Bilgilendirme Koşulları’nı ve Mesafeli Satış Sözleşmesi’ni okudum onaylıyorum.</Text>
                                </View>


                                {/* BROWN AREA */}
                                <View style={styles.brownArea}>
                                    <Image
                                        style={styles.brownTop}
                                        source={require('../../../assets/root/basketBack.png')}
                                    />
                                    <View style={styles.brownView}>

                                        {/* PRODUCT TOTAL */}
                                        <View style={styles.brownAltView}>
                                            <View style={styles.brownListView}>
                                                <Text style={styles.brownListTitle}>Ürün Toplam Tutar</Text>
                                                <Text style={styles.brownListPrice}>{props?.route?.params?.totalPrice}₺</Text>
                                            </View>
                                        </View>

                                        {/* USED HOLLY PUAN */}
                                        <View style={[styles.brownAltView, { marginTop: 17 }]}>
                                            <View style={styles.brownListView}>
                                                <Text style={styles.brownListTitle}>Kullanılan Holly Puan</Text>
                                                <View style={styles.brownSpecialView}>
                                                    <Text style={styles.brownListPrice}>{props?.route?.params?.hollyPoint}</Text>
                                                    <Image
                                                        source={require('../../../assets/root/hPWhite.png')}
                                                        resizeMode="contain"
                                                        style={styles.hPWhite}
                                                    />
                                                </View>
                                            </View>
                                        </View>

                                        {/* DISCOUNT TOTAL */}
                                        <View style={[styles.brownAltView, { marginTop: 17 }]}>
                                            <View style={styles.brownListView}>
                                                <Text style={[styles.brownListTitle, { color: black_t3 }]}>İndirim</Text>
                                                <Text style={[styles.brownListPrice, { color: black_t3 }]}>-{props?.route?.params?.discount}₺₺</Text>
                                            </View>
                                        </View>

                                        <View style={{
                                            width: Layout.screen.width,
                                            alignSelf: 'center',
                                            height: 0.5,
                                            backgroundColor: black,
                                            opacity: 0.35,
                                            marginTop: 14
                                        }} />

                                        {/* TOTAL */}
                                        <View style={[styles.brownAltView, { marginTop: 12 }]}>
                                            <View style={styles.brownListView}>
                                                <Text style={[styles.brownListTitle, { fontSize: 14 }]}>Ödenecek Tutar</Text>
                                                <Text style={[styles.brownListPrice, { fontSize: 20 }]}>{props?.route?.params?.totalPrice - props?.route?.params?.discount}₺</Text>
                                            </View>
                                        </View>
                                    </View>

                                    {/* NEXT BUTTON */}
                                    <TouchableOpacity
    onPress={() => {
        if (condition) {
            // 📌 Seçili kartın verilerini kontrol et ve eksiksiz aktar
            const cardInfo = selectedCard?.isLocal
                ? { 
                    // 📌 Yeni Eklenen Kart Bilgileri
                    ctoken: null, // Kayıtlı kart değilse token olmaz
                    cName: selectedCard?.c_name, 
                    cLastFour: selectedCard?.last_4, 
                    cSchema: selectedCard?.schema,

                    // 🔥 Yeni Kart Bilgileri (Eksiksiz)
                    card_number: selectedCard?.card_number, 
                    expiry_month: selectedCard?.expiryMonth, 
                    expiry_year: selectedCard?.expiryYear, 
                    cvv: selectedCard?.cvc,
                }
                : {
                    // 📌 Kayıtlı Kart Kullanılıyorsa
                    ctoken: selectedCard?.ctoken, 
                    cName: selectedCard?.c_name,   
                    cLastFour: selectedCard?.last_4,
                    cSchema: selectedCard?.schema,
                };

            // 📌 `BasketOkey` sayfasına doğru bilgileri gönder
            navigation.navigate("BasketOkey", {
                addressId: selectedAddress,
                billingAddressId: same ? selectedAddress : selectedBillingAddress,
                billingType: tax == 1 ? true : false, // true - institutional | false - individual
                taxNumber: vkn,
                taxOffice: vDepartment,
                companyName: companyName,
                efatura: electronic,
                cardInfo,  // 📌 Kart Bilgilerini Eksiksiz Gönder
                totalPrice: props?.route?.params?.totalPrice,
                hollyPoint: props?.route?.params?.hollyPoint,
                discount: props?.route?.params?.discount
            });
        } else {
            startToast("Lütfen Ön Bilgilendirme Koşulları'nı ve Mesafeli Satış Sözleşmesi'ni onaylayınız.");
        }
    }}
    style={styles.nextButton}
>
    <Text style={styles.next}>ONAYLA</Text>
</TouchableOpacity>



                                </View>
                            </>
                    }
                </ScrollView>
            </SafeAreaView>
        </View>
    )
}
export default AddressCard;

// -- STYLES -- //
const styles = StyleSheet.create({
    containerPayTr: { flex: 1 },
    main: {
        flex: 1,
        backgroundColor: white
    },
    concertInfoFirstTitle: {
        fontWeight: 'bold',
        color: white,
        fontSize: 18
    },
    concertInfoCardView: {
        borderWidth: 1,
        borderColor: yellow_t1,
        borderRadius: 8,
        height: 44,
        marginTop: 8,
        backgroundColor: white,
        justifyContent: 'space-between',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        width: 200,
    },
    concertInfoCardName: { fontSize: 10 },
    concertInfoCardNo: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 2
    },
    concertInfoCardLogo: {
        width: 28,
        height: 17
    },
    noCardText:{
        color: white,
        textAlign: 'center',
    },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    safeAreaView: { flexGrow: 1 },
    addressView: {
        borderWidth: 1,
        borderColor: gray_t1,
        borderRadius: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        padding: 10,
        paddingHorizontal: 15,
        marginTop: 40
    },
    concertInfoCardLogo: {
        width: 28,
        height: 17
    },
    addressTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        alignSelf: 'center'
    },
    addressAltView: { marginVertical: 10 },
    addressAltTitle: { color: black_t3 },
    addressItemView: {
        borderRadius: 8,
        borderWidth: 1,
        borderColor: black,
        marginTop: 5,
        padding: 10,
        backgroundColor: white
    },
    addressItemTitle: {
        color: black_t3,
        fontSize: 12,
        fontWeight: 'bold'
    },
    addressItemDesc: {
        fontSize: 10,
        marginTop: 4,
        letterSpacing: 1.2,
        color: gray_t4
    },
    differentAddressButton: {
        alignSelf: 'center',
        marginVertical: 10,
        borderRadius: 15,
        backgroundColor: brown_t2
    },
    differentAddress: {
        fontSize: 12,
        color: white,
        fontWeight: 'bold'
    },
    sameWhereView: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 20,
    },
    sameWhereButton: {
        width: 20,
        height: 20,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: yellow_t1
    },
    sameWhereButtonAlt: {
        width: 7.5,
        height: 7.5,
        borderRadius: 8,
        backgroundColor: white
    },
    sameWhereText: {
        fontSize: 14,
        color: black,
        marginLeft: 10
    },
    taxView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: Layout.screen.width / 1.2,
    },
    taxTouch: {
        width: '48%',
        height: 40,
        borderRadius: 10,
        borderColor: gray_t4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    taxTouchText: {
        fontSize: 12,
        fontWeight: 'bold'
    },
    taxAltView: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    taxButtonView: {
        alignSelf: 'center',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
    },
    taxButton: {
        width: 20,
        height: 20,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: yellow_t1
    },
    taxAltButton: {
        width: 7.5,
        height: 7.5,
        borderRadius: 8,
        backgroundColor: white
    },
    taxOrder: {
        fontSize: 14,
        color: black,
        marginLeft: 10
    },
    selectView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 5,
    },
    cardView: {
        borderRadius: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        padding: 10,
        paddingHorizontal: 15,
        marginBottom: 50,
        marginTop: 30,
        backgroundColor: blue_t2
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        alignSelf: 'center',
        color: white
    },
    cardAltTitle: {
        fontSize: 14,
        color: white,
        marginTop: 10,
        fontWeight: '600'
    },
    touchCredit: {
        borderWidth: 1,
        borderColor: brown_t2,
        height: 50,
        justifyContent: 'center',
        borderRadius: 10,
        marginTop: 5,
        paddingHorizontal: 15,
        width: '100%',
        alignSelf: 'center',
        backgroundColor: white
    },
    touchCardName: {
        fontSize: 10,
        color: black_t3
    },
    touchCardNo: {
        fontSize: 10,
        fontWeight: 'bold',
        marginTop: 10,
        color: black_t3
    },
    masterCardIcon: {
        width: 28,
        height: 17,
        position: 'absolute',
        right: 15,
        bottom: 10
    },
    addCardView: { marginTop: 10 },
    useDifferentCardText: {
        color: white,
        fontSize: 12
    },
    addCardAltView: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    useDifferentCardButton: {
        alignSelf: 'center',
        marginTop: 15,
        marginBottom: 5,
        borderRadius: 15,
        height: 30,
        width: 130,
        backgroundColor: white,
        justifyContent: 'center',
        textAlign: 'center'
    },
    useDifferentCardButtonText: {
        fontSize: 14,
        color: blue_t2,
        fontWeight: 'bold',
        textAlign: 'center',
        textAlignVertical: 'center'
    },
    conditionsView: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        flexDirection: 'row',
        marginBottom: 20,
    },
    conditionTouch: {
        width: 30,
        height: 30,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: yellow_t1
    },
    conditionAltTouch: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: white
    },
    conditionText: {
        fontSize: 14,
        color: black,
        marginLeft: 10
    },
    brownArea: {
    },
    brownTop: {
        height: 39,
        width: Layout.screen.width,
    },
    brownView: {
        backgroundColor: yellow_t1,
        height: 240
    },
    brownAltView: {
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        marginTop: 7
    },
    brownListView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    brownListTitle: {
        color: white,
        fontSize: 12,
        fontWeight: 'bold'
    },
    brownSpecialView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    hPWhite: {
        width: 15,
        height: 17,
        marginLeft: 3
    },
    brownListPrice: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold'
    },
    nextButton: {
        backgroundColor: brown_t2,
        height: 195,
        borderTopLeftRadius: 60,
        borderTopRightRadius: 60,
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        position: 'absolute',
        bottom: -140,
        zIndex: 0,
        alignItems: 'center'
    },
    next: {
        marginTop: 20,
        color: white,
        fontWeight: 'bold',
        fontSize: 16
    },
});
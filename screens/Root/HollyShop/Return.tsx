import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Layout from "../../../constants/Layout";
import { black, black_t3, brown_t2, gray_t4, white, yellow_t1 } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { CheckIcon, Select, TextArea } from "native-base";

const Return: React.FC = () => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- MODAL -- //
    const [modal, setModal] = React.useState(false);

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >
            <SafeAreaView>

                {/* RETURN CODE */}
                {
                    modal ?
                        <View style={styles.modal}>
                            <TouchableOpacity
                                onPress={() => {
                                    setModal(false)
                                }}
                                style={styles.modalCloseTopTouch}
                            />
                            <View style={styles.modalAltView}>

                                {/* HOLLY SHOP LOGO */}
                                <Image
                                    source={require('../../../assets/root/hollyshop.png')}
                                    style={styles.hollyShopLogo}
                                    resizeMode="contain"
                                />

                                {/* CODE */}
                                <Text style={styles.codeTitle}>İade Kodu</Text>
                                <Text style={styles.code}>HLT2312ANT</Text>

                                {/* GO ORDER PAGE */}
                                <TouchableOpacity
                                    onPress={() => {
                                        // WILL GO ORDER PAGE TWO (POP)ES
                                        navigation.pop();
                                        navigation.pop();
                                    }}
                                    style={styles.modalButton}
                                >
                                    <Text style={styles.modalButtonText}>siparişlerime Git</Text>
                                </TouchableOpacity>
                            </View>
                            <TouchableOpacity
                                onPress={() => {
                                    setModal(false)
                                }}
                                style={styles.modalCloseBottomTouch}
                            />
                        </View>
                        :
                        <></>
                }
                <ScrollView showsVerticalScrollIndicator={false}>
                    <HeaderThree
                        basket={false}
                        search={false}
                        navigation={navigation}
                    />

                    {/* INFO */}
                    <View style={[styles.formView, shadow]}>
                        <Text style={styles.warnText}>İade talebi oluşturduğun ürünü kargo kodu ile kargoya teslim etmelisin.
                            Üründen kazandığın Holly Puan profilinden silinecektir.</Text>
                    </View>

                    <View style={[styles.formView, shadow, { marginTop: 20 }]}>
                        {/* PRODUCT NAME */}
                        <Text style={styles.productName}>Holly Tişört</Text>

                        {/* PRODUCT ID */}
                        <Text style={styles.productId}>(HL23T232)</Text>

                        {/* PRODUCT IMAGE */}
                        <Image
                            source={require('../../../assets/exp/blackTshirt.png')}
                            style={styles.productImage}
                            resizeMode="contain"
                        />

                        <Select
                            w={Layout.screen.width / 1.2}
                            height={50}
                            borderWidth={1}
                            mb={5}
                            letterSpacing={1.3}
                            fontSize={15}
                            fontWeight={'bold'}
                            color={black}
                            marginTop={5}
                            borderRadius={10}
                            borderColor={black}
                            placeholderTextColor={black_t3}
                            _selectedItem={{
                                bg: "teal.600",
                                endIcon: <CheckIcon size={5} />
                            }}
                            placeholder="İade Nedenini Seçiniz"
                        >
                            <Select.Item
                                label="Backend Development" value="backend"
                            />
                        </Select>

                        {/* DESC TITLE */}
                        <Text style={styles.descTitle}>Açıklama yaz</Text>

                        {/* TEXTAREA FOR REASON RETURN */}
                        <View style={[styles.commentView, shadow]}>
                            <TextArea
                                autoCompleteType={""}
                                placeholderTextColor={black_t3}
                                color={black}
                                backgroundColor={white}
                                borderWidth={0}
                                borderRadius={10}
                                fontSize={10}
                                placeholder="İade nedenini birkaç cümle ile bize iletebilirsin"
                            />
                        </View>
                    </View>

                    {/* RETURN BUTTON */}
                    <TouchableOpacity
                        onPress={() => {
                            setModal(true);
                        }}
                        style={styles.button}
                    >
                        <Text style={styles.buttonText}>İade kodu al</Text>
                    </TouchableOpacity>
                </ScrollView>
            </SafeAreaView>
        </ImageBackground>
    )
}
export default Return;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1 },
    modal: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalAltView: {
        width: Layout.screen.width / 1.1,
        paddingVertical: 30,
        backgroundColor: white,
        borderRadius: 10,
        alignItems: 'center'
    },
    hollyShopLogo: {
        width: 141,
        height: 21
    },
    codeTitle: {
        fontSize: 20,
        color: black_t3,
        fontWeight: 'bold',
        letterSpacing: 2,
        marginTop: 20
    },
    code: {
        fontSize: 20,
        color: brown_t2,
        fontWeight: 'bold',
        letterSpacing: 3,
        marginTop: 20
    },
    modalButton: {
        borderRadius: 5,
        width: 153,
        height: 36,
        backgroundColor: yellow_t1,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 20
    },
    modalButtonText: {
        fontSize: 14,
        color: white,
        fontWeight: 'bold'
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    formView: {
        borderRadius: 10,
        padding: 10,
        width: Layout.screen.width / 1.1,
        backgroundColor: white,
        alignSelf: 'center',
        paddingHorizontal: 15,
        alignItems: 'center'
    },
    warnText: {
        fontSize: 15,
        color: black_t3,
        fontWeight: 'bold',
        margin: 5
    },
    productName: {
        alignSelf: 'center',
        fontSize: 22,
        fontWeight: 'bold',
        color: black_t3
    },
    productId: {
        alignSelf: 'center',
        marginTop: 2,
        fontSize: 14,
        color: black_t3
    },
    productImage: {
        width: 150,
        height: 150,
        marginTop: 10,
        alignSelf: 'center'
    },
    descTitle: {
        fontSize: 15,
        letterSpacing: 1.3,
        fontWeight: 'bold',
        color: black_t3
    },
    commentView: {
        width: Layout.screen.width / 1.2,
        marginTop: 20
    },
    button: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        backgroundColor: gray_t4,
        borderRadius: 10,
        height: 40,
        marginBottom: 40,
        marginTop: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        color: white,
        fontWeight: 'bold',
        fontSize: 18
    },
});
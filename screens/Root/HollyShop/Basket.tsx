import React, { useEffect, useState } from "react";
import { ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import BasketItem from "../../../components/BasketItem";
import { brown_t2, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import BasketPayInfo from "../../../components/BasketPayInfo";
import { MainStore } from "../../../stores/MainStore";
import { get, getImageURL } from "../../../networking/Server";
import { Observer } from "mobx-react-lite";
import Toast from "../../../components/Toast";

const Basket: React.FC = () => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- HOLLY INFO -- //
    //const [hollyInfoModal, setHollyInfoModal] = React.useState(false);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }


    // -- TOTAL HOLLY POINTS -- //
    const [hollyPoints, setHollyPoints] = React.useState(0);
    const [winHollyPoints, setWinHollyPoints] = React.useState(0);
    const [totalPrice, setTotalPrice] = React.useState(0);
    const [hollyPointsValue, setHollyPointsValue] = React.useState(0);
    const [discount, setDiscount] = React.useState(0);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const [isItemDeleted, setIsItemDeleted] = useState(false);


    const resetHollyPointsToUse = () => {
        setIsItemDeleted(true); // Bir öğe silindiğinde durumu true olarak ayarla
    };

    useEffect(() => {
        if (isItemDeleted) {
            setIsItemDeleted(false); // BasketPayInfo bileşenine bildirim gönderildikten sonra durumu sıfırla
        }
    }, [isItemDeleted]);

    const getSettings = () => {
        try {
            get("get-settings").then((res: any) => {
                if (res.type == "success") {
                    setHollyPointsValue(res.hollyPointsValue)
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }

            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }


    const getHollyPoints = () => {
        get("/users/holly-points").then((res: any) => {
            if (res.type == "success")
                setHollyPoints(res.hollyPoints);
            else {
                startToast("Bir şeyler ters gitti", "error");
            }
        })
    }

    const countAgain = (counting: number, price: number) => {
        setTimeout(() => {
            MainStore.basket.forEach((basketItem: any) => {
                counting += basketItem.hollyPoints * basketItem.quantity;
                price += parseFloat(basketItem.discountedPrice) > 0 ? (basketItem.discountedPrice * basketItem.quantity) : (basketItem.price * basketItem.quantity);
            });
            setWinHollyPoints(counting);
            setTotalPrice(parseFloat((price).toFixed(2)));
        }, 500)
    }

    React.useEffect(() => {
        getSettings();
        countAgain(0, 0);
        getHollyPoints();
    }, []);

    const insets = useSafeAreaInsets();

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={brown_t2}
                />
            </View>

            <SafeAreaView style={{
                zIndex: 10,
                position: 'absolute',
                width: Layout.screen.width
            }}>
                <HeaderThree
                    headerStatus={header}
                    basket={false}
                    search={false}
                    navigation={navigation}
                    onSearch={false}
                />
            </SafeAreaView>

            <SafeAreaView>

                <ScrollView
                    onScroll={handleScroll}
                    showsVerticalScrollIndicator={false}
                >
                    <Observer
                        render={() => (
                            <>
                                {MainStore.basket?.map((item: any, index: React.Key) => {
                                    return item.model === "shop" ? (
                                        <View
                                            style={{
                                                marginTop: index == 0 ? Math.max(insets.bottom, 0) ? 40 : 60 : 0,
                                                marginBottom: MainStore.basket.length - 1 === index ? 400 : 0
                                            }}
                                            key={index}
                                        >
                                            <BasketItem
                                                item={item}
                                                id={item.id}
                                                whenQuantity={() => {
                                                    countAgain(0, 0);
                                                }}
                                                countAgain={countAgain}
                                                onItemDeleted={resetHollyPointsToUse}
                                                quantity={item.quantity}
                                                name={item.name}
                                                image={{ uri: getImageURL(item.images[0]) }}
                                                price={item.price}
                                            />
                                        </View>
                                    ) : (
                                        <View style={{
                                            marginTop: index == 0 ? Math.max(insets.bottom, 0) ? 40 : 60 : 0,
                                            marginBottom: MainStore.basket.length - 1 === index ? 400 : 0
                                        }}
                                            key={index}
                                        >
                                            <BasketItem
                                                item={item}
                                                id={item.id}
                                                whenQuantity={() => {
                                                    countAgain(0, 0);
                                                }}
                                                countAgain={countAgain}
                                                onItemDeleted={resetHollyPointsToUse}
                                                quantity={item.quantity}
                                                name={item.title}
                                                image={{ uri: getImageURL(item.image) }}
                                                price={item.price}
                                            />
                                        </View>
                                    );
                                })}
                            </>
                        )}
                    />
                </ScrollView>

            </SafeAreaView>

            {/* BASKET PAY INFORMATIONS */}
                <Observer render={() =>
                    <BasketPayInfo
                        hollyPointsToUse={0}
                        setHPTU={(set: number) => {
                            setDiscount(set)
                        }}
                        hollyPoints={hollyPoints}
                        winHollyPoints={winHollyPoints}
                        totalPrice={totalPrice}
                        hollyPointsValue={hollyPointsValue}
                        card={false}
                        isItemDeleted={isItemDeleted}
                       
                    />}
                />
            

            {/* NEXT BUTTON */}
            <TouchableOpacity
                onPress={() => {
                    if (MainStore.basket.length < 1) {
                        startToast("Lütfen sepete ürün ekleyiniz.", "error");
                        return
                    }
                    //@ts-ignore
                    navigation.navigate("AddressCard", { totalPrice, hollyPoint: discount / hollyPointsValue, discount });
                }}
                style={styles.nextButton}
            >
                <Text style={styles.next}>İLERLE</Text>
            </TouchableOpacity>

        </ImageBackground >
    )
}
export default Basket;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1 },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    nextButton: {
        backgroundColor: brown_t2,
        height: 195,
        borderTopLeftRadius: 60,
        borderTopRightRadius: 60,
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        position: 'absolute',
        bottom: -140,
        zIndex: 0,
        alignItems: 'center'
    },
    next: {
        marginTop: 20,
        color: white,
        fontWeight: 'bold',
        fontSize: 16
    },
});
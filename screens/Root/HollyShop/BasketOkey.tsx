import React from "react";
import { ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import BasketItem from "../../../components/BasketItem";
import { brown_t2, white } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import BasketPayInfo from "../../../components/BasketPayInfo";
import { Observer } from "mobx-react-lite";
import { MainStore } from "../../../stores/MainStore";
import WebView from "react-native-webview";
import { get, getImageURL, post } from "../../../networking/Server";
import Toast from "../../../components/Toast";

const BasketOkey: React.FC = (props: any) => {
    const { params } = props.route;

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- TOTAL HOLLY POINTS -- //
    const [hollyPoints, setHollyPoints] = React.useState(0);
    const [winHollyPoints, setWinHollyPoints] = React.useState(0);
    const [totalPrice, setTotalPrice] = React.useState(props?.route?.params?.totalPrice);
    const [hollyPoint] = React.useState(props?.route?.params?.hollyPoint);
    const [hollyPointsValue, setHollyPointsValue] = React.useState(0);
    const [discount, setDiscount] = React.useState(props?.route?.params?.discount);
    const [paytrLink, setPaytrLink] = React.useState("");
    const webview: any = React.useRef(null);

    const insets = useSafeAreaInsets();
    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };


    const countAgain = (counting: number, price: number) => {
        setTimeout(() => {
            MainStore.basket.forEach((basketItem: any) => {
                counting += basketItem.hollyPoints * basketItem.quantity;
                price += parseFloat(basketItem.discountedPrice) > 0 ? (basketItem.discountedPrice * basketItem.quantity) : (basketItem.price * basketItem.quantity);
            });
            setWinHollyPoints(counting);
            setTotalPrice(parseFloat((price).toFixed(2)));
        }, 500)
    }

    const getSettings = () => {
        try {
            get("get-settings").then((res: any) => {
                if (res.type == "success") {
                    setHollyPointsValue(res.hollyPointsValue)
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                }

            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
        }
    }

    const getHollyPoints = () => {
        get("/users/holly-points").then((res: any) => {
            if (res.type == "success")
                setHollyPoints(res.hollyPoints);
            else {
                startToast("Bir şeyler ters gitti", "error");
            }
        })
    }

    const completeOrder = () => {
        if (params.cardInfo?.ctoken) {
            // ✅ Kayıtlı Kart Seçildiyse
            post('/shop/order', {
                products: MainStore.basket,
                addressId: params.addressId,
                billingAddressId: params.billingAddressId,
                billingType: params.billingType,
                taxNumber: params.taxNumber,
                taxOffice: params.taxOffice,
                companyName: params.companyName,
                efatura: params.efatura,
    
                // ✅ Kayıtlı Kart Bilgileri
                ctoken: params.cardInfo?.ctoken, 
                cName: params.cardInfo?.cName,   
                cLastFour: params.cardInfo?.cLastFour,
                cSchema: params.cardInfo?.cSchema,
            }).then((res: any) => {
                if (res.type === "success") {
                    setPaytrLink(res.paymentLink);
                    startToast("Ödemeye aktarılıyorsunuz.", "success");
                } else {
                    startToast("Ödeme başarısız. Lütfen tekrar deneyin.", "error");
                }
            });
        } else {
            // ✅ Yeni Eklenen Kart Kullanılıyorsa
            post('/shop/order-without-saved-card', {
                products: MainStore.basket,
                addressId: params.addressId,
                billingAddressId: params.billingAddressId,
                billingType: params.billingType,
                taxNumber: params.taxNumber,
                taxOffice: params.taxOffice,
                companyName: params.companyName,
                efatura: params.efatura,
    
                // ✅ Yeni Kartın Tüm Bilgileri
                cc_owner: params.cardInfo?.cName,     
                card_number: params.cardInfo?.card_number, 
                expiry_month: params.cardInfo?.expiry_month,
                expiry_year: params.cardInfo?.expiry_year,
                cvv: params.cardInfo?.cvv, 
            }).then((res: any) => {
                if (res.type === "success") {
                    setPaytrLink(res.paymentLink);
                    startToast("Ödemeye aktarılıyorsunuz.", "success");
                } else {
                    startToast("Ödeme başarısız adım 2. Lütfen tekrar deneyin.", "error");
                }
            });
        }
    };
    
    
    
    React.useEffect(() => {
        getSettings();
        countAgain(0, 0);
        getHollyPoints();
    }, []);

    // -- WEBVIEW PAYTR -- //
    const handleWebViewNavigationStateChange = (params: { url: any; }) => {
        const { url } = params;
        if (url?.includes('success')) {
            // webview.current.stopLoading();
            // webview.current = null;
            setPaytrLink("");
            startToast("Ödeme başarılı.", "success")
            setTimeout(() => {
                navigation.navigate("MyOrders");
                MainStore.emptyBasket();
            }, 2000);
        } else if (url?.includes('fail')) {
            startToast("İşlem reddedildi.", "error")
            setPaytrLink("");
        }
    }

    if (paytrLink) {
        return (
            <WebView
                ref={webview}
                style={styles.containerPayTr}
                source={{ uri: paytrLink }}
                onNavigationStateChange={handleWebViewNavigationStateChange}
            />
        )
    }

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={brown_t2}
                />
            </View>

            <SafeAreaView style={{
                zIndex: 10,
                position: 'absolute',
                width: Layout.screen.width
            }}>
                <HeaderThree
                    headerStatus={header}
                    basket={false}
                    search={false}
                    navigation={navigation}
                    onSearch={undefined}
                />
            </SafeAreaView>

            <SafeAreaView>
                <ScrollView
                    onScroll={handleScroll}
                    showsVerticalScrollIndicator={false}
                >
                    <Observer render={() => <>
                        {
                            MainStore.basket?.map((item: any, index: React.Key) => {
                                return item.model === "shop" ? (
                                    <View style={{
                                        marginTop: index == 0 ? Math.max(insets.bottom, 0) ? 40 : 60 : 0,
                                        marginBottom: MainStore.basket.length - 1 === index ? 400 : 0
                                    }}
                                        key={index}
                                    >
                                        <BasketItem
                                            item={item}
                                            id={item.id}
                                            whenQuantity={() => {
                                                countAgain(0, 0);
                                            }}
                                            quantity={item.quantity}
                                            name={item.name}
                                            image={{ uri: getImageURL(item.images[0]) }}
                                            price={item.price}
                                        />
                                    </View>
                                )
                                    :
                                    (
                                        <View
                                            style={{
                                                marginTop: index == 0 ? Math.max(insets.bottom, 0) ? 40 : 60 : 0,
                                                marginBottom: MainStore.basket.length - 1 === index ? 400 : 0
                                            }}
                                            key={index}
                                        >
                                            <BasketItem
                                                item={item}
                                                id={item.id}
                                                whenQuantity={() => {
                                                    countAgain(0, 0);
                                                }}
                                                quantity={item.quantity}
                                                name={item.title}
                                                image={{ uri: getImageURL(item.image) }}
                                                price={item.price}
                                            />
                                        </View>
                                    );
                            })
                        }
                    </>}
                    />
                </ScrollView>
            </SafeAreaView>

            {/* BASKET PAY INFORMATIONS */}
            <BasketPayInfo
                card={props?.route?.params?.cardInfo}
                hollyPoints={hollyPoints}
                winHollyPoints={winHollyPoints}
                totalPrice={totalPrice}
                hollyPointsValue={hollyPointsValue}
                setHPTU={(set: number) => {
                    setDiscount(set);
                }}
                hollyPointsToUse={hollyPoint}
            />

            {/* NEXT BUTTON */}
            <TouchableOpacity
                onPress={() => {
                    if (MainStore.basket.length < 1) {
                        startToast("Lütfen sepete ürün ekleyiniz.", "error");
                        return
                    }
                    completeOrder();
                }}
                style={styles.nextButton}
            >
                <Text style={styles.next}>ÖDEME YAP</Text>
            </TouchableOpacity>

        </ImageBackground >
    )
}
export default BasketOkey;

// -- STYLES -- //
const styles = StyleSheet.create({
    containerPayTr: { flex: 1 },
    main: { flex: 1 },
    toastView: {
        zIndex: 20,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    nextButton: {
        backgroundColor: brown_t2,
        height: 195,
        borderTopLeftRadius: 60,
        borderTopRightRadius: 60,
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        position: 'absolute',
        bottom: -140,
        zIndex: 0,
        alignItems: 'center'
    },
    next: {
        marginTop: 20,
        color: white,
        fontWeight: 'bold',
        fontSize: 16
    },
});
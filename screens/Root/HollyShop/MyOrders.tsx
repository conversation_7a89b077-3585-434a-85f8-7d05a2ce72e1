import { useNavigation } from "@react-navigation/native";
import React from "react";
import { ImageBackground, ScrollView, StyleSheet, Text } from "react-native";
import { screens } from "../../../navigation";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import HeaderThree from "../../../components/HeaderThree";
import MyOrdersItem from "../../../components/MyOrdersItem";
import { black_t3, yellow_t2 } from "../../../constants/Color";
import { get } from "../../../networking/Server";
import { Spinner } from "native-base";
import Layout from "../../../constants/Layout";

const MyOrders: React.FC = () => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(true);

    // -- ORDERS -- // 
    const [orders, setOrders] = React.useState([]);


    const insets = useSafeAreaInsets();
    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    const getOrders = () => {
        get('/shop/orders').then((res: any) => {
            if (res.type == "success") {
                setOrders(res.data);
                setLoading(false);
            } else {
                navigation.pop();
            }
        });
    }

    React.useEffect(() => {
        getOrders();
    }, []);

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >

            <SafeAreaView style={{
                zIndex: 10,
                position: 'absolute',
                width: Layout.screen.width
            }}>
                <HeaderThree
                    headerStatus={header}
                    basket={false}
                    search={false}
                    navigation={navigation}
                    onSearch={undefined}
                />
            </SafeAreaView>

            <SafeAreaView>
                <ScrollView
                    onScroll={handleScroll}
                    showsVerticalScrollIndicator={false}
                >

                    {
                        loading ? <Spinner mt={49} color={yellow_t2} /> : (
                            <>
                                <Text style={styles.title}>SİPARİŞLERİM</Text>
                                {
                                    orders.length < 1 ?
                                        <Text style={{
                                            textAlign: 'center',
                                            fontWeight: 'bold',
                                            color: yellow_t2,
                                            margin: 20,
                                        }}>Henüz siparişiniz bulunmuyor. Hemen siparişe başla!</Text>
                                        :
                                        orders.map((item: any, index: number) => {
                                            return (
                                                <MyOrdersItem
                                                    navigation={navigation}
                                                    key={index}
                                                    orderId={item?.id}
                                                    name={item?.name}
                                                    image={item?.images[0]}
                                                    status={item?.status}
                                                />
                                            )
                                        })
                                }
                            </>
                        )
                    }

                </ScrollView>
            </SafeAreaView>
        </ImageBackground>
    )
}
export default MyOrders;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexGrow: 1,
    },
    title: {
        textAlign: 'center',
        fontWeight: 'bold',
        fontSize: 19,
        letterSpacing: 3.3,
        color: black_t3,
        marginTop: 70
    }
});
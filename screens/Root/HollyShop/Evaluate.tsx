import React from "react";
import { Image, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../../navigation";
import Layout from "../../../constants/Layout";
import { black, black_t3, brown_t2, green_t2, white, yellow_t1 } from "../../../constants/Color";
import { shadow } from "../../../constants/Shadow";
import { TextArea } from "native-base";

const Evaluate: React.FC = () => {

    // -- NAVIGATION -- // 
    const navigation = useNavigation<screens>();

    // -- STARS -- //
    const [stars, setStars] = React.useState(4);

    // -- CONDITION -- // 
    const [condition, setCondition] = React.useState(false);

    // -- CONDITION TWO -- // 
    const [conditionTwo, setConditionTwo] = React.useState(false);

    return (
        <ImageBackground
            source={require('../../../assets/general/backgroundTwo.png')}
            style={styles.main}
        >
            <SafeAreaView>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <HeaderThree
                        basket={false}
                        search={false}
                        navigation={navigation}
                    />

                    {/* EVALUATE PRODUCT AND WIN HOLLY PUAN */}
                    <View style={[styles.formView, shadow]}>
                        <Text style={styles.evaluateTitle}>Ürünü değerlendir</Text>
                        <View style={styles.hPView}>
                            <Image
                                source={require('../../../assets/root/hP.png')}
                                style={styles.hPIcon}
                                resizeMode="contain"
                            />
                            <Text style={styles.hP}>10</Text>
                        </View>
                        <Text style={styles.hPWin}>KAZAN</Text>
                    </View>
                    <View style={[styles.formView, shadow, { marginTop: 20 }]}>
                        {/* PRODUCT NAME */}
                        <Text style={styles.productName}>Holly Tişört</Text>

                        {/* PRODUCT ID */}
                        <Text style={styles.productId}>(HL23T232)</Text>

                        {/* PRODUCT IMAGE */}
                        <Image
                            source={require('../../../assets/exp/blackTshirt.png')}
                            style={styles.productImage}
                            resizeMode="contain"
                        />

                        {/* STARS */}
                        <View style={styles.starsView}>
                            {
                                stars >= 1 ?
                                    <Image
                                        style={styles.star}
                                        source={require('../../../assets/root/fullStarTwo.png')}
                                        resizeMode="contain"
                                    />
                                    :
                                    <Image
                                        style={styles.star}
                                        source={require('../../../assets/root/emptyStarTwo.png')}
                                        resizeMode="contain"
                                    />
                            }
                            {
                                stars >= 2 ?
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/fullStarTwo.png')}
                                        resizeMode="contain"
                                    />
                                    :
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/emptyStarTwo.png')}
                                        resizeMode="contain"
                                    />
                            }
                            {
                                stars >= 3 ?
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/fullStarTwo.png')}
                                        resizeMode="contain"
                                    />
                                    :
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/emptyStarTwo.png')}
                                        resizeMode="contain"
                                    />
                            }
                            {
                                stars >= 4 ?
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/fullStarTwo.png')}
                                        resizeMode="contain"
                                    />
                                    :
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/emptyStarTwo.png')}
                                        resizeMode="contain"
                                    />
                            }
                            {
                                stars == 5 ?
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/fullStarTwo.png')}
                                        resizeMode="contain"
                                    />
                                    :
                                    <Image
                                        style={[styles.star, { marginLeft: 6 }]}
                                        source={require('../../../assets/root/emptyStarTwo.png')}
                                        resizeMode="contain"
                                    />
                            }
                        </View>

                        {/* EVALUATE VIEW */}
                        <Text style={styles.commentTitle}>Yorum yaz</Text>
                        <View style={[styles.commentView, shadow]}>
                            <TextArea
                                autoCompleteType={""}
                                placeholderTextColor={black_t3}
                                color={black}
                                backgroundColor={white}
                                borderWidth={0}
                                borderRadius={10}
                                fontSize={10}
                                placeholder="Ürünle ilgili memnuniyetini yazabilirsin"
                            />
                        </View>
                    </View>

                    {/* CONDITIONS */}
                    <View style={styles.conditionsView}>
                        <TouchableOpacity
                            onPress={() => {
                                setCondition(!condition)
                            }}
                            style={styles.conditionTouch}>
                            {
                                condition ?
                                    <View style={styles.conditionAltTouch} />
                                    :
                                    <></>
                            }
                        </TouchableOpacity>
                        <Text style={styles.conditionText}>Yorum detaylarımın site genelinde kullanılmasına izin veriyorum. <Text style={{ fontWeight: 'bold', fontSize: 14 }}>Aydınlatma metni</Text> için tıklayınız.</Text>
                    </View>

                    {/* CONDITIONS TWO */}
                    <View style={[styles.conditionsView, { marginBottom: 20 }]}>
                        <TouchableOpacity
                            onPress={() => {
                                setConditionTwo(!conditionTwo)
                            }}
                            style={styles.conditionTouch}>
                            {
                                conditionTwo ?
                                    <View style={styles.conditionAltTouch} />
                                    :
                                    <></>
                            }
                        </TouchableOpacity>
                        <Text style={styles.conditionText}>Değerlendirme yapmak için <Text style={{ fontWeight: 'bold', fontSize: 14 }}>Kullanıcı Sözleşmesi’ni</Text> kabul ediyorum.</Text>
                    </View>

                    {/* EVALUATE BUTTON */}
                    <TouchableOpacity style={styles.button}>
                        <Text style={styles.buttonText}>Değerlendir</Text>
                    </TouchableOpacity>
                </ScrollView>
            </SafeAreaView>
        </ImageBackground>
    )
}
export default Evaluate;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flex: 1 },
    formView: {
        borderRadius: 10,
        padding: 10,
        width: Layout.screen.width / 1.1,
        backgroundColor: white,
        alignSelf: 'center',
        paddingHorizontal: 15,
        alignItems: 'center'
    },
    evaluateTitle: {
        fontSize: 15,
        color: black_t3,
        fontWeight: 'bold',
        letterSpacing: 1.3,
        marginTop: 10
    },
    hPView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10
    },
    hPIcon: {
        width: 41,
        height: 45.42
    },
    hP: {
        marginLeft: 8,
        color: green_t2,
        fontSize: 25,
        fontWeight: 'bold'
    },
    hPWin: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 15,
        color: brown_t2,
        marginBottom: 10
    },
    productName: {
        alignSelf: 'center',
        fontSize: 22,
        fontWeight: 'bold',
        color: black_t3
    },
    productId: {
        alignSelf: 'center',
        marginTop: 2,
        fontSize: 14,
        color: black_t3
    },
    productImage: {
        width: 150,
        height: 150,
        marginTop: 10,
        alignSelf: 'center'
    },
    starsView: {
        flexDirection: 'row',
        marginTop: 10
    },
    star: {
        width: 35.83,
        height: 34.04
    },
    commentTitle: {
        marginTop: 8,
        fontSize: 15,
        fontWeight: 'bold',
        color: black_t3
    },
    commentView: {
        width: Layout.screen.width / 1.2,
        marginTop: 10
    },
    conditionsView: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 20
    },
    conditionTouch: {
        width: 30,
        height: 30,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: yellow_t1
    },
    conditionAltTouch: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: white
    },
    conditionText: {
        fontSize: 14,
        width: Layout.screen.width / 1.3,
        color: black,
        marginLeft: 10
    },
    button: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        backgroundColor: brown_t2,
        borderRadius: 10,
        height: 40,
        marginBottom: 40,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        color: white,
        fontWeight: 'bold',
        fontSize: 18
    },
});
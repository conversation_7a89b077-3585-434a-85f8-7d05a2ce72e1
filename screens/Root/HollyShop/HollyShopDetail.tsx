import React from "react";
import { StyleSheet, ImageBackground, Image, ScrollView, TouchableOpacity, Text, View } from "react-native";
import HeaderThree from "../../../components/HeaderThree";
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import HollyShopProduct from "../../../components/HollyShopProduct";
import HollyShopInfo from "../../../components/HollyShopInfo";
import Evaluations from "../../../components/Evaluations";
import { brown_t2, white, kremrengi } from "../../../constants/Color";
import Layout from "../../../constants/Layout";
import Swiper from "react-native-swiper";
import { getImageURL, post } from "../../../networking/Server";
import { screens } from "../../../navigation";
import Toast from "../../../components/Toast";
import { Spinner } from "native-base";
import { MainStore } from "../../../stores/MainStore";

const HollyShopDetail: React.FC = (props: any) => {

    const productId = props.route?.params?.productId;

    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();

    // -- MODAL -- //
    const [modal, setModal] = React.useState(false);

    // -- status Toast -- //
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500)
    }

    // -- DATA -- //
    const [data, setData]: any = React.useState({});
    const [loading, setLoading] = React.useState(true);

    const [header, setHeader] = React.useState(false);

    const handleScroll = (event: any) => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < 5) {
            setHeader(false);
        } else {
            setHeader(true);
        }
    };

    // -- SELECT ATT -- //
    const [selectAtt, setSelectAtt] = React.useState("");


    const getProduct = () => {
        try {
            post("shop/product-details", { productId }).then((res: any) => {
                if (res.type == "success") {
                    setData(res.data)

                    setLoading(false)
                } else {
                    startToast("Bir şeyler ters gitti.", "error");
                    setTimeout(() => {
                        navigation.pop();
                    }, 2500)
                }

            })
        } catch (e) {
            startToast("Bir şeyler ters gitti.", "error");
            setTimeout(() => {
                navigation.pop();
            }, 2500)
        }
    }

    React.useEffect(() => {
        getProduct();
    }, []);

    const insets = useSafeAreaInsets();

    return (
        <View
            style={styles.main}
        >
            {/* TOAST */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={brown_t2}
                />
            </View>

            <SafeAreaView style={{
                zIndex: 10,
                position: 'absolute',
                width: Layout.screen.width
            }}>
                <HeaderThree
                    headerStatus={header}
                    search={false}
                    navigation={navigation}
                    basket={true}
                    onSearch={undefined}
                />
            </SafeAreaView>

            {/* MODAL AREA */}
            {
                modal ?
                    <View style={styles.modal}>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseTopTouch}
                        />
                        <View style={styles.modalAltView}>
                            <Swiper
                                style={styles.swiper}
                                dotColor={white}
                                dotStyle={styles.dotStyle}
                                activeDotStyle={styles.dotStyle}
                                activeDotColor={brown_t2}
                            >
                                {
                                    data?.images?.map((item: any, index: React.Key) => {
                                        return (
                                            <Image
                                                key={index}
                                                style={styles.modalImg}
                                                source={{ uri: getImageURL(item) }}
                                            />
                                        )
                                    })
                                }
                            </Swiper>
                        </View>
                        <TouchableOpacity
                            onPress={() => {
                                setModal(false)
                            }}
                            style={styles.modalCloseBottomTouch}
                        />
                    </View>
                    :
                    <></>
            }

            <Image
                source={require('../../../assets/root/hollyShopDetailBacknoshadow.png')}
                style={styles.topRightBack}
                resizeMode="cover"
            />

            {
                loading ?
                    <Spinner color={brown_t2} size={18} alignSelf={'flex-start'} ml={10} style={{ marginTop: Math.max(insets.bottom, 0) ? 120 : 65 }} />
                    :
                    <>
                        <ScrollView
                            onScroll={handleScroll}
                            style={
                                [
                                    styles.scroll,
                                    {
                                        paddingTop: Math.max(insets.bottom, 0) ? 120 : 65
                                    }
                                ]
                            }
                            showsVerticalScrollIndicator={false}
                        >
                            {/* PRODUCT INFORMATION */}
                            <HollyShopProduct
                                setModal={() => {
                                    setModal(true)
                                }}
                                details={data?.details}
                                discountPercentage={data?.discountPercentage}
                                image={data?.images[0]}
                                options={data?.options}
                                price={data?.price}
                                discountedPrice={data?.discountedPrice}
                                hollyPoints={data?.hollyPoints}
                                name={data?.name}
                                selectAtt={selectAtt}
                                onPressSizes={(set: string) => {
                                    setSelectAtt(set);
                                }}
                            />

                            {/* INFO */}
                            <HollyShopInfo
                                hollyPoints={data?.hollyPoints}
                                avarageRating={data?.avarageRating}
                                description={data?.description}

                            />
                            {/* EVALUATIONS */}
                            {
                                data?.comments?.length < 1 ?
                                    <></>
                                    :
                                    < Evaluations comments={data?.comments} />
                            }

                        </ScrollView>

                        {/* BASKET BUTTON */}
                        <TouchableOpacity
    onPress={() => {
        const result = MainStore.basket.filter(
            (item_: any) => item_.id == data?.id && item_.model == "shop"
        );

        // Eğer seçenekler varsa, seçimin zorunlu olmasını kontrol et
        const isSelectionRequired = data?.options?.length > 0;
        const isSelectionMade = selectAtt !== "";

        if (result.length < 1) {
            // Eğer seçenekler varsa ve seçim yapıldıysa ya da seçenekler yoksa doğrudan ekle
            if (!isSelectionRequired || isSelectionMade) {
                MainStore.setBasket(data, false, selectAtt);
                startToast("Sepete eklendi.", "success");
                setTimeout(() => {
                    navigation.navigate("Basket");
                }, 500);
            } else {
                startToast("Bir seçim yapmalısınız.", "error");
            }
        } else {
            startToast("Zaten sepette bulunuyor.", "error");
        }
    }}
    style={styles.basketButton}
>
    <Text style={styles.basket}>SEPETE EKLE</Text>
</TouchableOpacity>

                    </>
            }
        </View>
    )
}
export default HollyShopDetail;

// -- STYLES -- // 
const styles = StyleSheet.create({
    main: { flex: 1,
    backgroundColor: kremrengi },
    toastView: {
        zIndex: 12,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center'
    },
    modal: {
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalCloseTopTouch: {
        height: Layout.screen.height / 3,
        width: Layout.screen.width,
        position: 'absolute',
        top: 0
    },
    modalAltView: {
        width: Layout.screen.width / 1.1,
        paddingVertical: 0,
        backgroundColor: "rgba(256,256,256,0.8)",
        borderRadius: 32,
        minHeight: 300
    },
    swiper: {
        height: '100%',
        zIndex: 2
    },
    dotStyle: {
        height: 10,
        width: 10,
        borderRadius: 5
    },
    modalImg: {
        height: 180,
        width: 150,
        marginTop: 40,
        alignSelf: 'center'
    },
    modalCloseBottomTouch: {
        width: Layout.screen.width,
        height: Layout.screen.height / 3,
        position: 'absolute',
        bottom: 0
    },
    topRightBack: {
        position: 'absolute',
        right: 0,
        height: 406,
        width: 149
    },
    scroll: { marginBottom: 40 },
    basketButton: {
        backgroundColor: brown_t2,
        height: 195,
        borderTopLeftRadius: 65,
        borderTopRightRadius: 65,
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        position: 'absolute',
        bottom: -140,
        zIndex: 0,
        alignItems: 'center'
    },
    basket: {
        marginTop: 20,
        color: white,
        fontWeight: 'bold',
        fontSize: 16
    },
});
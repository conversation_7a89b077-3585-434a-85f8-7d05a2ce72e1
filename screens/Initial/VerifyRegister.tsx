import React, { useRef, useState, useEffect } from "react";
import { 
  StyleSheet, 
  ImageBackground, 
  Image, 
  View,
  KeyboardAvoidingView, 
  Text,
  Dimensions, 
  TextInput, 
  SafeAreaView, 
  ScrollView, 
  TouchableWithoutFeedback, 
  Keyboard,
  Platform,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  TouchableOpacity
} from "react-native";
import Layout from "../../constants/Layout";
import { green_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import { post } from "../../networking/Server";
import { Spinner } from "native-base";
import Toast from "../../components/Toast";
import { MainStore } from "../../stores/MainStore";
import { BackIcon } from "../../components/Svgs";

const VerifyRegister: React.FC = (props: any) => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast] = useState<boolean | null>(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");
    const [code, setCode] = useState(["", "", "", ""]);
    const [loading, setLoading] = useState(false);
    const [expectedCode, setExpectedCode] = useState<string>("");

    useEffect(() => {
        // Kodu string olarak garanti altına al
        const codeFromParams = String(props.route?.params?.code || "");
        setExpectedCode(codeFromParams);
        
        // Bileşen açıldığında inputları resetle
        setCode(["", "", "", ""]);
    }, []);

    const inputRefs = [
        useRef<TextInput>(null),
        useRef<TextInput>(null),
        useRef<TextInput>(null),
        useRef<TextInput>(null)
    ];

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const handleChange = (text: string, index: number) => {
        const numericText = text.replace(/[^0-9]/g, '');
        const newCode = [...code];
        
        if (numericText.length > 0) {
            newCode[index] = numericText[0];
            if (index < 3) inputRefs[index + 1].current?.focus();
        } else {
            newCode[index] = "";
            if (index > 0) inputRefs[index - 1].current?.focus();
        }
        
        setCode(newCode);
    };

    const handleKeyPress = (
        { nativeEvent }: NativeSyntheticEvent<TextInputKeyPressEventData>, 
        index: number
    ) => {
        if (nativeEvent.key === 'Backspace' && !code[index] && index > 0) {
            inputRefs[index - 1].current?.focus();
        }
    };

    const verify = () => {
        const enteredCode = code.join("");

        if (enteredCode.length !== 4) {
            startToast("Lütfen tüm alanları doldurun", "error");
            return;
        }

        if (enteredCode === expectedCode) {
            // Başarılı doğrulama işlemleri
            startToast("Doğrulama Başarılı!", "success");
            post("users/verify-register", { 
                userId: String(props.route?.params?.userId) 
            }).then((res: any) => {
                if (res.type === "success") {
                    navigation.pop(2);
                } else {
                    setLoading(false);
                    startToast(res.error, "error");
                }
            });
        } else {
            startToast("Kod uyuşmuyor", "error");
        }
    };

     const isIOSWithNotch = () => {
                const { height } = Dimensions.get('window');
                return (
                  Platform.OS === 'ios' && 
                  !Platform.isPad && 
                  (height >= 812 || Dimensions.get('screen').height >= 812)
                );
              };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.gifBackground}>
                <View style={styles.toastView}>
                    <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
                </View>
                <KeyboardAvoidingView
                                                            behavior={Platform.OS === "ios" ? "padding" : "height"}
                                                            keyboardVerticalOffset={
                                                                isIOSWithNotch() ? 10 : 
                                                                Platform.OS === "ios" ? 0 : 30
                                                              }
                                                          >
                <SafeAreaView style={styles.container}>
                    <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
                                                                    <BackIcon
                                                                                            size={25}
                                                                                            color={white}
                                                                                        />
                                                                </TouchableOpacity>
                    <ScrollView 
                        contentContainerStyle={styles.scrollContainer} 
                        keyboardShouldPersistTaps="handled"
                    >
                        <Image 
                            source={require('../../assets/initial/hollyent.png')} 
                            style={styles.logo} 
                            resizeMode="contain" 
                        />
                        <View style={styles.textAndInput}>
                            <Text style={styles.title}>{MainStore.language.verify_info}</Text>
                            <View style={styles.inputContainer}>
                            {code.map((digit, index) => (
            <TextInput
                key={index}
                ref={inputRefs[index]}
                style={styles.input}
                keyboardType="number-pad"
                maxLength={1}
                value={digit}
                onChangeText={(text) => handleChange(text, index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                autoFocus={index === 0}
                textAlign="center"
                selectionColor={white}
                caretHidden={false}
            />
        ))}
                            </View>
                        </View>
                        <View style={styles.buttonContainer}>
                            {loading ? (
                                <Spinner color={white} size={18} />
                            ) : (
                                <SwipeButton onToggle={verify} buttonText="Doğrula" />
                            )}
                        </View>
                    </ScrollView>
                </SafeAreaView>
                </KeyboardAvoidingView>
            </ImageBackground>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    gifBackground: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
    },
    goBackButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 30 : 20,
        left: -30,
        zIndex: 10,
        padding: 10
    },
    container: {
        flex: 1,
        width: '100%',
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 20,
    },
    logo: {
        height: 100,
        marginBottom: 40,

         width: Layout.screen.width / 2,
                alignSelf: 'center',
                marginTop: 80,
    },
    textAndInput: {
        alignItems: 'center',
        marginBottom: 40,
    },
    title: {
        fontSize: 18,
        color: white,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 30,
    },
    inputContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: Layout.screen.width * 0.6,
    },
    input: {
        width: 50,
        height: 50,
        fontSize: 24,
        fontWeight: 'bold',
        backgroundColor: 'rgba(255,255,255,0.2)',
        borderRadius: 10,
        color: white,
        borderWidth: 1,
        borderColor: white,
        marginHorizontal: 5,
    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        paddingBottom: 20,
    },
});

export default VerifyRegister;
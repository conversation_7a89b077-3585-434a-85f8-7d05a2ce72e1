import React, { useEffect, useState } from "react";
import { Image, ImageBackground, Linking, Platform, StyleSheet, Text, TouchableOpacity, View, SafeAreaView, BackHandler, Alert } from "react-native";
import Layout from "../../constants/Layout";
import { black, green_t1, blue_t4, white } from "../../constants/Color";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import { post } from "../../networking/Server";
import { goPage } from "../../functions/goPage";
import { MainStore } from "../../stores/MainStore";
import Toast from "../../components/Toast";
import SwipeButtonInitialTwo from "../../components/SwipeButtonInitialTwo";
import { AnnouncementStore } from "../../stores/AnnouncementStore";
import { WheelStoreInstance as WheelStore } from "../../stores/WheelStore";
import { HollyPointsStoreInstance as HollyPointsStore } from "../../stores/HollyPointsStore";
import { ShamanStoreInstance as ShamanStore } from "../../stores/ShamanStore";
import { HollyShopStoreInstance as HollyShopStore } from "../../stores/HollyShopStore";

require('dayjs/locale/tr');
require('dayjs/locale/en');
const dayjs = require('dayjs');

const _images = [
  { id: 6, image: require('../../assets/initial/holly-white.png') },
  { id: 3, image: require('../../assets/initial/hollygar.png') },
  { id: 4, image: require('../../assets/initial/street.png') },
  { id: 5, image: require('../../assets/initial/ticket.png') },
  { id: 1, image: require('../../assets/initial/atrium.png') },
  { id: 2, image: require('../../assets/initial/magnus.png') },
  { id: 7, image: require('../../assets/dorock.png') },
  { id: 7, image: require('../../assets/initial/thepub.png') },
  { id: 8, image: require('../../assets/initial/odinlogo.png') },


];

const APP_VERSIONS = {
  ios: "1.0.0",
  android: "1.1.4",
};

const Initial: React.FC = () => {
  const navigation = useNavigation<screens>();
  const [loading, setLoading] = useState(true);
  const [needsUpdate, setNeedsUpdate] = useState(false);
  const [updateInfo, setUpdateInfo] = useState({ message: "", url: "" });
  const [statusToast, setStatusToast]: any = useState(null);
  const [typeToast, setTypeToast] = useState("");
  const [subtitleToast, setSubTitleToast] = useState("");

  const startToast = (message: string, type: string) => {
    setStatusToast(true);
    setSubTitleToast(message);
    setTypeToast(type);
    setTimeout(() => setStatusToast(false), 1500);
  };

  const handleSwipe = (direction: any) => {
    if (direction === 'left') {
      navigation.navigate("Login");
    } else if (direction === 'right') {
      navigation.navigate("Register");
    }
  };

  const checkVersion = async () => {
    const currentVersion = Platform.OS === 'ios' ? APP_VERSIONS.ios : APP_VERSIONS.android;
    const res: any = await post("version", { currentVersion, platform: Platform.OS });
    if (res.type === "success") {
      if (res.needsUpdate) {
        setNeedsUpdate(true);
        setUpdateInfo({ message: res.updateMessage, url: res.updateUrl });
        setLoading(false);
      } else {
        await controlToken();
      }
    } else {
      startToast(res.error, "error");
      setLoading(false);
    }
  };

  const controlToken = async () => {
    if (!MainStore.token) {
      startToast(MainStore.language.welcome, "success");
      setLoading(false);
    } else {
      const res: any = await post("users/validate");
      if (res.type === "success") {
        startToast(res.message, "success");

        await Promise.all([
          AnnouncementStore.fetchAnnouncements(),
          WheelStore.fetchWheelInfo(),
          HollyPointsStore.fetchHollyPoints(),
          ShamanStore.fetchShamanData(),
          HollyShopStore.fetchShopData(),
        ]);

        if (Object.keys(MainStore.city).length === 0) {
          goPage(navigation, "ChooseCity", {}, false);
        } else {
          goPage(navigation, "Root", {}, false);
        }
      } else {
        startToast(res.error, "error");
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    const initializeApp = async () => {
      setLoading(true);
      await checkVersion();
    };
    initializeApp();
    dayjs.locale(MainStore.language.language === "tr" ? 'tr' : 'en');
  }, []);

  if (loading) {
    return (
      <ImageBackground
        source={require('../../assets/initial/pgif.gif')}
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
      >
        <Image
          source={require('../../assets/root/hollyentgif.gif')}
          style={{ width: 200, height: 200 }}
          resizeMode="contain"
        />
      </ImageBackground>
    );
  }

  if (needsUpdate) {
    return (
      <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.updateBackground}>
        <View style={styles.updateContainer}>
          <Text style={styles.updateTitle}>Güncelleme Gerekli</Text>
          <Text style={styles.updateMessage}>{updateInfo.message}</Text>
          <TouchableOpacity
            style={styles.updateButton}
            onPress={() => {
              // Önce uygulama mağazasına yönlendir
              Linking.openURL(updateInfo.url);

              // Platform'a göre uygulamayı kapat veya mesaj göster
              if (Platform.OS === 'android') {
                // Android'de uygulamayı kapat
                setTimeout(() => {
                  BackHandler.exitApp();
                }, 1000);
              } else {
                // iOS'ta kullanıcıya uygulamayı kapatması için mesaj göster
                setTimeout(() => {
                  Alert.alert(
                    "Uygulamayı Kapatın",
                    "Lütfen uygulamayı kapatıp, güncelleme tamamlandıktan sonra tekrar açın.",
                    [{ text: "Tamam", onPress: () => {} }]
                  );
                }, 1000);
              }
            }}
          >
            <Text style={styles.updateButtonText}>Şimdi Güncelle</Text>
          </TouchableOpacity>
        </View>
      </ImageBackground>
    );
  }

  return (
    <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.gifBackground}>
      <View style={styles.toastView}>
        <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
      </View>
      <SafeAreaView style={styles.container}>
        <Image source={require('../../assets/initial/hollyent.png')} style={styles.logo} resizeMode="contain" />
        <View style={styles.imageContainer}>
          {_images.map((item, index) => (
            <Image key={index} source={item.image} style={styles.otherImg} resizeMode="contain" />
          ))}
        </View>
        <View style={styles.buttonContainer}>
          <SwipeButtonInitialTwo onSwipe={handleSwipe} />
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default Initial;

const styles = StyleSheet.create({
  gifBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toastView: {
    position: 'absolute',
    top: 0,
    zIndex: 20,
  },
  container: {
    flex: 1,
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logo: {
    width: Layout.screen.width / 2,
    alignSelf: 'center',
    marginTop: 80,
    height: 80,
  },
  imageContainer: {
    alignItems: 'center',
  },
  otherImg: {
    height: 30,
    width: 140,
    marginVertical: 10,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
    paddingBottom: 20
  },
  updateBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    width: '80%',
  },
  updateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: black,
  },
  updateMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: black,
  },
  updateButton: {
    backgroundColor: blue_t4,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  updateButtonText: {
    color: white,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

import React, { useRef } from "react";
import { StyleSheet, Dimensions, KeyboardAvoidingView, Platform, ImageBackground, Image, View, Text, TextInput, SafeAreaView, ScrollView, TouchableWithoutFeedback, Keyboard, TouchableOpacity } from "react-native";
import Layout from "../../constants/Layout";
import { green_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import Toast from "../../components/Toast";
import { MainStore } from "../../stores/MainStore";
import { Spinner } from "native-base";
import { BackIcon } from "../../components/Svgs";

const Verify: React.FC = (props: any) => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast]: any = React.useState(null);
    const [typeToast, setTypeToast] = React.useState("");
    const [subtitleToast, setSubTitleToast] = React.useState("");
    const [code, setCode] = React.useState(["", "", "", ""]);
    const [loading, setLoading] = React.useState(false);
    const inputRefs = [useRef(null), useRef(null), useRef(null), useRef(null)];

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const handleChange = (text: string, index: number) => {
        const numericText = text.replace(/[^0-9]/g, '');
        const newCode = [...code];
        
        if (numericText.length > 0) {
            newCode[index] = numericText[0];
            if (index < 3) inputRefs[index + 1].current?.focus();
        } else {
            newCode[index] = "";
            if (index > 0) inputRefs[index - 1].current?.focus();
        }
        
        setCode(newCode);
    };

    const verify = () => {
        setLoading(true);
    
        if (code.join("").trim() === String(props.route.params.code).trim()) {
            startToast(MainStore.language.success_code, "success");
            setTimeout(() => {
                //@ts-ignore
                navigation.navigate("UpdatePassword", { phone: props.route.params.phone });
            }, 1500);
        } else {
            startToast(MainStore.language.no_match_code, "error");
        }
    
        setLoading(false);
    };

    const isIOSWithNotch = () => {
            const { height } = Dimensions.get('window');
            return (
              Platform.OS === 'ios' && 
              !Platform.isPad && 
              (height >= 812 || Dimensions.get('screen').height >= 812)
            );
          };
    
    

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.gifBackground}>
                <View style={styles.toastView}>
                    <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
                </View>
                <KeyboardAvoidingView
                                            behavior={Platform.OS === "ios" ? "padding" : "height"}
                                            keyboardVerticalOffset={
                                                isIOSWithNotch() ? 10 : 
                                                Platform.OS === "ios" ? 0 : 30
                                              }
                                          >
                
                <SafeAreaView style={styles.container}>
                    <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
                                                <BackIcon
                                                                        size={25}
                                                                        color={white}
                                                                    />
                                            </TouchableOpacity>
                    <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
                        <Image source={require('../../assets/initial/hollyent.png')} style={styles.logo} resizeMode="contain" />
                        <View style={styles.textAndInput}>
                            <Text style={styles.title}>{MainStore.language.verify_info}</Text>
                            <View style={styles.inputContainer}>
                                {code.map((digit, index) => (
                                    <TextInput
                                        key={index}
                                        ref={inputRefs[index]}
                                        style={styles.input}
                                        keyboardType="number-pad"
                                        maxLength={1}
                                        value={digit}
                                        onChangeText={(text) => handleChange(text, index)}
                                        autoFocus={index === 0}
                                        textAlign="center"
                                    />
                                ))}
                            </View>
                        </View>
                        <View style={styles.buttonContainer}>
                            {loading ? (
                                <Spinner color={white} size={18} />
                            ) : (
                                <SwipeButton onToggle={verify} buttonText={MainStore.language.approve} />
                            )}
                        </View>
                    </ScrollView>
                </SafeAreaView>
                </KeyboardAvoidingView>
            </ImageBackground>
        </TouchableWithoutFeedback>
    );
};

export default Verify;

const styles = StyleSheet.create({
    gifBackground: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goBackButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 30 : 20,
        left: -30,
        zIndex: 10,
        padding: 10
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
    },
    container: {
        flex: 1,
        width: '100%',
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    logo: {
        width: Layout.screen.width / 2,
               alignSelf: 'center',
               marginTop: 80,
               height: 100,

    },
    textAndInput: {
        alignItems: 'center',
    },
    title: {
        fontSize: 18,
        color: white,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
    },
    inputContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '60%',
    },
    input: {
        width: 50,
        height: 50,
        fontSize: 24,
        fontWeight: 'bold',
        backgroundColor: 'rgba(255,255,255,0.2)',
        borderRadius: 10,
        color: white,
        textAlign: 'center',
        borderWidth: 1,
        borderColor: white,
        marginHorizontal: 5,

    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        marginTop: 20,
        paddingBottom: 20

    },
});
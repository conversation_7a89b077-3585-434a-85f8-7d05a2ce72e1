import React, { useState } from "react";
import { ImageBackground, Dimensions,  StyleSheet, Image, KeyboardAvoidingView, ScrollView, View, Text, SafeAreaView, TouchableWithoutFeedback, Keyboard, TouchableOpacity, Platform } from "react-native";
import InitialInput from "../../components/InitialInput";
import Layout from "../../constants/Layout";
import { green_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { post } from "../../networking/Server";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import { Spinner } from "native-base";
import Toast from "../../components/Toast";
import GlobalCode from "../../components/GlobalCode";
import { MainStore } from "../../stores/MainStore";
import { BackIcon } from "../../components/Svgs";

const Register: React.FC = () => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast]: any = useState(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");
    const [loading, setLoading] = useState(false);
    const [name, setName] = useState("");
    const [surname, setSurname] = useState("");
    const [phone, setPhone] = useState("");
    const [mail, setMail] = useState("");
    const [password, setPassword] = useState("");
    const [rePassword, setRePassword] = useState("");
    const [birthday, setBirthday] = useState("");
    const [referans, setReferans] = useState("");
    const [modal, setModal] = useState(false);
    const [countryCode, setCountryCode] = useState("90");
    const [passwordVisible, setPasswordVisible] = useState(false);

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const handleRegister = () => {
        if (!name || !surname || !phone || !mail || !password || !rePassword) {
            startToast(MainStore.language.no_empty, "error");
        } else {
            setLoading(true);
            post("users/register", {
                firstName: name,
                lastName: surname,
                phoneNumber: "+" + countryCode + phone,
                email: mail,
                password: password,
                dateOfBirth: birthday,
                referralCode: referans
            }).then((res: any) => {
                if (res.type == "success") {
                    //@ts-ignore
                    navigation.navigate("VerifyRegister", {
                        code: res.code,
                        userId: res.userId
                    });
                    startToast(res.message, "success");
                } else {
                    startToast(res.error, "error");
                    setLoading(false);
                }
            });
        }
    };

    const isIOSWithNotch = () => {
        const { height } = Dimensions.get('window');
        return (
          Platform.OS === 'ios' && 
          !Platform.isPad && 
          (height >= 812 || Dimensions.get('screen').height >= 812)
        );
      };
    
    
    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.gifBackground}>
                <View style={styles.toastView}>
                    <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
                </View>
                <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            keyboardVerticalOffset={
                isIOSWithNotch() ? 10 : 
                Platform.OS === "ios" ? 0 : 30
              }
          >
                <SafeAreaView style={styles.container}>

                <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
                            <BackIcon
                                                    size={25}
                                                    color={white}
                                                />
                        </TouchableOpacity>
               
            <Image source={require('../../assets/initial/hollyent.png')} style={styles.logo} resizeMode="contain" />
                    <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled"               contentInsetAdjustmentBehavior="automatic"
                    >
                        <View style={styles.inputContainer}>
                            <InitialInput
                            image={require('../../assets/initial/nameSurname.png')} 
                            onChangeText_={setName}
                            required={true}
                            placeHolder={MainStore.language.name}
                            value={name}
                             />
                            <View style={styles.inputSpacing} />
                            <InitialInput required={true} image={require('../../assets/initial/nameSurname.png')} value={surname} onChangeText_={setSurname} placeHolder={MainStore.language.surname} />
                            <View style={styles.inputSpacing} />
                               <InitialInput required={true}
                                                    countryCode={countryCode} 
                                                    setModal={setModal}     
                                                    onChangeText_={setPhone} 
                                                    image={require('../../assets/initial/phone.png')} 
                                                    value={phone}
                                                    keyboardType="phone-pad" 
                                                    placeHolder={MainStore.language.phone} />
                            <View style={styles.inputSpacing} />
                            <InitialInput required={true}  image={require('../../assets/initial/mail.png')} value={mail} onChangeText_={setMail} keyboardType="email-address" placeHolder={MainStore.language.mail} />
                            <View style={styles.inputSpacing} />
                            <View style={styles.passwordContainer}>
                                <InitialInput  required={true} image={require('../../assets/initial/password.png')} value={password} onChangeText_={setPassword} secureTextEntry={!passwordVisible} placeHolder={MainStore.language.password} />
                                <TouchableOpacity onPress={() => setPasswordVisible(!passwordVisible)} style={styles.eyeIcon}>
                                    <Image source={require('../../assets/eye.png')} style={styles.iconImage} />
                                </TouchableOpacity>
                            </View>
                            <View style={styles.inputSpacing} />
                            <View style={styles.passwordContainer}>
                                <InitialInput  required={true} image={require('../../assets/initial/password.png')} onChangeText_={setRePassword} secureTextEntry={!passwordVisible} value={rePassword} placeHolder={MainStore.language.repassword} />
                            </View>
                            <View style={styles.inputSpacing} />
                            <InitialInput image={require('../../assets/initial/calendar.png')}  onChangeText_={setBirthday} value={birthday}     isDateField={true}
 placeHolder={MainStore.language.birthday} />
                            <View style={styles.inputSpacing} />
                            <InitialInput image={require('../../assets/initial/referans.png')} onChangeText_={setReferans} value={referans} placeHolder={MainStore.language.referance_code} />
                        </View>
                        
                    </ScrollView>

                    
        
                    
                   
                    <View style={styles.buttonContainer}>
                        {loading ? (
                            <Spinner color={white} size={18} />
                        ) : (
                            <SwipeButton onToggle={handleRegister} buttonText={MainStore.language.send_code} />
                        )}
                    </View>
                   

                </SafeAreaView>
                </KeyboardAvoidingView>

            </ImageBackground>

        </TouchableWithoutFeedback>
    );
};

export default Register;

const styles = StyleSheet.create({
    gifBackground: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goBackButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 30 : 20,
        left: -30,
        zIndex: 10,
        padding: 10
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
    },
    container: {
        flex: 1,
        width: '100%',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
    },
    inputSpacing: {
        height: 15,
    },
    logo: {
        width: Layout.screen.width / 2,
        alignSelf: 'center',
        height: 100,
        marginTop: 40

    },
    inputContainer: {
        alignItems: 'center',
        paddingTop: 10,
        width: '90%',
    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        marginTop: 20,
        paddingBottom: 20

    },
    passwordContainer: {
        position: 'relative',
    },
    eyeIcon: {
        position: 'absolute',
        right: 30,
        top: '50%',
        transform: [{ translateY: -12 }],
    },
    iconImage: {
        width: 24,
        height: 24,
    },
});

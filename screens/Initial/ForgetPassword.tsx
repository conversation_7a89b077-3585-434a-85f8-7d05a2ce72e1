import React, { useState, useEffect } from "react";
import { StyleSheet, Platform, KeyboardAvoidingView, Dimensions, ImageBackground, Image, View, Text, Keyboard, TouchableWithoutFeedback, SafeAreaView, ScrollView, TouchableOpacity } from "react-native";
import Layout from "../../constants/Layout";
import InitialInput from "../../components/InitialInput";
import { green_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import { post } from "../../networking/Server";
import { Spinner } from "native-base";
import Toast from "../../components/Toast";
import GlobalCode from "../../components/GlobalCode";
import { MainStore } from "../../stores/MainStore";
import { BackIcon } from "../../components/Svgs";

const ForgetPassword: React.FC = () => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast]: any = useState(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");
    const [phone, setPhone] = useState("");
    const [email, setEmail] = useState("");
    const [loading, setLoading] = useState(false);
    const [modal, setModal] = useState(false);
    const [countryCode, setCountryCode] = useState("90");
    const [resetMethod, setResetMethod] = useState("phone"); // "phone" veya "email"

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => setStatusToast(false), 1500);
    };

    const handleSendCode = () => {
        Keyboard.dismiss();

        // Seçilen yönteme göre validasyon
        if (resetMethod === "phone" && !phone) {
            startToast("Lütfen telefon numaranızı girin", "error");
            return;
        } else if (resetMethod === "email" && !email) {
            startToast("Lütfen e-posta adresinizi girin", "error");
            return;
        } else if (resetMethod === "email" && !/\S+@\S+\.\S+/.test(email)) {
            startToast("Lütfen geçerli bir e-posta adresi girin", "error");
            return;
        }

        setLoading(true);

        // Seçilen yönteme göre farklı API isteği
        const endpoint = resetMethod === "phone" ? "users/send-sms" : "users/send-email";
        const data = resetMethod === "phone"
            ? { phoneNumber: "+" + countryCode + phone, type: "phone" }
            : { email: email, type: "email" };

        post(endpoint, data)
            .then((res: any) => {
                if (res.type == "error") {
                    startToast(res.error, "error");
                } else {
                    startToast(res.message, "success");
                    if (resetMethod === "phone") {
                        //@ts-ignore
                        navigation.navigate("Verify", {
                            code: res.code,
                            phone: "+" + countryCode + phone,
                            resetMethod: "phone"
                        });
                    } else {
                        //@ts-ignore
                        navigation.navigate("Verify", {
                            code: res.code,
                            email: email,
                            resetMethod: "email"
                        });
                    }
                }
            })
            .finally(() => setLoading(false));
    };

     const isIOSWithNotch = () => {
                const { height } = Dimensions.get('window');
                return (
                  Platform.OS === 'ios' &&
                  !Platform.isPad &&
                  (height >= 812 || Dimensions.get('screen').height >= 812)
                );
              };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground source={require('../../assets/initial/pgif.gif')} style={styles.gifBackground}>
                <View style={styles.toastView}>
                    <Toast type={typeToast} subtitle={subtitleToast} status={statusToast} successColor={green_t1} />
                </View>
                <KeyboardAvoidingView
                                            behavior={Platform.OS === "ios" ? "padding" : "height"}
                                            keyboardVerticalOffset={
                                                isIOSWithNotch() ? 10 :
                                                Platform.OS === "ios" ? 0 : 30
                                              }
                                          >
                <SafeAreaView style={styles.container}>
                    <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
                                                                    <BackIcon
                                                                                            size={25}
                                                                                            color={white}
                                                                                        />
                                                                </TouchableOpacity>
                    <Image source={require('../../assets/initial/hollyent.png')} style={styles.logo} resizeMode="contain" />
                    <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
                        <View style={styles.content}>
                            <View style={styles.badge}>
                                <Text style={styles.badgeText}>
                                    {resetMethod === "phone"
                                        ? "Uygulamaya kayıt olduğunuz telefon numarasını giriniz!"
                                        : "Uygulamaya kayıt olduğunuz e-posta adresinizi giriniz!"}
                                </Text>
                            </View>

                            {/* Telefon numarası girişi */}
                            {resetMethod === "phone" && (
                                <View>
                                    <InitialInput
                                        countryCode={countryCode}
                                        setModal={setModal}
                                        value={phone}
                                        isPhone={true}
                                        image={require('../../assets/initial/phone.png')}
                                        keyboardType="phone-pad"
                                        placeHolder={MainStore.language.phone}
                                        secureTextEntry={false}
                                        onChangeText_={setPhone}
                                    />
                                    <TouchableOpacity
                                        style={styles.switchMethodLink}
                                        onPress={() => setResetMethod("email")}
                                    >
                                        <Text style={styles.switchMethodText}>E-posta ile de sıfırlayabilirsiniz</Text>
                                    </TouchableOpacity>
                                </View>
                            )}

                            {/* E-posta girişi */}
                            {resetMethod === "email" && (
                                <View>
                                    <InitialInput
                                        value={email}
                                        isPhone={false}
                                        image={require('../../assets/initial/mail.png')}
                                        keyboardType="email-address"
                                        placeHolder="E-posta adresiniz"
                                        secureTextEntry={false}
                                        onChangeText_={setEmail}
                                    />
                                    <TouchableOpacity
                                        style={styles.switchMethodLink}
                                        onPress={() => setResetMethod("phone")}
                                    >
                                        <Text style={styles.switchMethodText}>Telefon numarası ile de sıfırlayabilirsiniz</Text>
                                    </TouchableOpacity>
                                </View>
                            )}
                        </View>
                    </ScrollView>
                    <View style={styles.buttonContainer}>
                        {loading ? (
                            <Spinner color={white} size={18} />
                        ) : (
                            <SwipeButton onToggle={handleSendCode} buttonText={MainStore.language.send_code} />
                        )}
                    </View>
                </SafeAreaView>
                </KeyboardAvoidingView>
            </ImageBackground>
        </TouchableWithoutFeedback>
    );
};

export default ForgetPassword;

const styles = StyleSheet.create({
    gifBackground: {
        flex: 1,
        alignItems: 'center',
    },
    goBackButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 30 : 20,
        left: -30,
        zIndex: 10,
        padding: 10
    },
    toastView: {
        position: 'absolute',
        top: 0,
        zIndex: 20,
    },
    container: {
        flex: 1,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
    },
    logo: {
         width: Layout.screen.width / 2,
                alignSelf: 'center',
                marginTop: 80,
                height: 100,
    },
    content: {
        alignItems: 'center',
        width: '90%',
    },
    badge: {
        backgroundColor: 'rgba(255,255,255,0.2)',
        padding: 20,
        width: Layout.screen.width / 1.4,
        borderRadius: 20,
        marginBottom: 20,
    },
    badgeText: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        paddingBottom: 10
    },
    switchMethodLink: {
        alignSelf: 'flex-end',
        marginTop: 8,
        marginRight: 10,
        paddingVertical: 5,
    },
    switchMethodText: {
        color: white,
        fontSize: 12,
        textDecorationLine: 'underline',
    },
});

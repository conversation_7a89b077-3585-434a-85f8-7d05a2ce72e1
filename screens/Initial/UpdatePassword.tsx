import React, { useState, useEffect } from "react";
import { 
  StyleSheet, 
  Dimensions, 
  ImageBackground, 
  Image, 
  View, 
  Text, 
  KeyboardAvoidingView, 
  Platform, 
  Keyboard, 
  TouchableOpacity, 
  TouchableWithoutFeedback 
} from "react-native";
import Layout from "../../constants/Layout";
import { green_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import InitialInput from "../../components/InitialInput";
import { post } from "../../networking/Server";
import { Spinner } from "native-base";
import Toast from "../../components/Toast";
import { MainStore } from "../../stores/MainStore";
import { SafeAreaView } from "react-native-safe-area-context";
import { ScrollView } from "react-native-gesture-handler";
import { BackIcon } from "../../components/Svgs";

const UpdatePassword: React.FC = (props: any) => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast]: any = useState(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");
    const [password, setPassword] = useState("");
    const [rePassword, setRePassword] = useState("");
    const [loading, setLoading] = useState(false);
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [keyboardOpen, setKeyboardOpen] = useState(false);

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => setKeyboardOpen(true));
        const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => setKeyboardOpen(false));
        return () => {
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => setStatusToast(false), 1500);
    };

    const handleUpdate = () => {
        if (password.length < 6) {
            startToast(MainStore.language.password_eight, "error");
        } else if (password !== rePassword) {
            startToast(MainStore.language.no_match_password, "error");
        } else {
            setLoading(true);
            post("users/reset-password", { 
                phoneNumber: props.route.params?.phone, 
                password 
            }).then((res: any) => {
                if (res.type === "error") {
                    startToast(res.error, "error");
                } else {
                    navigation.navigate("Login");
                }
            }).finally(() => setLoading(false));
        }
    };

    const isIOSWithNotch = () => {
        const { height } = Dimensions.get('window');
        return (
            Platform.OS === 'ios' && 
            !Platform.isPad && 
            (height >= 812 || Dimensions.get('screen').height >= 812)
        );
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground 
                source={require('../../assets/initial/pgif.gif')} 
                style={styles.gifBackground}
            >
                <View style={styles.toastView}>
                    <Toast 
                        type={typeToast} 
                        subtitle={subtitleToast} 
                        status={statusToast} 
                        successColor={green_t1} 
                    />
                </View>
                
                <KeyboardAvoidingView
                    behavior={Platform.OS === "ios" ? "padding" : "height"}
                    keyboardVerticalOffset={isIOSWithNotch() ? 10 : 0}
                    style={styles.flex}
                >
                    <SafeAreaView style={styles.container}>
                        <TouchableOpacity style={styles.goBackButton} onPress={() => navigation.goBack()}>
                                                    <BackIcon
                                                                            size={25}
                                                                            color={white}
                                                                        />
                                                </TouchableOpacity>
                        <ScrollView 
                            contentContainerStyle={styles.scrollContainer}
                            keyboardShouldPersistTaps="handled"
                        >
                            <Image 
                                source={require('../../assets/initial/hollyent.png')} 
                                style={styles.logo} 
                                resizeMode="contain" 
                            />
                            
                            {!keyboardOpen && (
                                <View style={styles.badge}>
                                    <Text style={styles.badgeText}>
                                        {MainStore.language.update_password_info}
                                    </Text>
                                </View>
                            )}

                            <View style={styles.inputGroup}>
                                <View style={styles.passwordContainer}>
                                    <InitialInput
                                        image={require('../../assets/initial/password.png')}
                                        onChangeText_={setPassword}
                                        keyboardType="default"
                                        value={password}
                                        placeHolder={MainStore.language.password}
                                        secureTextEntry={!passwordVisible}
                                    />
                                    <TouchableOpacity 
                                        onPress={() => setPasswordVisible(!passwordVisible)} 
                                        style={styles.eyeIcon}
                                    >
                                        <Image 
                                            source={require('../../assets/eye.png')} 
                                            style={styles.iconImage} 
                                        />
                                    </TouchableOpacity>
                                </View>

                                <View style={styles.inputSpacing} />

                                <View style={styles.passwordContainer}>
                                    <InitialInput
                                        image={require('../../assets/initial/password.png')}
                                        onChangeText_={setRePassword}
                                        keyboardType="default"
                                        value={rePassword}
                                        placeHolder={MainStore.language.repassword}
                                        secureTextEntry={!passwordVisible}
                                    />
                                </View>
                            </View>
                        </ScrollView>

                        <View style={styles.buttonContainer}>
                            {loading ? (
                                <View style={styles.spinnerView}>
                                    <Spinner color={white} size={18} />
                                </View>
                            ) : (
                                <SwipeButton 
                                    onToggle={handleUpdate} 
                                    buttonText={MainStore.language.update} 
                                />
                            )}
                        </View>
                    </SafeAreaView>
                </KeyboardAvoidingView>
            </ImageBackground>
        </TouchableWithoutFeedback>
    );
};

const styles = StyleSheet.create({
    flex: {
        flex: 1,
    },
    gifBackground: { 
        flex: 1, 
        justifyContent: 'center' 
    },
    toastView: { 
        position: 'absolute', 
        top: 0, 
        zIndex: 20 
    },
    goBackButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 30 : 20,
        left: -30,
        zIndex: 10,
        padding: 10
    },
    container: {
        flex: 1,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
        paddingTop: 20,
    },
    logo: { 
        height: 100, 
        width: Layout.screen.width / 2,
        marginBottom: 40,
        marginTop: 80,
    },
    badge: { 
        backgroundColor: 'rgba(255,255,255,0.2)', 
        padding: 20, 
        width: Layout.screen.width / 1.4, 
        borderRadius: 20, 
        marginBottom: 40 
    },
    badgeText: { 
        color: white, 
        fontSize: 16, 
        textAlign: 'center', 
        fontWeight: 'bold' 
    },
    inputGroup: {
        width: '90%',
        alignItems: 'center',
    },
    passwordContainer: {
        width: '100%',
        position: 'relative',
    },
    eyeIcon: { 
        position: 'absolute', 
        right: 5, 
        top: '50%', 
        transform: [{ translateY: -12 }], 
    },
    iconImage: { 
        width: 24, 
        height: 24 
    },
    inputSpacing: {
        height: 20,
    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        paddingBottom: 20,
    },
    spinnerView: { 
        alignItems: 'center', 
        paddingVertical: 20 
    },
});

export default UpdatePassword;
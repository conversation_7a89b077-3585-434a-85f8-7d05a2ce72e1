import React, { useRef, useState } from "react";
import { StyleSheet, View, Text, ImageBackground, Image, AppState, Platform, TouchableOpacity } from "react-native";
import WheelPicker from "../../components/WheelPicker";
import Layout from "../../constants/Layout";
import MapView from "react-native-maps";
import { MainStore } from "../../stores/MainStore";
import { blue_t1, blue_t2, blue_t3, blue_t4, green_t1, green_t2, red_t1, white } from "../../constants/Color";
import MapViewDirections from "react-native-maps-directions";
import GetLocation from 'react-native-get-location';
import { goPage } from "../../functions/goPage";
import { useNavigation } from "@react-navigation/native";
import { get, post } from "../../networking/Server";
import { Observer } from "mobx-react-lite";
import Toast from "../../components/Toast"; // Toast bileşeni

const ChooseCity: React.FC = () => {

    const [selectedItemIndex, setSelectedItemIndex] = React.useState(0);
    const [items, setItems] = React.useState<{ latitude: number, longitude: number, active: number }[]>([]); // active alanını ekledik

    // Toast state'leri
    const [statusToast, setStatusToast] = useState(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const navigation = useNavigation();

    const handleIndexChange = (index: any) => {
        setSelectedItemIndex(index);
        const selectedCity = items[index]; // Seçilen öğenin tam nesnesini al
        updateRoute(selectedCity);
    };
    
    const updateRoute = (selectedCity: { latitude: number, longitude: number, name: string }) => {
        if (myLocationDirect.latitude !== 0 && myLocationDirect.longitude !== 0) {
            setDirectionsProps({
                ...directionsProps,
                destination: {
                    latitude: selectedCity.latitude,
                    longitude: selectedCity.longitude
                }
            });

            setMyLocationDirect({
                latitude: myLocationDirect.latitude,
                longitude: myLocationDirect.longitude
            });
            MainStore.setCity(selectedCity); // MainStore'daki şehri güncelle
        }
    };

    const [directionsProps, setDirectionsProps] = React.useState({
        origin: {
            latitude: 0,
            longitude: 0
        },
        destination: {
            latitude: 0,
            longitude: 0
        }
    });

    // -- MY LOCATION FOR DIRECT -- ||
    const [myLocationDirect, setMyLocationDirect] = React.useState({
        latitude: 0,
        longitude: 0
    });

    // -- APP STATE STATUS -- // IS OTHER APPS OR PHONE HOME PAGE LK.
    const [appState, setAppState] = React.useState("active");


    // -- STATUS -- //
    const [status, setStatus] = React.useState(false);

    React.useEffect(() => {

        const listener = AppState.addEventListener("change", handleAppStateChange);

        getCities();

        GetLocation.getCurrentPosition({
            enableHighAccuracy: true,
            timeout: 60000,
        })
            .then(location => {
                setMyLocationDirect(location);
            })
            .catch(error => {
                const { code, message } = error;
            });

        return () => {
            listener.remove();
        }

    }, []);

    const getCities = () => {
        get("/vendors").then((res: any) => {
            setItems(res.vendors); // active alanını içerir
            MainStore.setCity(res.vendors[0]);
        });
    };

    const handleAppStateChange = (nextAppState: any) => {
        setAppState(nextAppState);
    };
    const mapRef = useRef<MapView>(null);


    const handleCitySelection = async () => {
        const selectedCity = items[selectedItemIndex];
    
        // Eğer `active` değeri 2 ise, toast mesajı göster ve geri dön
        if (selectedCity.active === 2) {
            startToast("Seçtiğiniz şehir aktif değil!", "error"); // Toast mesajı göster
            return;
        }
    
        try {
            // Şehir bilgilerini sunucuya gönder
            const response: any = await post("/users/update-city", { cityId: selectedCity.id });
    
            if (response.type === "success") {
                MainStore.setCity(selectedCity); // MainStore'daki şehri güncelle
                startToast("Şehir başarıyla güncellendi", "success");
                goPage(navigation, "Initial", {}, false); // Başka bir sayfaya yönlendir
            } else {
                startToast(response.error || "Bir hata oluştu", "error");
            }
        } catch (error) {
            console.error("City update error:", error);
            startToast("Bir şeyler ters gitti", "error");
        }
    };

    return (
        <ImageBackground
            resizeMode="cover"
            source={require('../../assets/initial/world.gif')}
            style={styles.container}
        >
            {/* Toast Bileşeni */}
            <View style={styles.toastView}>
                <Toast
                    type={typeToast}
                    subtitle={subtitleToast}
                    status={statusToast}
                    successColor={green_t1}
                />
            </View>
            
            <View style={{
                width: Layout.screen.width / 1.1,
                alignSelf: 'center',
                overflow: 'hidden',
                justifyContent: 'center',
                borderBottomRightRadius: 50,
                borderBottomLeftRadius: 50,
                height: Layout.screen.height / 2.5,
            }}>
                <Observer render={() => <MapView
                    region={{
                        latitude: Number(MainStore.city.latitude) || 0,
                        longitude: Number(MainStore.city.longitude) || 0,
                        latitudeDelta: 0.02,
                        longitudeDelta: 0.02,
                    }}
                    showsUserLocation={true}
                    showsMyLocationButton={true}
                    style={styles.map}
                    userInterfaceStyle={"dark"}
                    userLocationPriority="high"
                    userLocationUpdateInterval={5000}
                    userLocationFastestInterval={2000}
                    followsUserLocation={true}
                    focusable={true}
                    ref={mapRef}
                    userLocationCalloutEnabled={true}
                >
                    {
                        myLocationDirect?.latitude == 0 || myLocationDirect?.longitude == 0 ?
                            <></>
                            :
                            <Observer
                                render={() => <MapViewDirections
                                    origin={{
                                        latitude: myLocationDirect.latitude,
                                        longitude: myLocationDirect.longitude
                                    }}
                                    destination={{
                                        latitude: Number(MainStore.city.latitude),
                                        longitude: Number(MainStore.city.longitude)
                                    }}
                                    apikey={"AIzaSyAvB9qUgpKyXkEHobwbfCehGUm1NyIssl0"} // insert your API Key here
                                    strokeWidth={8}
                                    strokeColor={green_t2}
                                    mode="DRIVING"
                                    onReady={(result) => {
                                        // Harita rotaya odaklanacak
                                        mapRef.current?.fitToCoordinates(result.coordinates, {
                                            edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
                                            animated: true,
                                        });
                                    }}
                                />}
                            />
                    }
                </MapView>} />
            </View>
            <Image
                source={require('../../assets/initial/chooseCity.png')}
                style={{
                    width: 475,
                    height: Layout.screen.height / 1.5,
                    position: 'absolute',
                    left: -400,
                    bottom: 0
                }}
                resizeMode="contain"
            />
            <View style={{
                position: 'absolute',
                width: Layout.screen.width,
                bottom: Layout.screen.height > 880 ?
                    Layout.screen.height / 6
                    :
                    Layout.screen.height > 800 ?
                        Layout.screen.height / 6.7
                        :
                        Layout.screen.height / 6.5,
                left: 50,
            }}>

                <WheelPicker
                    items={items}
                    onIndexChange={handleIndexChange}
                    itemHeight={
                        Layout.screen.height > 880 ?
                            Layout.screen.height / 9
                            :
                            Layout.screen.height / 8.25
                    }
                />
    
            </View>

            <TouchableOpacity
                onPress={handleCitySelection} // Şehir seçimini kontrol eden işlev
                style={{
                    width: Layout.screen.width / 2,
                    height: 50,
                    backgroundColor: 'rgba(256,256,256,0.3)',
                    position: 'absolute',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bottom: 30,
                    alignSelf: 'center',
                    borderRadius: 100
                }}>
                <Text style={{
                    fontFamily: 'MADE TOMMY',
                    color: white,
                    fontSize: 20
                }}>Seç</Text>
            </TouchableOpacity>

        </ImageBackground>
    );
};
export default ChooseCity;

// -- STYLES -- //
const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
        height: '100%',
        backgroundColor: 'black',
    },
    map: {
        flex: 1,
        height: '100%',
        width: '100%',
        alignSelf: 'center',
        borderRadius: 10,
    },
    selectedItemText: {
        color: 'white',
        fontSize: 20,
    },
    buttonBackgroundView: {
        height: 50,
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        bottom: 40,
        position: 'absolute',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    spinnerView: {
        alignItems: 'center',
        width: Layout.screen.width / 1.2
    },
    buttonLeftSwipeView: {
        height: 48,
        zIndex: 0,
        position: 'absolute',
        width: 143,
        marginLeft: 2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    buttonLeftText: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold'
    },
    buttonSwipeView: {
        alignItems: 'center',
        width: 143,
        zIndex: 3
    },
    buttonRightIcon: {
        height: 25,
        width: 40,
        marginRight: 20
    },
});

import React, { useState, useEffect, useRef } from "react";
import { 
  StyleSheet, 
  KeyboardAvoidingView, 
  Platform, 
  Dimensions, 
  ImageBackground, 
  Image, 
  View, 
  Text, 
  SafeAreaView, 
  TouchableWithoutFeedback, 
  Keyboard, 
  TouchableOpacity,
  StatusBar,
  Animated
} from "react-native";
import Layout from "../../constants/Layout";
import InitialInput from "../../components/InitialInput";
import { green_t1, red_t1, white } from "../../constants/Color";
import SwipeButton from "../../components/SwipeButton";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import { screens } from "../../navigation";
import { post } from "../../networking/Server";
import { MainStore } from "../../stores/MainStore";
import { Spinner } from "native-base";
import Toast from "../../components/Toast";
import { BackIcon } from "../../components/Svgs";

const Login: React.FC = () => {
    const navigation = useNavigation<screens>();
    const [statusToast, setStatusToast] = useState<boolean | null>(null);
    const [typeToast, setTypeToast] = useState("");
    const [subtitleToast, setSubTitleToast] = useState("");
    const [loading, setLoading] = useState(false);
    const [phone, setPhone] = useState("");
    const [password, setPassword] = useState("");
    const [modal, setModal] = useState(false);
    const [countryCode, setCountryCode] = useState("90");
    
    // Animasyon değeri
    const animatedPadding = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        // Klavye açıldığında
        const keyboardWillShowSub = Keyboard.addListener(
            Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
            event => {
                // Klavye yüksekliğine göre animasyon
                Animated.timing(animatedPadding, {
                    toValue: event.endCoordinates.height - (Platform.OS === "ios" ? 0 : 0),
                    duration: Platform.OS === "ios" ? 0 : 0,
                    useNativeDriver: false
                }).start();
            }
        );

        // Klavye kapandığında
        const keyboardWillHideSub = Keyboard.addListener(
            Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
            () => {
                // Padding'i sıfırla
                Animated.timing(animatedPadding, {
                    toValue: 0,
                    duration: Platform.OS === "ios" ? 0 : 0,
                    useNativeDriver: false
                }).start();
            }
        );

        return () => {
            keyboardWillShowSub.remove();
            keyboardWillHideSub.remove();
        };
    }, []);

    // Sayfa her odaklandığında yenileme yap
    useFocusEffect(
        React.useCallback(() => {
            return () => {};
        }, [])
    );

    const startToast = (message: string, type: string) => {
        setStatusToast(true);
        setSubTitleToast(message);
        setTypeToast(type);
        setTimeout(() => {
            setStatusToast(false);
        }, 1500);
    };

    const handleLogin = () => {
        setLoading(true);
        if (!phone || !password) {
            startToast(MainStore.language.no_empty, "error");
            setLoading(false);
            return;
        }
        try {
            post("users/login", {
                phoneNumber: "+" + countryCode + phone,
                password,
            }).then((res: any) => {
                if (res.type === "error") {
                    startToast(res.error, "error");
                } else {
                    startToast(res.message, "success");
                    MainStore.setToken(res.token);
                    setTimeout(() => {
                        navigation.navigate("ChooseCity");
                    }, 2500);
                }
            }).finally(() => {
                setLoading(false);
            });
        } catch (e) {
            startToast(MainStore.language.went_wrong, "error");
            setLoading(false);
        }
    };

    const isIOSWithNotch = () => {
        const { height } = Dimensions.get('window');
        return (
            Platform.OS === 'ios' && 
            !Platform.isPad && 
            (height >= 812 || Dimensions.get('screen').height >= 812)
        );
    };

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
            <ImageBackground 
                source={require('../../assets/initial/pgif.gif')} 
                style={styles.gifBackground}
                resizeMode="cover"
            >
                <View style={styles.toastView}>
                    <Toast 
                        type={typeToast} 
                        subtitle={subtitleToast} 
                        status={statusToast} 
                        successColor={green_t1} 
                    />
                </View>
                
                <SafeAreaView style={styles.container}>
                    {/* Geri Dönüş Butonu - En Üstte Sabit */}
                    <View style={styles.headerContainer}>
                        <TouchableOpacity 
                            style={styles.goBackButton} 
                            onPress={() => navigation.goBack()}
                        >
                            <BackIcon size={25} color={white} />
                        </TouchableOpacity>
                    </View>

                    {/* Ana İçerik - Klavye Durumuna Göre Ayarlanır */}
                    <View style={styles.mainContentContainer}>
                        {/* Logo Bölümü */}
                        <View style={styles.logoContainer}>
                            <Image 
                                source={require('../../assets/initial/hollyent.png')} 
                                style={styles.logo} 
                                resizeMode="contain" 
                            />
                        </View>
                        
                        {/* Input Alanları */}
                        <View style={styles.inputsContainer}>
                            <InitialInput 
                                countryCode={countryCode} 
                                setModal={setModal}
                                onChangeText_={setPhone} 
                                image={require('../../assets/initial/phone.png')} 
                                value={phone}
                                keyboardType="phone-pad" 
                                placeHolder={MainStore.language.phone} 
                            />

                            <View style={styles.inputSpacing} />
                            
                            <InitialInput
                                secureTextEntry={true}
                                image={require('../../assets/initial/password.png')}
                                onChangeText_={setPassword}
                                keyboardType="default"
                                value={password}
                                placeHolder={MainStore.language.password}
                            />
                            
                            <TouchableOpacity 
                                onPress={() => navigation.navigate("ForgetPassword")} 
                                style={styles.forgetPasswordTouch}
                            >
                                <Text style={styles.forgetPassword}>
                                    {MainStore.language.password_forgot}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* Boş Alan (Esnek) */}
                        <View style={styles.flexSpace} />
                    </View>

                    {/* Buton Konteyner - Her Zaman Görünür */}
                    <Animated.View style={[
                        styles.buttonOuterContainer,
                        
                    ]}>
                        <View style={styles.buttonContainer}>
                            {loading ? (
                                <Spinner color={white} size={18} />
                            ) : (
                                <SwipeButton 
                                    onToggle={handleLogin} 
                                    buttonText="Giriş Yap" 
                                />
                            )}
                        </View>
                    </Animated.View>
                </SafeAreaView>
            </ImageBackground>
        </TouchableWithoutFeedback>
    );
};

export default Login;

const styles = StyleSheet.create({
    gifBackground: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    toastView: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 20,
        alignItems: 'center',
    },
    container: {
        flex: 1,
        width: '100%',
    },
    headerContainer: {
        width: '100%',
        paddingHorizontal: 20,
        paddingTop: Platform.OS === 'ios' ? 10 : 20,
        zIndex: 10,
    },
    goBackButton: {
        padding: 8,
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    mainContentContainer: {
        flex: 1,
        paddingHorizontal: 20,
        flexDirection: 'column',
    },
    logoContainer: {
        width: '100%',
        alignItems: 'center',
        marginTop: 20,
        marginBottom: 40,
    },
    logo: {
        width: Layout.screen.width / 2,
        height: 100,
    },
    inputsContainer: {
        width: '100%',
        alignItems: 'center',
    },
    inputSpacing: {
        height: 15,
    },
    forgetPasswordTouch: {
        paddingVertical: 10,
        alignSelf: 'flex-end',
        marginRight: 20,
        marginTop: 5,
    },
    forgetPassword: {
        fontFamily: 'MADE TOMMY',
        color: white,
        fontSize: 12,
    },
    flexSpace: {
        flex: 1,
    },
    buttonOuterContainer: {
        width: '100%',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
    },
    buttonContainer: {
        width: '100%',
        alignItems: 'center',
        paddingVertical: 20,
        paddingHorizontal: 20,
    },
});
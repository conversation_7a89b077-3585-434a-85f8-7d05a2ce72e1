// STORE CHANGES (HollyChatStore.ts)

// Add a method to handle new conversations
@action
addNewConversation(conversation) {
  // Check if this conversation already exists
  const existingIndex = this.conversations.findIndex(
    conv => conv._id === conversation._id
  );
  
  if (existingIndex >= 0) {
    // Update existing conversation
    this.conversations[existingIndex] = {
      ...this.conversations[existingIndex],
      messages: conversation.messages,
      updatedAt: conversation.updatedAt
    };
  } else {
    // Add new conversation
    this.conversations.push(conversation);
    
    // Sort conversations by updatedAt (newest first)
    this.conversations.sort((a, b) => {
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });
  }
}

// In the constructor or initialization method, add a listener for new conversations
setupSocketListeners() {
  if (!this.socket) return;
  
  this.socket.on('new conversation', (conversation) => {
    this.addNewConversation(conversation);
  });
  
  // Also listen for message received to update conversation list
  this.socket.on('message received', (message) => {
    // Find the conversation this message belongs to
    const conversationIndex = this.conversations.findIndex(
      conv => conv._id === message.chatRoomId
    );
    
    if (conversationIndex >= 0) {
      // Update the conversation's messages and updatedAt
      const updatedConversation = {
        ...this.conversations[conversationIndex],
        messages: [message, ...this.conversations[conversationIndex].messages],
        updatedAt: message.createdAt
      };
      
      // Remove the old conversation
      this.conversations.splice(conversationIndex, 1);
      
      // Add the updated conversation (will be sorted to the top)
      this.addNewConversation(updatedConversation);
    }
  });
}

// Backend tarafında yapılması gereken değişiklikler (app.js dosyasında)

// 1. createChatRoomIfNotExists fonksiyonunu güncelleyelim
async function createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId) {
  // Front-end chatRoomId gönderiyorsa DB'de var kabul ediyoruz
  if (chatRoomId) {
    return chatRoomId; 
  }
  try {
    // Var mı diye kontrol
    const [rows] = await pool.query(
      `SELECT id FROM holly_chat_rooms
       WHERE (senderId=? AND receiverId=?) OR (senderId=? AND receiverId=?)`,
      [senderId, receiverId, receiverId, senderId]
    );
    if (rows.length > 0) {
      return rows[0].id;
    }

    // Yoksa oluştur
    const [insertResult] = await pool.query(
      `INSERT INTO holly_chat_rooms (senderId, receiverId, createdAt, updatedAt)
       VALUES (?, ?, NOW(), NOW())`,
      [senderId, receiverId]
    );
    
    const newRoomId = insertResult.insertId;
    
    // Yeni oda oluşturulduğunda, göndericiyi otomatik olarak odaya kat
    // Bu kısım önemli: Gönderici odaya katılmazsa, kendi mesajını göremez
    const senderSocketId = connectedUsers[senderId];
    if (senderSocketId) {
      const senderSocket = io.sockets.sockets.get(senderSocketId);
      if (senderSocket) {
        senderSocket.join(newRoomId);
        console.log(`Sender ${senderId} joined new room ${newRoomId}`);
      }
    }
    
    // Alıcı bağlı ise, onu da odaya kat
    const receiverSocketId = connectedUsers[receiverId];
    if (receiverSocketId) {
      const receiverSocket = io.sockets.sockets.get(receiverSocketId);
      if (receiverSocket) {
        receiverSocket.join(newRoomId);
        console.log(`Receiver ${receiverId} joined new room ${newRoomId}`);
      }
    }
    
    return newRoomId;
  } catch (err) {
    console.error('Error creating chat room:', err);
    throw err;
  }
}

// 2. Yeni mesaj event handler'ını güncelleyelim
socket.on('new message', async (newMessageReceived) => {
  try {
    const { senderId, receiverId, chatRoomId, text, messageStatus = MESSAGE_STATUS.SENT } = newMessageReceived;

    // 1) Uygunsuz kelime filtresi
    const containsInappropriate = inappropriateWords.some((word) =>
      text.toLowerCase().includes(word)
    );
    if (containsInappropriate) {
      return socket.emit('message error', 'Mesaj uygunsuz kelimeler içeriyor!');
    }

    console.log('Processing message:', senderId, receiverId, chatRoomId, text, messageStatus);

    // 2) Yoksa chatRoom oluştur
    const roomId = await createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId);
    
    // 3) Göndericiyi odaya kat (eğer zaten katılmamışsa)
    socket.join(roomId);
    console.log(`Socket ${socket.id} joined room ${roomId}`);

    // 4) Mesajı veritabanına ekle
    const messageId = await sendMessageToDB(
      pool, 
      senderId, 
      receiverId, 
      roomId, // Oluşturulan veya var olan roomId'yi kullan
      text, 
      messageStatus
    );

    const now = new Date();
    const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000));

    // 5) GiftedChat'e uygun payload
    const payload = {
      _id: messageId,
      text: text,
      createdAt: turkeyTime.toISOString(),
      user: {
        _id: senderId,
      },
      chatRoomId: roomId, // Oluşturulan veya var olan roomId'yi kullan
      isDeleted: false,
      messageStatus: messageStatus,
    };

    // 6) Alıcı bağlı değilse bildirim gönder
    if (!connectedUsers[receiverId]) {
      const { receiverDetails, senderDetails } = await fetchNotificationDetails(receiverId, senderId);
      if (receiverDetails && receiverDetails.player_id) {
        const senderFullName = senderDetails 
          ? `${senderDetails.firstName} ${senderDetails.lastName}` 
          : 'Gönderen Adı';
        await sendNotification(receiverDetails.player_id, senderFullName, text);
      }
    }

    // 7) Bu mesajı odaya yayınla
    io.to(roomId).emit('message received', payload);
    console.log(`Message sent to room ${roomId}`);

  } catch (err) {
    console.error('Error in new message:', err);
    socket.emit('message error', 'Internal Server Error');
  }
});

import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { black_t3, gray_t7, green_t1, white } from "../constants/Color";
import Layout from "../constants/Layout";

export type SettingsBackground = {
    title: string,
    children: any,
    onPress: any,
    buttonTitle: string
}

const SettingsBackground: React.FC<SettingsBackground> = ({
    title,
    children,
    onPress,
    buttonTitle
}) => {
    return (
        <View style={styles.main}>

            {/* PAGE TITLE */}
            <Text style={styles.title}>{title}</Text>
            {/* CHILD */}
            {
                children
            }

            {/* BUTTON */}
            <TouchableOpacity
                style={styles.button}
                onPress={onPress}
            >
                <Text style={styles.buttonTitle}>{buttonTitle}</Text>
            </TouchableOpacity>
        </View>
    )
}

export default SettingsBackground;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        backgroundColor: 'rgba(255,255,255,0.9)',
        width: Layout.screen.width / 1.1,
        height: Layout.screen.height / 1.3,
        alignSelf: 'center',
        borderRadius: 10,
        marginTop: Layout.screen.height / 10
    },
    title: {
        alignSelf: 'center',
        fontSize: 16,
        letterSpacing: 1.2,
        fontWeight: 'bold',
        marginVertical: 20,
        color: black_t3
    },
    button: {
        backgroundColor: green_t1,
        width: 180,
        borderRadius: 15,
        position: 'absolute',
        bottom: -20,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        height: 45
    },
    buttonTitle: {
        fontSize: 18,
        color: white,
        fontFamily: 'MADE TOMMY'
    },
});
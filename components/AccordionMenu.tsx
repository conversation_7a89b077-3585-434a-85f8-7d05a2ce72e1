// AccordionItem.js
import React, { useEffect, useState } from 'react';
import { View, Text, Dimensions, Image } from 'react-native';
import {
  PanGestureHandler,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  useAnimatedGestureHandler,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

const AccordionItem = ({ title, content, imageSource, imageWidth, imageHeight, kavisrengi, color, width, handleToggle, itemsLength, widths, index }) => {
  const expandedWidth = Dimensions.get('window').width - itemsLength * 40;

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx) => {
      ctx.startWidth = width.value;
    },
    onActive: (event, ctx) => {
      // Sadece kapalı olan öğelerin genişliğini değiştir
      if (width.value < expandedWidth && width.value > 40) {
        const newWidth = ctx.startWidth + event.translationX;
        if (newWidth <= expandedWidth && newWidth >= 40) {
          width.value = newWidth;
          if (index < itemsLength - 1 && widths[index + 1].value > 40) {
            widths[index + 1].value = Math.max(expandedWidth - newWidth, 40);
          }
        }
      }
    },
    onEnd: (_) => {
      if (width.value > 0) {
        runOnJS(handleToggle)();
      } else {
        width.value = withSpring(40);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    const translateY = interpolate(width.value, [40, expandedWidth], [50, -100]);
    return {
      width: width.value,
      backgroundColor: color,
    };
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View
        style={[
          animatedStyle,
          {
            height: 200,
            margin: 0,
          },
        ]}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <View>
            <Text>{content}</Text>
          </View>
          <Image
            source={imageSource}
            style={{ width: imageWidth, height: imageHeight, resizeMode: 'cover', top: 10, right: 10 }}
          />
          <View style={{ 
            borderTopLeftRadius: 100,
            width: 10, 
            height: 80, 
            backgroundColor: kavisrengi, 
            position: 'absolute', 
            right: 0, 
            top: 120 
          }}>
          </View>
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};



const AccordionMenu = ({ items }) => {
  const expandedWidth = Dimensions.get('window').width - items.length * 40;
  const [widths, setWidths] = useState(items.map((_, i) => useSharedValue(i === 4 ? expandedWidth : 40))); //

  const handleToggle = (index) => {
    widths.forEach((width, i) => {
      if (i === index) {
        width.value = withSpring(expandedWidth);
      } else {
        width.value = withSpring(40);
      }
    });
  };

  return (
    <View style={{ flexDirection: 'row', backgroundColor: 'transparent' }}>
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          title={item.title}
          content={item.content}
          color={item.color}
          imageWidth={item.imageWidth}
          imageHeight={item.imageHeight}
          kavisrengi={item.kavisrengi}
          imageSource={item.imageSource}
          width={widths[index]}
          handleToggle={() => handleToggle(index)}
          itemsLength={items.length}
          widths={widths}
          index={index}
        />
      ))}
    </View>
  );
};

export default AccordionMenu;

import React, { useState, useEffect, useRef } from "react";
import { 
  Modal, 
  View, 
  Image, 
  TouchableOpacity, 
  Text, 
  Animated, 
  Dimensions, 
  StyleSheet 
} from "react-native";

const { width, height } = Dimensions.get("window");

interface Story {
  image: string;
}

interface StoryViewerProps {
  stories: Story[];
  isVisible: boolean;
  onClose: () => void;
}

const StoryViewer: React.FC<StoryViewerProps> = ({ stories, isVisible, onClose }) => {
  const [currentStory, setCurrentStory] = useState(0);
  const progress = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      startProgress();
    }
  }, [currentStory, isVisible]);

  const startProgress = () => {
    progress.setValue(0);
    Animated.timing(progress, {
      toValue: width,
      duration: 5000, // 5 saniye
      useNativeDriver: false,
    }).start(({ finished }) => {
      if (finished) {
        goToNextStory();
      }
    });
  };

  const goToNextStory = () => {
    if (currentStory < stories.length - 1) {
      setCurrentStory(currentStory + 1);
    } else {
      onClose();
    }
  };

  const goToPreviousStory = () => {
    if (currentStory > 0) {
      setCurrentStory(currentStory - 1);
    }
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent>
      <View style={styles.container}>
        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          {stories.map((_, index) => (
            <View key={index} style={styles.progressBarBackground}>
              <Animated.View
                style={[
                  styles.progressBarFill,
                  {
                    width: index === currentStory ? progress : index < currentStory ? width : 0,
                  },
                ]}
              />
            </View>
          ))}
        </View>

        {/* Story Image */}
        <TouchableOpacity style={styles.leftTouchable} onPress={goToPreviousStory} />
        <Image source={{ uri: stories[currentStory]?.image || "" }} style={styles.storyImage} />
        <TouchableOpacity style={styles.rightTouchable} onPress={goToNextStory} />

        {/* Close Button */}
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeText}>X</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "black",
    justifyContent: "center",
    alignItems: "center",
  },
  progressBarContainer: {
    flexDirection: "row",
    position: "absolute",
    top: 50,
    width: "90%",
    alignSelf: "center",
    justifyContent: "space-between",
  },
  progressBarBackground: {
    flex: 1,
    height: 3,
    backgroundColor: "gray",
    marginHorizontal: 2,
  },
  progressBarFill: {
    height: 3,
    backgroundColor: "white",
  },
  leftTouchable: {
    position: "absolute",
    width: "50%",
    height: "100%",
    left: 0,
  },
  rightTouchable: {
    position: "absolute",
    width: "50%",
    height: "100%",
    right: 0,
  },
  storyImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  closeButton: {
    position: "absolute",
    top: 40,
    right: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 10,
    borderRadius: 20,
  },
  closeText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default StoryViewer;

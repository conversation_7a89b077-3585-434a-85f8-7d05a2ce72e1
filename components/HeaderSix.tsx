import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { black, red_t1 } from "../constants/Color";
import Layout from "../constants/Layout";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { BackIcon } from "./Svgs";

export type HeaderSix = {
    navigation: any,
    headerStatus: boolean
}

const HeaderSix: React.FC<HeaderSix> = ({
    navigation,
    headerStatus = null
}) => {

    const insets = useSafeAreaInsets();

    if (headerStatus == null)
        return (
            <View
                style={styles.main}
            >
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={styles.leftTouch}
                >
                    <BackIcon
                        size={25}
                        color={black}
                    />
                </TouchableOpacity>
                <Image
                        source={require('../assets/holly-green.png')}
                        style={styles.logo}
                />
            </View>
        )

    return (
        <SafeAreaView style={{
            zIndex: 10,
            position: 'absolute',
            alignSelf: 'center',
            width: Layout.screen.width,
            height: Math.max(insets.bottom, 0) ? 130 : 65,
            backgroundColor: headerStatus ? 'rgba(256,256,256,0.5)' : 'transparent'
        }}>
            <View
                style={styles.main}
            >
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={styles.leftTouch}>
                    <BackIcon
                        size={25}
                        color={black}
                    />
                </TouchableOpacity>
                <Image
                        source={require('../assets/root/hollygarden.png')}
                        style={styles.logo}
                />
            </View>
        </SafeAreaView>
    )
}

export default HeaderSix;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 65
    },
    leftTouch: {
        position: 'absolute',
        paddingLeft: 10,
        padding: 15,
        left: 0
    },
    logo: {
        width: 150,
        resizeMode: 'contain',
    },
});

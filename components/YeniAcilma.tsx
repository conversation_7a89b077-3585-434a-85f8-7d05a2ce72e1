import React, { useState, useLayoutEffect, useRef } from 'react';
import {
  View,
  Text,
  Image,
  PanResponder,
  Dimensions,
  ImageSourcePropType,
  StyleProp,
  ViewStyle,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import Svg, { Path } from 'react-native-svg';

// Android için LayoutAnimation etkinleştirme
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface YeniAcilmaItemProps {
  imageSource?: ImageSourcePropType;
  color: string;
  soltext?: string;
  soltextColor?: string;
  soltextfontsize?: number;
  kavisrengi?: string;
  content?: React.ReactNode;
  isOpen: boolean;
  imageWidth?: number;
  imageHeight?: number;
  closedWidth: number;
  openWidth: number;
  index: number;
  handleDragRelease: (index: number) => void;
  borderTopLeftRadius?: number;
  borderBottomLeftRadius?: number;
  borderTopRightRadius?: number;
  borderBottomRightRadius?: number;
}

const YeniAcilmaItem: React.FC<YeniAcilmaItemProps> = ({
  imageSource,
  color,
  soltext,
  soltextColor,
  soltextfontsize,
  kavisrengi,
  content,
  isOpen,
  imageWidth,
  imageHeight,
  closedWidth,
  openWidth,
  index,
  handleDragRelease,
  borderTopLeftRadius,
  borderBottomLeftRadius,
  borderTopRightRadius,
  borderBottomRightRadius,
}) => {
  // PanResponder sadece sürüklenmeyi algılama ve
  // handleDragRelease fonksiyonunu tetikleme amacıyla kullanılıyor
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => Math.abs(gestureState.dx) > 15,
      onPanResponderGrant: () => {
        // Bir item seçildiğinde animasyon hazırlığı
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        handleDragRelease(index);
      },
      // İsterseniz onPanResponderMove veya onPanResponderRelease vs. de ekleyebilirsiniz
    })
  ).current;

  // isOpen true ise genişliği openWidth, değilse closedWidth olarak ayarla
  const containerStyle: StyleProp<ViewStyle> = {
    width: isOpen ? openWidth : closedWidth,
    overflow: 'hidden',
    height: 200,
    backgroundColor: color,
    borderTopLeftRadius,
    borderBottomLeftRadius,
    borderTopRightRadius,
    borderBottomRightRadius,
  };

  return (
      <View style={containerStyle} {...panResponder.panHandlers}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          {/* İçerik alanı */}
          <View style={{ justifyContent: 'center', alignItems: 'center', height: 200 }}>
            {isOpen && <Text>{content}</Text>}
          </View>

          {/* Sağ üstteki image veya sol dikey metin */}
          {imageSource ? (
            <Image
              source={imageSource}
              style={{
                width: imageWidth,
                height: imageHeight,
                resizeMode: 'contain',
                top: 5,
                right: 12,
                position: 'absolute',
              }}
            />
          ) : (
            <View style={{ justifyContent: 'center', alignItems: 'center', width: 30, height: 200 }}>
              <Text
                style={{
                  top: 40,
                  width: 100,
                  fontSize: soltextfontsize,
                  transform: [{ rotate: '270deg' }],
                  color: soltextColor,
                  fontFamily: 'MADE TOMMY',
                  position: 'absolute',
                }}
              >
                {soltext}
              </Text>
            </View>
          )}

          {/* Kavis (SVG) */}
          <View
            style={{
              position: 'absolute',
              right: -27,
              top: 90,
              zIndex: 2, 
              transform: [
                { scaleY: -1 }, 
                { scaleX: -1 }, 
                { translateX: 25 },
              ],
            }}
          >
            <Svg width={15} height={110} viewBox="0 0 13.63 127.46" preserveAspectRatio="xMinYMin meet">
              <Path
                fill={kavisrengi || '#00731c'} // Varsayılan renk
                d="M0,0h13.63v87.24c0,4.43-1.46,8.74-4.15,12.26h0C3.33,107.52,0,117.35,0,127.46"
              />
            </Svg>
          </View>
        </View>
    </View>
  );
};

interface YeniAcilmaProps {
  items: Array<{
    imageSource?: ImageSourcePropType;
    color: string;
    soltext?: string;
    soltextColor?: string;
    kavisrengi?: string;
    soltextfontsize?: number;
    content?: React.ReactNode;
    imageWidth?: number;
    imageHeight?: number;
    borderTopLeftRadius?: number;
    borderBottomLeftRadius?: number;
    borderTopRightRadius?: number;
    borderBottomRightRadius?: number;
  }>;
}

const YeniAcilma: React.FC<YeniAcilmaProps> = ({ items }) => {
  const { width } = Dimensions.get('window');
  const closedWidth = 40;   // Kapalı durumdaki genişlik
  const screenPadding = 36; // Sağ-sol kenarlardan bırakacağınız pay

  // Tüm item’lar kapalı haldeyken toplam kapalı genişlik ve geriye kalan
  // boşluğu kullanarak açık item genişliğini hesaplıyoruz
  const calculateOpenWidth = () => {
    // (items.length - 1) adet item kapalı kalacağı için
    const totalClosedWidth = closedWidth * (items.length - 1);
    return Math.ceil(width - screenPadding - totalClosedWidth);
  };

  const [openIndex, setOpenIndex] = useState<number | null>(items.length - 1);
  const [openWidth, setOpenWidth] = useState(calculateOpenWidth());

  useLayoutEffect(() => {
    setOpenWidth(calculateOpenWidth());
    // Eleman sayısı değiştiğinde yumuşak geçiş
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [items.length]);

  // Bir item seçilince (kaydırma başlatılınca) openIndex güncellenir
  const handleDragRelease = (index: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    // Aynı item’e tekrar tıklanırsa kapatmak isterseniz:
    // setOpenIndex(prev => (prev === index ? null : index));
    // Sadece tek item açık kalsın istiyorsanız:
    setOpenIndex(index);
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        height: 200,
        margin: 0,
    padding: 0,
      }}
    >
      {items.map((item, index) => (
        <YeniAcilmaItem
          key={index}
          {...item}
          isOpen={openIndex === index}
          closedWidth={closedWidth}
          openWidth={openWidth}
          index={index}
          handleDragRelease={handleDragRelease}
        />
      ))}
    </View>
  );
};

export default YeniAcilma;

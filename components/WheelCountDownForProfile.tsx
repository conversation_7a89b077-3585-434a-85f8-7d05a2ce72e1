import React, { useState, useEffect } from "react";
import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';
import { black_t3, green_t1, red_t6, white } from "../constants/Color";
import { get } from "../networking/Server";

const WheelCountDown: React.FC = () => {
    const [day, setDay] = useState(0);
    const [hour, setHour] = useState(0);
    const [minute, setMinute] = useState(0);
    const [second, setSecond] = useState(0);
    const [wheeltext, setWheeltext] = useState<string | null>(null);

    const getWheelInfo = () => {
        get("wheel").then((res: any) => {
            if (res.type === "success") {
                setWheeltext('Zaman Geldi!');
            } else {
                const now = new Date().getTime();
                const wheelTime = new Date(res.date).getTime();

                let fMil = Math.abs(wheelTime - now);

                const daywheel = Math.floor(fMil / (1000 * 60 * 60 * 24));
                fMil %= (1000 * 60 * 60 * 24);

                var hourwheel = Math.floor(fMil / (1000 * 60 * 60));
                fMil %= (1000 * 60 * 60);

                var minutewheel = Math.floor(fMil / (1000 * 60));
                fMil %= (1000 * 60);

                var secondwheel = Math.floor(fMil / 1000);

                setDay(daywheel);
                setHour(hourwheel);
                setMinute(minutewheel);
                setSecond(secondwheel);
            }
        });
    };

    useEffect(() => {
        getWheelInfo();
        // Her 1 saniyede bir güncelle
        const interval = setInterval(getWheelInfo, 1000);
        return () => clearInterval(interval);
    }, []);

    if (wheeltext) {
        return (
            <ImageBackground
                source={require('../assets/root/wheelCountBckzaman.png')}
                style={styles.main}
            >
                <View style={styles.mainAlt}>
                    <Text style={styles.timeReadyText}>{wheeltext}</Text>
                </View>
            </ImageBackground>
        );
    }

    return (
        <ImageBackground
            source={require('../assets/root/wheelCountBck.png')}
            style={styles.main}
        >
            <View style={styles.mainAlt}>
                <Text style={styles.titleTime}>KALAN SÜRE</Text>
                <View style={styles.countDownView}>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={day * 24 * 60 * 60}
                        duration={day * 24 * 60 * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[day * 24 * 60 * 60, (day * 24 * 60 * 60) / 2]}
                        size={31}
                        strokeWidth={3}
                    >
                        {() => <Text style={styles.countDownText}>{day}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={hour * 60 * 60}
                        duration={hour * 60 * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[7, 3.5]}
                        size={31}
                        strokeWidth={3}
                    >
                        {() => <Text style={styles.countDownText}>{hour}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={minute * 60}
                        duration={minute * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[7, 3.5]}
                        size={31}
                        strokeWidth={3}
                        onComplete={() => ({ shouldRepeat: true, delay: 0 })}
                    >
                        {() => <Text style={styles.countDownText}>{minute}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={second}
                        duration={60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[25, 0]}
                        size={31}
                        strokeWidth={3}
                        onComplete={() => ({ shouldRepeat: true, delay: 0 })}
                    >
                        {() => <Text style={styles.countDownText}>{second}</Text>}
                    </CountdownCircleTimer>
                </View>
            </View>
        </ImageBackground>
    );
};

const styles = StyleSheet.create({
    main: {
        width: 300,
        height: 122
    },
    mainAlt: {
        alignSelf: 'flex-end',
        width: 150,
        marginRight: 21
    },
    titleTime: {
        marginTop: 47,
        fontSize: 11,
        fontFamily: 'MADE TOMMY',
        color: black_t3
    },
    timeReadyText: {
        marginTop: 78,
        fontSize: 15,
        fontFamily: 'MADE TOMMY',
        color: black_t3,
        textAlign: 'center'
    },
    countDownView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 10
    },
    countDownText: {
        fontSize: 10,
        color: white
    },
});

export default WheelCountDown;
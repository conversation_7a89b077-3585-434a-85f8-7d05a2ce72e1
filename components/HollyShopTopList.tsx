import React from "react";
import { ScrollView, StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { brown_t2, gray_t14 } from "../constants/Color";

export type HollyShopTopList = {
    categories: any
}

const HollyShopTopList: React.FC<HollyShopTopList> = ({
    categories = []
}) => {
    return (
        <View style={styles.main}>
            <View style={styles.mainAlt}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                >
                    {
                        categories?.map((item: any, index: React.Key) => {
                            return (
                                <TouchableOpacity
                                    style={styles.scrollTouch}
                                    key={index}
                                >
                                    <View
                                        style={styles.backgroundBrown}
                                    >
                                        <Text style={styles.scrollTitle}>{item?.name}</Text>
                                    </View>
                                </TouchableOpacity>
                            )
                        })
                    }

                </ScrollView>
            </View>
        </View>
    )
}
export default HollyShopTopList;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        height: 70,
        marginTop: 80
    },
    mainAlt: {
        marginHorizontal: 20,
        marginTop: 10
    },
    scrollTouch: {
        alignItems: 'center',
        marginRight: 15
    },
    backgroundBrown: {
        height: 18,
        borderWidth: 1,
        backgroundColor: gray_t14,
        borderColor: brown_t2,
        paddingHorizontal:10,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    scrollTitle: {
        fontSize: 9,
        color: brown_t2,
        fontFamily: 'MADE TOMMY'
    }
});
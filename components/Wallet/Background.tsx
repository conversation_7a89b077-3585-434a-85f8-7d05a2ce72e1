import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import { BackIcon } from "../../components/Svgs";
import { white } from "../../constants/Color";
import MoneyText from "./MoneyText";
import { useNavigation } from "@react-navigation/native";


const Background = ({ amount }: { amount: string }) => {

  const navigation = useNavigation();

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.background}>
        <View style={styles.leftTouch} >
            <TouchableOpacity onPress={goBack} style={styles.touch}>
                <BackIcon size={25} color={white} />
            </TouchableOpacity>
            <MoneyText amount={amount} />
        </View>
    </View>

  );
};

const styles = StyleSheet.create({
  background: {
    backgroundColor: "#dc1354",
    height: 300,
    top: 0,
    width: "100%",
    alignItems: 'center',
  },
  leftTouch: {
    width: "100%",
  },
  touch: {
    top: 10,
    padding: 20,
    width: 70,
  }
});

export default Background;

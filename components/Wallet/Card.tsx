import { View, Text, TouchableOpacity,StyleSheet, Image, Modal } from 'react-native';
import { useNavigation } from "@react-navigation/native";
import { screens } from "../../navigation";
import { black, green_t1, white } from '../../constants/Color';
import React, { useState } from 'react';


const Card = () => {
  
    // -- NAVIGATION -- //
    const navigation = useNavigation<screens>();
    const navigateToPayMoney = () => {
        navigation.navigate('PayMoney'); 
    };

    const navigateToTransctions = () => {
        navigation.navigate('TransactionHistory'); 
    };

    const [payMoneyModalVisible, setPayMoneyModalVisible] = useState(false);

    const openPayMoneyModal = () => {
        setPayMoneyModalVisible(true);
    };

    const closePayMoneyModal = () => {
        setPayMoneyModalVisible(false);
    };

    const navigateToCards = () => {
      navigation.navigate("PayMoneyWithCard"); // "Kartlarım" sayfasına yönlendirme (sayfa adını projenize göre değiş<PERSON>rin)
    };

    return (  
        <View style={styles.container}>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={navigateToPayMoney}>
              <View style={styles.icon}>
                <View/>
                <Image
                  resizeMode="cover"
                  source={require("../../assets/iconqr.png")}
                />
              </View>
              <Text style={styles.buttonText}>Ödeme</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={openPayMoneyModal}>
              <View style={styles.icon}>
                <View/>
                <Image
                  resizeMode="cover"
                  source={require("../../assets/iconplus.png")}
                />
              </View>
              <Text style={styles.buttonText}>Para Yükle</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={navigateToTransctions}>
              <View style={styles.icon}>
                <View/>
                <Image
                  resizeMode="cover"
                  source={require("../../assets/historyicon.png")}
                />
              </View>
              <Text style={styles.buttonText}>İşlemler</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.button} onPress={navigateToCards}>
              <View style={styles.iconGold}>
                <View />
                <Image
                  resizeMode="contain"
                  source={require("../../assets/hpico.png")}
                  style={styles.iconhp}
                />
              </View>
              <Text style={styles.buttonText}>PayCard</Text>
            </TouchableOpacity>
            
            
          </View>
          <Modal
                animationType="slide"
                transparent={true}
                visible={payMoneyModalVisible}
                onRequestClose={closePayMoneyModal}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text>Bakiye Yükleme</Text>
                        <Text style={styles.aciklama} >Bakiye yükleme işlemlerini Holly kasalardan nakit olarak yapabilirsiniz.</Text>
                        <TouchableOpacity style={styles.buttonclose} onPress={closePayMoneyModal}>
                                <Text style={styles.buttonTextmodal}>Tamam</Text>
                            </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
      );
      
};

const styles = StyleSheet.create({
    container: {
      top: -50,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: white,
      width: '90%',
      height: 100,
      borderRadius: 10,
    },
    buttonContainer: {
      flexDirection: 'row',
      
    },
    iconhp:{
      height: 25,
      width: 25
    },
    button: {
      flex: 1,
      margin: 5,
      alignItems: 'center',

    },
    icon: {
      backgroundColor: "#dc1354",
      borderRadius: 5,
      padding: 10,
    },
    iconGold: {
      backgroundColor: black,
      borderRadius: 5,
      padding: 10,
    },
    buttonText: {
      color: black,
      padding: 5
      
    },
    aciklama:{
      color: black,
      padding: 5,
      fontWeight: '700',
      alignContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      top: 15,
      textAlign: 'center'
    },
    buttonTextmodal: {
      color: white,
      fontWeight: '700',
  },
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      backgroundColor: 'white',
      width: '80%',
      padding: 20,
      borderRadius: 12,
      alignItems: 'center', 

    },
    buttonclose: {
      backgroundColor: "#dc1354",
      padding: 15,
      top: 40,
      alignItems: 'center',
      borderRadius: 12,
      width: '50%'
  },
  });
  

export default Card;
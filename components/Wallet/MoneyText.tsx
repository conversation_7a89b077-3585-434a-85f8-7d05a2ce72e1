import React from "react";
import { View, StyleSheet, Text, TextInput } from "react-native";
import { black, white } from "../../constants/Color";

const MoneyText = ({ amount }: { amount: string }) => {

  const parsedAmount = parseFloat(amount);
  const formattedAmount = !isNaN(parsedAmount) ? parsedAmount.toLocaleString("tr-TR") : "0.00";

  const amountWithSymbol = formattedAmount + " ₺";

  return (
    <View style={styles.container}>
      <Text style={[styles.odeme]}>Ödemek İstediğiniz Tutar</Text>
      <TextInput
        style={[styles.tutar]}
        value={amountWithSymbol}
        showSoftInputOnFocus={false}
        maxLength={9}
        autoFocus={true}
        editable={false}
        selectTextOnFocus={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  tutar: {
    textAlign: "center",
    color: white,
    fontSize: 32,
    top: 20,
    width: '100%',
    fontWeight: 'bold',
    
  },
  odeme: {
    fontSize: 16,
    textAlign: "center",
    opacity: 0.7,
    color: white,

  },
  container: {
    width: "100%",
    top: 40,
    alignItems: "center",
    
  },
});

export default MoneyText;

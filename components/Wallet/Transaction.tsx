import React from 'react';
import { View, Text, StyleSheet, Image, ImageSourcePropType } from 'react-native';
import { green_t1, white } from '../../constants/Color';

interface TransactionProps {
    icon: ImageSourcePropType;
    description: string;
    date: string;
    amount: string;
    cashback?: string; // Cashback opsionel hale getirildi.
    type: 'expense' | 'upload' | 'holly_points_exchange';
}

const Transaction: React.FC<TransactionProps> = ({ icon, description, date, amount, type, cashback }) => {
    // Tarih formatlaması için güvenli bir yaklaşım
    let formattedDate = '';
    let formattedTime = '';
    
    try {
        // Tarih verisi var mı kontrol et
        if (date) {
            const transactionDate = new Date(date);
            
            // Tarih geçerli mi kontrol et
            if (!isNaN(transactionDate.getTime())) {
                // UTC+3 için manuel ayarlama (Türkiye saati)
                const turkeyHours = transactionDate.getUTCHours() + 3;
                transactionDate.setUTCHours(turkeyHours);
                
                // Tarih formatı: DD.MM.YYYY
                const day = transactionDate.getUTCDate().toString().padStart(2, '0');
                const month = (transactionDate.getUTCMonth() + 1).toString().padStart(2, '0');
                const year = transactionDate.getUTCFullYear();
                formattedDate = `${day}.${month}.${year}`;
                
                // Saat formatı: HH:MM
                const hours = transactionDate.getUTCHours().toString().padStart(2, '0');
                const minutes = transactionDate.getUTCMinutes().toString().padStart(2, '0');
                formattedTime = `${hours}:${minutes}`;
            } else {
                formattedDate = 'Geçersiz tarih';
                formattedTime = '';
            }
        } else {
            formattedDate = 'Tarih yok';
            formattedTime = '';
        }
    } catch (error) {
        console.error('Tarih formatı hatası:', error);
        formattedDate = 'Tarih hatası';
        formattedTime = '';
    }

    return (
        <View style={styles.container}>
            <View style={styles.main}>
                <View style={styles.iconContainer}>
                    <Image source={icon} style={styles.icon} />
                </View>
                <View style={styles.textContainer}>
                    <Text style={styles.description}>{description}</Text>
                    <Text style={styles.date}>{formattedDate} : {formattedTime}</Text>
                </View>
                <View style={styles.amountContainer}>
                    <Text style={[
                        styles.amount, 
                        type === 'expense' ? styles.expense : 
                        type === 'holly_points_exchange' ? styles.hollyPointsExchange : styles.upload
                    ]}>
                        {type === 'expense' ? '-' : '+'}{amount}
                    </Text>
                    {cashback !== '0.00' && cashback !== undefined && // Eğer cashback değeri varsa sütunu göster
                        <View style={styles.cahback}>
                            <Image source={require('../../assets/root/hP.png')} style={styles.icon2} />
                            <Text style={styles.amountcashback}>{cashback}</Text>
                        </View>
                    }
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
    },
    cahback: {
        flexDirection: 'row',
        alignItems: 'center',
        textAlign: 'center',
        alignSelf: 'center'
    },
    main: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 12,
        backgroundColor: 'white',
        marginBottom: 10,
        borderRadius: 10,
        width: '95%',
    },
    iconContainer: {
        marginRight: 12,
    },
    icon: {
        width: 60,
        height: 9,
    },
    icon2: {
        width: 15,
        height: 15,
        marginTop: 5,
        marginBottom: 5,
        marginRight: 5,
    },
    textContainer: {
        flex: 1,
    },
    description: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    date: {
        fontSize: 14,
        color: 'gray',
    },
    amountContainer: {
        marginLeft: 'auto', // Sağa hizalamak için
    },
    amount: {
        fontSize: 16,
        fontWeight: 'bold',
        color: white,
        marginLeft: 'auto',
    },
    amountcashback: {
        top: -1,
        fontSize: 14,
        color: green_t1,
        fontWeight: 'bold',
    },
    expense: {
        color: 'red',
    },
    upload: {
        color: 'green',
    },
    hollyPointsExchange: {
        color: green_t1, // Holly Points işlemleri için yeşil renk
    },
});

export default Transaction;
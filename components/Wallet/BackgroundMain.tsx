import React from "react";
import { View, TouchableOpacity, StyleSheet, Text, Image, RefreshControl  } from "react-native";
import { BackIcon } from "../../components/Svgs";
import { black_t2, white, pink } from "../../constants/Color";
import { ScrollView } from "react-native-gesture-handler";
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView } from "react-native-safe-area-context";
import Menu from "../../components/Menu";



const BackgroundMain = ({ userName, balance, walletid, handleRefresh }: { userName: string; balance: number; walletid:number; handleRefresh: () => void }) => {
  
  const navigation = useNavigation();
  

  

  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    handleRefresh(); // Yenileme işlemini gerçekleştir
    setTimeout(() => setRefreshing(false), 1000); // Yenileme işlemi bittiğinde refreshing durumunu kapat
  }, []);



  return (
    <View style={styles.background}>
      <ScrollView style={styles.bilgikarti} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
          <View>
            <Text style={styles.textname}>Hoşgeldin {userName} !</Text>
          </View>
          <View>
            <Text style={styles.textmoney}>{balance} ₺</Text>
          </View>
          <View>
            <Text style={styles.wallet}>Cüzdan Numarası: {walletid}</Text>
          </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  background: {
    backgroundColor: "#dc1354",
    height: 140,
    width: "100%",
    alignItems: 'center',
  },
  
  bilgikarti: {
    width: '90%',
   
  },
  specialTop: {
    width: 40,
    height: 40,
  },
  leftTouch: {
    flexDirection: "row", // Horizontal alignment
    justifyContent: "space-between", // Back on left, Menu on right
    alignItems: "center",
    width: "100%",
  },
  touch: {
    top: 5,
    padding: 20,
    width: 70,
  },
  textname: {
   color: white,
   marginBottom: 5,
   fontSize: 16
  },
  wallet: {
    color: white,
    fontSize: 10,
   },
   
  textmoney: {
    color: white,
    marginBottom: 2,
    fontSize: 18,
    fontWeight: '700'
   }
});

export default BackgroundMain;

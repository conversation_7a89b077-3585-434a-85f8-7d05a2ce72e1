import React from "react";
import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';
import { black_t3, green_t1, red_t6, white } from "../constants/Color";
import { WheelStoreInstance } from "../stores/WheelStore";

const WheelCountDown: React.FC = () => {
    const { daywheel, hour, minute, second, lastName } = WheelStoreInstance;

    if (!lastName) {
        return (
            <ImageBackground
                source={require('../assets/root/wheelCountBckzaman.png')}
                style={styles.main}
            >
                <View style={styles.mainAlt}>
                    <Text style={styles.timeReadyText}>{'Zaman Geldi!'}</Text>
                </View>
            </ImageBackground>
        );
    }

    return (
        <ImageBackground
            source={require('../assets/root/wheelCountBck.png')}
            style={styles.main}
        >
            <View style={styles.mainAlt}>
                <Text style={styles.titleTime}><PERSON><PERSON><PERSON>ÜRE</Text>
                <View style={styles.countDownView}>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={daywheel * 24 * 60 * 60}
                        duration={daywheel * 24 * 60 * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[daywheel * 24 * 60 * 60, (daywheel * 24 * 60 * 60) / 2]}
                        size={22}
                        strokeWidth={2}
                    >
                        {() => <Text style={styles.countDownText}>{daywheel}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={hour * 60 * 60}
                        duration={hour * 60 * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[7, 3.5]}
                        size={22}
                        strokeWidth={2}
                    >
                        {() => <Text style={styles.countDownText}>{hour}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={minute * 60}
                        duration={minute * 60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[7, 3.5]}
                        size={22}
                        strokeWidth={2}
                        onComplete={() => ({ shouldRepeat: true, delay: 0 })}
                    >
                        {() => <Text style={styles.countDownText}>{minute}</Text>}
                    </CountdownCircleTimer>
                    <CountdownCircleTimer
                        isPlaying
                        initialRemainingTime={second}
                        duration={60}
                        colors={[green_t1, red_t6]}
                        colorsTime={[25, 0]}
                        size={22}
                        strokeWidth={2}
                        onComplete={() => ({ shouldRepeat: true, delay: 0 })}
                    >
                        {() => <Text style={styles.countDownText}>{second}</Text>}
                    </CountdownCircleTimer>
                </View>
            </View>
        </ImageBackground>
    );
};

const styles = StyleSheet.create({
    main: {
        width: 196,
        height: 80
    },
    mainAlt: {
        alignSelf: 'flex-end',
        width: 100,
        marginRight: 12
    },
    titleTime: {
        marginTop: 27,
        fontSize: 8,
        fontWeight: 'bold',
        color: black_t3
    },
    timeReadyText: {
        marginTop: 49,
        fontSize: 12,
        color: black_t3,
        fontFamily: 'MADE TOMMY',
        textAlign: 'center'
    },
    countDownView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 8
    },
    countDownText: {
        fontSize: 8,
        color: white
    },
});

export default WheelCountDown;
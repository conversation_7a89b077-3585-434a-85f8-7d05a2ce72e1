import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { black, black_t3, red_t1 } from "../constants/Color";
import Layout from "../constants/Layout";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { BackIcon } from "./Svgs";

export type HeaderFour = {
    navigation: any,
    title: string,
    headerStatus: boolean
}
const HeaderFour: React.FC<HeaderFour> = ({
    navigation,
    title = "",
    headerStatus = null
}) => {

    const insets = useSafeAreaInsets();

    if (headerStatus == null)
        return (
            <View
                style={styles.main}
            >
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={styles.leftTouch}
                >
                    <BackIcon
                        size={25}
                        color={black}
                    />
                </TouchableOpacity>
                {title === "GIFT CARD" ? (
                    <Image
                        source={require('../assets/hollypaysiyah.png')}
                        style={styles.headerImage}
                        resizeMode="contain"
                    />
                ) : (
                    <Text style={styles.title}>{title}</Text>
                )}
            </View>
        )

    return (
        <SafeAreaView style={{
            zIndex: 10,
            position: 'absolute',
            alignSelf: 'center',
            width: Layout.screen.width,
            height: Math.max(insets.bottom, 0) ? 130 : 65,
            //marginTop: Math.max(insets.bottom, 0) ? 0 : 30,
            backgroundColor: headerStatus ? 'rgba(256,256,256,0.5)' : 'transparent'
        }}>
            <View
                style={styles.main}
            >
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={styles.leftTouch}>
                    <BackIcon
                        size={25}
                        color={black}
                    />
                </TouchableOpacity>
                {title === "GIFT CARD" ? (
                    <Image
                        source={require('../assets/hollypaysiyah.png')}
                        style={styles.headerImage}
                        resizeMode="contain"
                    />
                ) : (
                    <Text style={styles.title}>{title}</Text>
                )}
            </View>
        </SafeAreaView>
    )
}
export default HeaderFour;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 65
    },
    leftTouch: {
        position: 'absolute',
        paddingLeft: 10,
        padding: 15,
        left: 0
    },
    title: {
        fontSize: 19,
        color: black_t3,
        letterSpacing: 3.3
    },
    headerImage: {
        width: 150,
        height: 40,
    },
});
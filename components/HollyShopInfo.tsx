import React from "react";
import { Image, StyleSheet, Text, View } from "react-native";
import Layout from "../constants/Layout";
import { black, black_t3, gray_t3, gray_t4, green_t2 } from "../constants/Color";
import { MainStore } from "../stores/MainStore";

export type HollyShopInfo = {
    hollyPoints: number,
    avarageRating: number,
    description: string
}

const HollyShopInfo: React.FC<HollyShopInfo> = ({
    hollyPoints,
    avarageRating,
    description
}) => {
    return (
        <View style={styles.main}>

            {/* HP VIEW */}
            <View style={styles.hPView}>
                <Text style={styles.hPLeftText}>{MainStore.language.shop_desc_1}</Text>
                <Image
                    source={require('../assets/root/hP.png')}
                    style={styles.hP}
                    resizeMode="contain"
                />
                <Text style={styles.hPRightText}>{hollyPoints}</Text>
                <Text><PERSON> puan kazan</Text>
            </View>

            <View style={styles.titleStarView}>

                {/* INFO TITLE */}
                <Text style={styles.title}>{MainStore.language.product_about}</Text>

                {/* STARS */}
                <View style={styles.starsView}>
                    {
                        avarageRating > 0.5 ?
                            <Image
                                source={require('../assets/root/fullStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                            :
                            <Image
                                source={require('../assets/root/emptyStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                    }

                    {
                        avarageRating > 1.5 ?
                            <Image
                                source={require('../assets/root/fullStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                            :
                            <Image
                                source={require('../assets/root/emptyStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                    }
                    {
                        avarageRating > 2.5 ?
                            <Image
                                source={require('../assets/root/fullStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                            :
                            <Image
                                source={require('../assets/root/emptyStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                    }
                    {
                        avarageRating > 3.5 ?
                            <Image
                                source={require('../assets/root/fullStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                            :
                            <Image
                                source={require('../assets/root/emptyStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                    }
                    {
                        avarageRating > 4.5 ?
                            <Image
                                source={require('../assets/root/fullStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                            :
                            <Image
                                source={require('../assets/root/emptyStar.png')}
                                style={styles.star}
                                resizeMode="contain"
                            />
                    }
                    <Text style={styles.starPuan}>( {avarageRating} )</Text>
                </View>
            </View>
            <Text style={styles.infoDesc}>{description}</Text>
        </View>
    )
}
export default HollyShopInfo;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        top: -40,
        zIndex: -1
    },
    hPView: {
        borderWidth: 1,
        borderColor: gray_t3,
        alignItems: 'center',
        height: 50,
        justifyContent: 'center',
        borderRadius: 10,
        marginTop: 50,
        flexDirection: 'row',
    },
    hPLeftText: {
        color: black,
        fontSize: 14
    },
    hP: {
        width: 15.68,
        height: 17.37,
        marginLeft: 8
    },
    hPRightText: {
        color: green_t2,
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 3,
        marginRight: 8
    },
    titleStarView: {
        marginVertical: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    title: {
        fontWeight: 'bold',
        fontSize: 20,
        color: black_t3
    },
    starsView: { flexDirection: 'row' },
    star: {
        width: 15,
        height: 15
    },
    starPuan: {
        color: black,
        fontSize: 10,
        marginLeft: 5
    },
    infoDesc: {
        color: gray_t4,
        lineHeight: 18,
        fontSize: 14,
        marginBottom: 40,
    },
});
import React, {Component} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Animated,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import * as d3Shape from 'd3-shape';

import Svg, {G, Text, TSpan, Path, Pattern} from 'react-native-svg';

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

const {width, height} = Dimensions.get('screen');

class WheelComponent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      enabled: false,
      started: false,
      finished: false,
      winner: null,
      gameScreen: new Animated.Value(width - 90),
      wheelOpacity: new Animated.Value(1),
      imageLeft: new Animated.Value(width / 2 - 30),
      imageTop: new Animated.Value(height / 2 - 70),
    };
    this.angle = 0;

    this.prepareWheel();
  }

  prepareWheel = (winnerIndex) => {
    this.Rewards = this.props.options.rewards;
    this.RewardCount = this.Rewards.length;
    this.numberOfSegments = this.RewardCount;
    this.fontSize = 20;
    this.oneTurn = 360;
    this.angleBySegment = this.oneTurn / this.numberOfSegments;
    this.angleOffset = this.angleBySegment / 2;
    
    // Eğer winnerIndex geçilirse, bu değeri kullan
    // Aksi takdirde props'taki winner değerini kullan veya rastgele bir değer ata
    this.winner = winnerIndex !== undefined 
      ? winnerIndex 
      : (this.props.options.winner !== undefined 
        ? this.props.options.winner 
        : Math.floor(Math.random() * this.numberOfSegments));

    console.log("Wheel prepared with winner index:", this.winner);

    this._wheelPaths = this.makeWheel();
    this._angle = new Animated.Value(0);

    this.props.options.onRef(this);
  };

  resetWheelState = () => {
    this.setState({
      enabled: false,
      started: false,
      finished: false,
      winner: null,
      gameScreen: new Animated.Value(width - 40),
      wheelOpacity: new Animated.Value(1),
      imageLeft: new Animated.Value(width / 2 - 30),
      imageTop: new Animated.Value(height / 2 - 70),
    });
  };

  _tryAgain = () => {
    this.prepareWheel();
    this.resetWheelState();
    this.angleListener();
    this._onPress();
  };

  angleListener = () => {
    this._angle.addListener(event => {
      if (this.state.enabled) {
        this.setState({
          enabled: false,
          finished: false,
        });
      }

      this.angle = event.value;
    });
  };

  componentWillUnmount() {
    this.props.options.onRef(undefined);
  }

  componentDidMount() {
    this.angleListener();
  }

  makeWheel = () => {
    const data = Array.from({ length: this.numberOfSegments }).fill(1);
    const arcs = d3Shape.pie()(data);
    const colors = this.props.options.colors || [
      '#00731C',
      '#FFFFFF',
      '#00731C',
      '#FFFFFF',
      '#00731C',
      '#FFFFFF',
      '#000000',
      '#FFFFFF',
      '#E23B80',
      '#D82B2B',
    ];
  
    return arcs.map((arc, index) => {
      const instance = d3Shape.arc()
        .padAngle(0.00)
        .outerRadius(width / 2.4)
        .innerRadius(this.props.options.innerRadius || 100);
  
      const reward = this.Rewards[index];
  
      const name = reward && reward.name ? reward.name.toString() : '';
      const image = reward && reward.image ? reward.image : '';
  
      return {
        path: instance(arc),
        color: colors[index % colors.length],
        name: name,
        centroid: instance.centroid(arc),
        image: image,
        value: reward,  // Ödül değerini de ekleyerek doğru veriyi iletmesini sağlıyoruz
      };
    });
  };
  
  _getWinnerIndex = () => {
    const deg = Math.abs(Math.round(this.angle % this.oneTurn));
    // wheel turning counterclockwise
    if (this.angle < 0) {
      return Math.floor(deg / this.angleBySegment);
    }
    // wheel turning clockwise
    return (
      (this.numberOfSegments - Math.floor(deg / this.angleBySegment)) %
      this.numberOfSegments
    );
  };

  _onPress = () => {
    const duration = this.props.options.duration || 10000;

    this.setState({
      started: true,
    });
    
    console.log("Starting wheel animation with winner:", this.winner);
    console.log("Number of segments:", this.numberOfSegments);
    console.log("Rotation calculation:", 365 - this.winner * (this.oneTurn / this.numberOfSegments) + 360 * (duration / 1000));
    
    // Çarkın ne kadar döneceğini hesaplarken kazanan ödül doğru yere gelecek şekilde ayarlıyoruz
    Animated.timing(this._angle, {
      toValue:
        365 -
        this.winner * (this.oneTurn / this.numberOfSegments) +
        360 * (duration / 1000),
      duration: duration,
      useNativeDriver: true,
    }).start(() => {
      const winnerIndex = this._getWinnerIndex();
      console.log("Animation finished, calculated winner index:", winnerIndex);
      
      // Beklenen kazanan indeksi ile hesaplanan kazanan indeksi karşılaştırıyoruz
      if (winnerIndex !== this.winner) {
        console.warn("Winner index mismatch! Expected:", this.winner, "Got:", winnerIndex);
      }
      
      this.setState({
        finished: true,
        winner: this._wheelPaths[winnerIndex].value,
      });
      this.props.getWinner(this._wheelPaths[winnerIndex].value, winnerIndex);
    });
  };
  
  _imageRender = (x, y, image, i, rotation) => {
    const wheelSize = width - 40; // Wheel componentinin gerçek boyutu
    const center = wheelSize / 2; // Merkez noktası
    const imageSize = 40;
    
    // Resim pozisyonunu centroid değerlerine göre ayarla
    const imageX = x + center - imageSize/2;
    const imageY = y + center - imageSize/2;
  
    return (
      <Image
        source={{uri: image}}
        style={{
          position: 'absolute',
          width: imageSize,
          height: imageSize,
          transform: [
            {translateX: imageX},
            {translateY: imageY},
            {rotate: `${rotation}deg`}
          ],
        }}
        key={`arc-${i}-image`}
      />
    );
  };
  
  _textRender = (x, y, number, i) => (
    <Text
      x={x - number.length * 5}
      y={y - 50}
      fill={
        this.props.options.textColor ? this.props.options.textColor : '#fff'
      }
      textAnchor="middle"
      fontSize={this.fontSize}>
      {Array.from({length: number.length}).map((_, j) => {
          return (
            <TSpan
              y={y - 50}
              dx={this.fontSize * 0.07}
              key={`arc-${i}-slice-${j}`}>
              {number.charAt(j)}
            </TSpan>
          );
      })}
    </Text>
  );

  _renderSvgWheel = () => {
    return (
      <View style={styles.container}>
        {this._renderKnob()}
        <Animated.View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            transform: [
              {
                rotate: this._angle.interpolate({
                  inputRange: [-this.oneTurn, 0, this.oneTurn],
                  outputRange: [
                    `-${this.oneTurn}deg`,
                    `0deg`,
                    `${this.oneTurn}deg`,
                  ],
                }),
              },
            ],
            width: width - 20,
            height: width - 20,
            borderRadius: (width - 20) / 2,
            borderWidth: this.props.options.borderWidth
              ? this.props.options.borderWidth
              : 2,
            borderColor: this.props.options.borderColor
              ? this.props.options.borderColor
              : '#fff',
            opacity: this.state.wheelOpacity,
          }}>
          <ImageBackground
            source={require('../assets/carkarkplan.png')}
            resizeMode="cover"
            style={{
              position: 'absolute',
              width: width ,
              height: width ,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
<AnimatedSvg
  width={width - 40}
  height={width - 40}
  viewBox={`0 0 ${width} ${width}`}
  style={{
    transform: [{ rotate: `-${this.angleOffset}deg` }],
  }}>
              <G x={width/2} y={width/2}>
    {this._wheelPaths.map((arc, i) => {
      const [x, y] = arc.centroid;
      return (
        <G key={`arc-${i}`}>
                      <Path d={arc.path} strokeWidth={2} fill={arc.color} />
                      <G
                        rotation={
                          (i * this.oneTurn) / this.numberOfSegments +
                          this.angleOffset
                        }
                        origin={`${x}, ${y}`}>
                        {/* Ödül isimleri */}
                        {/*this._textRender(x, y, number, i)*/}
                        {/* Ödül resimleri */}
                        {this._imageRender(x, y, arc.image, i, (i * this.oneTurn)/this.numberOfSegments + this.angleOffset)}
                      </G>
                    </G>
                  );
    
                })}
              </G>
            </AnimatedSvg>
          </ImageBackground>
        </Animated.View>
      </View>
    );
  };
  
  _renderKnob = () => {
    const knobSize = this.props.options.knobSize
      ? this.props.options.knobSize
      : 20;
    // [0, this.numberOfSegments]
    const YOLO = Animated.modulo(
      Animated.divide(
        Animated.modulo(
          Animated.subtract(this._angle, this.angleOffset),
          this.oneTurn,
        ),
        new Animated.Value(this.angleBySegment),
      ),
      1,
    );

    return (
      <Animated.View
        style={{
          width: knobSize,
          height: knobSize * 2,
          justifyContent: 'flex-end',
          zIndex: 2,
          opacity: this.state.wheelOpacity,
          transform: [
            {
              rotate: YOLO.interpolate({
                inputRange: [-1, -0.5, -0.0001, 0.0001, 0.5, 1],
                outputRange: [
                  '0deg',
                  '0deg',
                  '35deg',
                  '-35deg',
                  '0deg',
                  '0deg',
                ],
              }),
            },
          ],
        }}>
        <Svg
          width={knobSize}
          height={(knobSize * 100) / 57}
          viewBox={`0 0 57 100`}
          style={{
            transform: [{translateY: 40}],
          }}>
          <Image
            source={
              this.props.options.knobSource
                ? this.props.options.knobSource
                : require('../assets/knob.png')
            }
            style={{ width: knobSize, height: (knobSize * 100) / 57 }}
          />
        </Svg>
      </Animated.View>
    );
  };

  _renderTopToPlay() {
    if (this.state.started == false) {
      return (
        <TouchableOpacity onPress={() => this._onPress()}>
          {this.props.options.playButton()}
        </TouchableOpacity>
      );
    }
  }

  render() {
    return (
      <View style={styles.container}>
      <View
        style={{
          position: 'absolute',
          width: width,
          height: height / 2,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        
        <Animated.View style={[styles.content, { padding: 10 }]}>
          {this._renderSvgWheel()}
        </Animated.View>
      </View>
      {this.props.options.playButton ? this._renderTopToPlay() : null}
    </View>
    );
  }
}

export default WheelComponent;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {},
  startText: {
    fontSize: 50,
    color: '#fff',
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.4)',
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
});
import React, { useState } from "react";
import { View, StyleSheet, FlatList, Image, Dimensions, TouchableOpacity, Text } from 'react-native';
import Animated, {interpolate, Extrapolate, useSharedValue, useAnimatedStyle} from "react-native-reanimated";
import Ticket from "./Ticket";
import { white } from "../constants/Color";
const dayjs = require('dayjs');

const SRC_WIDTH = Dimensions.get("window").width;
const CARD_LENGTH = SRC_WIDTH * 0.8;
const SPACING = SRC_WIDTH * 0.02;
const SIDECARD_LENGTH = (SRC_WIDTH * 0.0) ;
const AnimatedFlatList = Animated.createAnimatedComponent(FlatList)

interface ItemProps {
  index: number,
  scrollX: number,
  item: any,
  type: number
}


function Item({ index, scrollX, item, type }: ItemProps) {

  const size = useSharedValue(0.8);

  const inputRange = [
    (index -1) * CARD_LENGTH,
    index * CARD_LENGTH,
    (index + 1) * CARD_LENGTH
  ]

  size.value = interpolate(
    scrollX,
    inputRange,
    [0.8, 1, 0.8],
    Extrapolate.CLAMP,
  )


  const opacity = useSharedValue(1);
  const opacityInputRange = [
    (index - 1) * CARD_LENGTH,
    index * CARD_LENGTH,
    (index + 1) * CARD_LENGTH,
  ];
  opacity.value = interpolate(
    scrollX,
    opacityInputRange,
    [0.5, 1, 0.5],
    Extrapolate.CLAMP
  );

  const cardStyle = useAnimatedStyle(()=>{
    return{
      transform: [{scaleY: size.value}],
      opacity: opacity.value,
    }
  })

  return (
    <Animated.View style={[styles.card, cardStyle, {
      marginLeft: index == 0 ? SIDECARD_LENGTH : SPACING,
      marginRight: index == 2 ? SIDECARD_LENGTH: SPACING,
    }]}>
      {
        type == 2 ?
          <TouchableOpacity
            onPress={() => {
              //scrollXIndex.setValue(index);
            }}
            onLongPress={() => {
              //setConcertQr(item.qrImage);
              //setModal(true);
            }}
            //(dynamicIndex + 1) === index 
            style={{
              marginHorizontal: 5,
            }}>
            <Ticket name={item.concertName} image={item.concertImage} date={""} />
          </TouchableOpacity>
          :
          <TouchableOpacity
            onPress={() => {
              //scrollXIndex.setValue(index)
            }}
          >
            <Image
              style={{
                width: 144,
                height: 116.37
              }}
              source={require('../assets/menu/usedTicketProfile.png')}
            />
            <Text style={{ position: 'absolute', bottom: 14, left: 25, fontSize: 8, color: white, fontWeight: 'bold' }}>{item.concertName.toString().substring(0, 12)}..</Text>
            <Text style={{ position: 'absolute', bottom: 9, left: 25, fontSize: 4, color: white, fontWeight: 'bold' }}>{dayjs(item.concertDate).format("DD MMMM dddd")}</Text>
          </TouchableOpacity>
      }
    </Animated.View>
  );
}

export default function Carousel({ data, type }: any) {
  const [scrollX, setScrollX] = useState(0);

  return (
    <Animated.View>
      <AnimatedFlatList  
        scrollEventThrottle={16}
        showsHorizontalScrollIndicator={false}
        decelerationRate={0.8}
        snapToInterval={CARD_LENGTH + (SPACING * 1.5)}
        disableIntervalMomentum={true}
        disableScrollViewPanResponder={true}
        snapToAlignment={"center"}
        data={data}
        horizontal={true}
        renderItem={({ item, index }) => {
          return (
            <Item type={type} item={item} index={index} scrollX={scrollX} />
          );
        }}
        keyExtractor={(item) => item.id}
        onScroll={(event) => {
          setScrollX(event.nativeEvent.contentOffset.x);
        }}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    overflow: "hidden"
  }
});

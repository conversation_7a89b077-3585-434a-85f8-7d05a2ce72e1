import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import Layout from "../constants/Layout";
import { white, black } from "../constants/Color";
import { MainStore } from "../stores/MainStore";

export type NotifyView = {
    navigation: any
    bottomDesc: string,
    content: string,
    header: string,
    detail: number,
    detailId: number
}

const NotifyView: React.FC<NotifyView> = ({
    navigation,
    bottomDesc = "",
    content = "",
    header = "",
    detail = 0,
    detailId = 0
}) => {
    return (
        <View style={styles.main}>
            <Text style={styles.title}>
                {header}
            </Text>
            <Text style={styles.desc}>
                {content.length > 50 ? `${content.substring(0, 100)}...` : content}
            </Text>
            {/*
                1 - Concert
                2 - Daily Program
                3 - Holly Shop
                4 - Holly Snap
            */}
            <TouchableOpacity
                onPress={() => {
                    navigation.navigate(
                        detail == 1 ?
                            "ConcertDetail"
                            :
                            detail == 2 ?
                                //navigation.navigate("")
                                "DailyActivityDetail"
                                :
                                detail == 3 ?
                                    "HollyShop"
                                    :
                                    "HollySnap",

                        detail == 1 ?
                            { concertId: detailId }
                            :
                            detail == 2 ?
                                { activityId: detailId }
                                :
                                detail == 3 ?
                                    { productId: detailId }
                                    :
                                    // HOLLY SNAP 
                                    {}
                    )
                }}
                style={styles.detailTouch}
            >
                <Text style={styles.detail}>{MainStore.language.detail}</Text>
            </TouchableOpacity>
            <View style={styles.whichCityView}>
                <Text style={styles.whichCity}>{bottomDesc}</Text>
            </View>
        </View >
    )
}
export default NotifyView;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        padding: 10,
        paddingBottom: Layout.screen.height / 12,
        width: Layout.screen.width / 1.08,
        alignSelf: 'center',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        bottom: 0,
        position: 'absolute'
    },
    title: {
        fontWeight: 'bold',
        color: white,
        fontSize: 14
    },
    desc: {
        fontSize: 12,
        color: white,
        marginVertical: 10
    },
    detailTouch: {
        backgroundColor: white,
        borderRadius: 8,
        width: '25%',
        alignSelf: 'flex-end',
        alignItems: 'center',
        justifyContent: 'center'
    },
    detail: {
        paddingHorizontal: 10,
        color: black,
        marginVertical: 2,
        fontSize: 14,
        fontWeight: 'bold'
    },
    whichCityView: {
        backgroundColor: black,
        height: Layout.screen.height / 22,
        width: Layout.screen.width,
        position: 'absolute',
        left: -20,
        paddingLeft: 25,
        justifyContent: 'center',
        bottom: 0
    },
    whichCity: {
        color: white,
        fontFamily: 'MADE TOMMY',
        fontSize: 18,
        marginRight: Layout.screen.width / 2.2
    },
});
import { <PERSON><PERSON><PERSON>ie<PERSON> } from "moti";
import React from "react";
import { StyleSheet } from "react-native";
import { TouchableOpacity, Image, Text, View } from "react-native";
import Layout from "../constants/Layout";
import { white } from "../constants/Color";


export type ConcertList = {
    rightImg: any,
    status: boolean,
    setStatus: Function,
    indexNo: Number | any,
    logo: {
        type: Number,
        logoAlt: any,
        style: Object
    },
    children: any,
    backgroundColor: string
}

const ConcertList: React.FC<ConcertList> = ({
    rightImg = "",
    status = false,
    setStatus = () => { },
    indexNo = 0,
    logo: {
        type = 0,
        logoAlt = "",
        style = {}
    },
    children = <></>,
    backgroundColor = 'black'
}) => {

    const [contentStatus, setContentStatus] = React.useState(true);

    React.useEffect(() => {
        if (status)
            setTimeout(() => {
                setContentStatus(true);
            }, 100)
        else {
            setContentStatus(false);
        }
    }, [status]);

    return (
        <TouchableOpacity
            onPress={() => {
                setContentStatus(!contentStatus);
                setTimeout(() => {
                    setStatus(!status);
                }, 100)
            }}
            activeOpacity={1}
            style={[
                styles.main,
                {
                    marginLeft: 55 * indexNo,
                    zIndex: 5 - indexNo,
                    width: Layout.screen.width > 400 ?
                        status ?
                            Layout.screen.width / 2.3
                            : 55
                        :
                        status ?
                            Layout.screen.width / 2.6
                            : 55,
                    left: -indexNo * 22
                }
            ]}
        >
            <MotiView
                from={{
                    width: 10,
                }}
                animate={{
                    width: status ?
                        Layout.screen.width - 172
                        :
                        10,
                }}
                transition={{ type: 'timing', duration: 100 }}
                style={[
                    styles.moti,
                    {
                        backgroundColor: backgroundColor,
                        borderTopLeftRadius: indexNo == 0 ? 10 : 0,
                        borderBottomLeftRadius: indexNo == 0 ? 10 : 0,
                        paddingLeft: status ? indexNo != 0 ? 40 : 0 : 0
                    }
                ]}
            >
                {
                    status && contentStatus ?
                        children
                        :
                        <></>
                }
            </MotiView>
            <View>
                <Image
                    source={rightImg}
                    style={[styles.allRightImg, { top: 0, height: 295 }]}
                    resizeMode="contain"
                />
                {
                    type == 1 ?
                        <Text style={[
                            styles.logoAltText,
                            {
                                left: indexNo == 0 ? -20 : -17,
                            },
                            style
                        ]}>{logoAlt}</Text>
                        :
                        <Image
                            resizeMode="contain"
                            style={[styles.altImg, style]}
                            source={logoAlt}
                        />
                }
            </View>
        </TouchableOpacity>
    )
}
export default ConcertList;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexDirection: 'row',
        position: 'absolute',
    },
    moti: {
        zIndex: 2,
        height: 295
    },
    allRightImg: {
        width: 56,
        height: 295,
        bottom: 3,
        right: 10,
        zIndex: 1
    },
    logoAltText: {
        fontSize: 16,
        fontWeight: 'bold',
        transform: [{ rotate: '270deg' }],
        position: 'absolute',
        zIndex: 2,
        top: 38,
        color: white,
        width: 80
    },
    altImg: {
        position: 'absolute',
        width: 72,
        height: 13,
        right: -10,
        zIndex: 2,
        top: 44,
        transform: [{ rotate: '270deg' }]

    },
});
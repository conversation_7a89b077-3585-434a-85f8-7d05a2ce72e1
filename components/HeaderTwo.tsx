import React from "react";
import { Image, ImageProps, StyleSheet, TouchableOpacity, View } from "react-native";
import Layout from "../constants/Layout";
import { BackIcon } from "./Svgs";
import { red_t1 } from "../constants/Color";

export type HeaderTwo = {
    onMenu: any,
    menuStatus: boolean,
    leftIcon: ImageProps,
    logo: ImageProps,
    menuIcon: ImageProps,
    navigation: any
}

const HeaderTwo = ({
    onMenu = (set: boolean) => { },
    menuStatus = false,
    navigation = {
        goBack: () => { }
    },
    leftIcon = require('../assets/general/background.png'),
    logo = require('../assets/general/background.png'),
    menuIcon = require('../assets/general/background.png'),
}) => {
    return (
        <View style={styles.main}>
            <View style={styles.leftArea}>
                <TouchableOpacity
                    style={{ paddingRight: 15  }}
                    onPress={() => {
                        navigation.goBack();
                    }}
                >
                    <BackIcon
                        size={25}
                        color={leftIcon}
                    />
                </TouchableOpacity>
                {
                    !logo ?
                        <></>
                        :
                        <Image
                            source={logo}
                            style={styles.logo}
                            resizeMode="contain"
                        />
                }
            </View>
            <TouchableOpacity
                onPress={() => {
                    onMenu(!menuStatus);
                }}
            >
                <Image
                    source={menuIcon}
                    style={styles.menu}
                    resizeMode="contain"
                />
            </TouchableOpacity>
        </View>
    )
}
export default HeaderTwo;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: Layout.screen.width / 1.1,
        marginTop: 20,
        alignSelf: 'center',
        alignItems: 'center'
    },
    leftArea: { flexDirection: 'row' },
    logo: {
        height: 30,
        width: 170,
        bottom: 3
    },
    menu: {
        height: 42,
        width: 42,
        borderRadius: 8
    },
});
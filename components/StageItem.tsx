import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { black, black_t2, brown_t1, brown_t2, brown_t3, brown_t4, green_t1, white, yellow_t1, yellow_t2 } from "../constants/Color";
import { shadow } from "../constants/Shadow";
import { get, getImageURL } from "../networking/Server";

export type StageItemProps = {
    topBackgroundColor: string,
    prfTextColor: string,
    stage: any,
    stageLogo: any,
    artistName: string,
    cameraIcon: any,
    artistImage: any,
    arraySnaps: any[],
    onCameraPress: () => void, // Handler for camera icon press
    onProfilePress: (userId: number) => void, // Handler for user profile press
}

const StageItem: React.FC<StageItemProps> = ({
    topBackgroundColor,
    prfTextColor,
    stage,
    stageLogo,
    artistName,
    cameraIcon,
    artistImage,
    arraySnaps,
    onCameraPress,
    onProfilePress
}) => {
    // Stage adı
    const title = stage?.split(" ");

    return (
        <View style={styles.itemView}>
            <View style={[styles.itemTop, shadow, { backgroundColor: topBackgroundColor }]}>
                {/* Sahne logosu veya başlık */}
                {stageLogo ?
                    <Image
                        source={stageLogo}
                        style={styles.stageLogo}
                        resizeMode="contain"
                    />
                    :
                    !title ?
                        <></>
                        :
                        <Text style={[styles.itemStage, { color: prfTextColor }]}> {title[0]?.toUpperCase()} <Text style={styles.itemStageAlt}>{title[1]?.toUpperCase()}</Text></Text>
                }

                {/* Sanatçı adı */}
                <Text style={[styles.artist, { color: prfTextColor }]}>
                    {artistName?.toUpperCase()}
                </Text>

                {/* Profil listesi */}
                <View style={styles.whoThereView}>
                    {/* Eğer 1'den fazla snap varsa */}
                    {arraySnaps.length > 0
                        ? arraySnaps.map((item: any, index: number) => {
                            // Eğer index 3'ten büyükse, boşluk döndür
                            if (index > 3) return <></>
                            return (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => onProfilePress(item.userId)} // onPress işlevselliği burada çağrılıyor
                                    style={styles.prfTouchable}
                                >
                                    <View style={styles.whoThereAlt}>
                                        {/* Eğer index 3'te ise */}
                                        {
                                            index === 3 &&
                                            <View style={styles.afterThreeView}>
                                                <Text style={styles.afterThreeText}>
                                                    +{arraySnaps.length - 3}
                                                </Text>
                                            </View>
                                        }
                                        {/* Görüntü kaynağı */}
                                        <View style={shadow}>
                                            <Image
                                                source={{ uri: getImageURL(item.image) }}
                                                style={styles.prf}
                                            />
                                        </View>
                                        {/* İsim */}
                                        <Text style={[styles.prfName, { color: prfTextColor }]}>{item.firstName}</Text>
                                    </View>
                                </TouchableOpacity>
                            )
                        })
                        :
                        <></>
                    }
                    
                </View>
            </View>

            {/* Sanatçı fotoğrafı */}
            <Image
                resizeMode="cover"
                style={styles.artistImg}
                source={artistImage}
                
            />

            {/* Kamera simgesi */}
            <TouchableOpacity
                onPress={onCameraPress} // Camera icon press handler
                style={[
                    styles.cameraTouch,
                    {
                        borderColor: topBackgroundColor,
                    }
                ]}
            >
                <View
                    style={[
                        styles.cameraView,
                        {
                            backgroundColor: topBackgroundColor,
                        }
                    ]}
                >
                    {/* Kamera ikonu */}
                    <Image
                        source={cameraIcon}
                        style={styles.cameraIcon}
                        resizeMode="contain"
                    />
                </View>
            </TouchableOpacity>
        </View>
    )
}


export default StageItem;

// -- STYLES -- //
const styles = StyleSheet.create({
    itemView: {
        padding: 10,
        margin: 10,
        marginBottom: 40,
        borderRadius: 15,
        backgroundColor: yellow_t2
    },
    itemTop: {
        padding: 10,
        alignItems: 'center',
        borderRadius: 15
    },
    stageLogo: {
        height: 9,
        width: 113,
        marginVertical: 5,
    },
    itemStage: {
        fontSize: 17,
        fontFamily: 'Helvetica Neue LT Pro'
    },
    itemStageAlt: { fontWeight: 'normal' },
    artist: {
        fontFamily: 'MADE TOMMY',
        letterSpacing: 2.1,
        color: black,
        fontSize: 17,
        marginTop: 5
    },
    whoThereView: {
        flexDirection: 'row',
        marginTop: 10
    },
    whoThereAlt: {
        alignItems: 'center',
        marginHorizontal: 10
    },
    afterThreeView: {
        position: 'absolute',
        height: 44,
        borderWidth: 2,
        borderColor: white,
        width: 44,
        zIndex: 2,
        borderRadius: 22,
        backgroundColor: 'rgba(0,0,0,0.7)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    afterThreeText: {
        fontFamily: 'MADE TOMMY',
        fontSize: 12,
        color: yellow_t1
    },
    prf: {
        borderRadius: 22,
        width: 44,
        height: 44,
        borderWidth: 2,
        borderColor: white
    },
    prfName: {
        fontSize: 6,
        fontWeight: 'bold',
        marginTop: 5
    },
    artistImg: {
        borderRadius: 10,
        marginTop: 10,
        height: 150,
        width: '100%'
    },
    cameraTouch: {
        borderWidth: 2,
        position: 'absolute',
        bottom: -30,
        alignSelf: 'center',
        width: 68,
        height: 68,
        borderRadius: 34,
        alignItems: 'center',
        justifyContent: 'center'
    },
    cameraView: {
        alignItems: 'center',
        justifyContent: 'center',
        height: 58,
        width: 58,
        borderRadius: 29
    },
    cameraIcon: {
        width: 29,
        height: 23
    },
});

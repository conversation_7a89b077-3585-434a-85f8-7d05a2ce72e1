import React from "react";
import { StyleSheet, View, Text, Image, TouchableOpacity, FlatList } from "react-native";
import Layout from "../constants/Layout";
import { black_t3, brown_t2, white } from "../constants/Color";
import { getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";

export type OtherProduct = {
    navigation: any,
    products: any,
    isSearch: boolean
}

const OtherProducts: React.FC<OtherProduct> = ({
    navigation,
    products = [],
    isSearch = false
}) => {
    return (
        <View style={styles.main}>
            <Text style={styles.title}>{isSearch ? MainStore.language.resultSearch : MainStore.language.other_products}</Text>
            <View style={styles.otherProductView}>
                <FlatList
                    data={products}
                    numColumns={2}
                    columnWrapperStyle={{ justifyContent: 'space-between', paddingHorizontal: 20 }}
                    renderItem={({ item, index }: any) => {
                        return (
                            <TouchableOpacity
                                key={index}
                                onPress={() => {
                                    navigation.navigate("HollyShopDetail", { productId: item.id })
                                }}
                                style={styles.oPRightView}
                            >
                                <Image
                                    source={{ uri: getImageURL(item?.images[0]) }}
                                    style={styles.productImg}
                                    resizeMode="cover"
                                />
                                <Text style={styles.productName}>{
                                    item.name.length > 22 ?
                                        item?.name?.substring(0, 22) + "..."
                                        :
                                        item.name
                                }</Text>
                            </TouchableOpacity>
                        )
                    }}
                />

            </View>
        </View>
    )
}
export default OtherProducts;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        width: Layout.screen.width / 1.13,
        alignSelf: 'center',
        paddingVertical: 10
    },
    title: {
        fontWeight: 'bold',
        fontSize: 18,
        color: black_t3
    },
    otherProductView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20
    },
    oPLeftView: {
        width: 111,
        height: 194,
        backgroundColor: brown_t2,
        borderRadius: 24,
        marginLeft: '5%'
    },
    oPRightView: {
        width: 111,
        marginTop: 20,
        height: 194,
        backgroundColor: brown_t2,
        borderRadius: 24,
        marginRight: '5%'
    },
    productImg: {
        height: 167,
        width: 119,
        backgroundColor: white,
        right: 4,
        borderRadius: 25
    },
    productName: {
        color: white,
        fontSize: 10,
        marginTop: 2,
        width: 100,
        alignSelf: 'center',
        fontWeight: 'bold',
        textAlign: 'center'
    },
});
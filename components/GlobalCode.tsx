import React from "react";
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Layout from "../constants/Layout";
import { black, black_t5, white } from "../constants/Color";
import { globalCodes } from "../constants/CountryCode";
import { Spinner } from "native-base";

export type GlobalCode = {
    setModal: any,
    setCountryCode: any
}
const GlobalCode: React.FC<GlobalCode> = ({
    setModal = () => { },
    setCountryCode = () => { }
}) => {
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
        setTimeout(() => {
            setLoading(false);
        }, 500);
    }, []);
    return (
        <View style={styles.main}>
            <Text style={styles.countryCodeText}>ÜLKE KODLARI</Text>
            <View style={styles.countryCodeView} />
            <ScrollView>
                {
                    loading ?
                        <Spinner mt={5} color={black_t5} size={18} />
                        :
                        globalCodes.map((item, index) => {
                            return (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => {
                                        setModal(false);
                                        setCountryCode(item.code)
                                    }}
                                >
                                    <View style={styles.countryCodeTouch}>
                                        <Text style={{
                                            fontSize: 16,
                                            fontFamily: 'MADE TOMMY',
                                            color: black
                                        }}>+{item.code} - {item.country}</Text>
                                    </View>
                                    <View style={styles.countryCodeTouchAltView} />
                                </TouchableOpacity>
                            )
                        })
                }
            </ScrollView>

        </View>
    )
}
export default GlobalCode;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        position: 'absolute',
        top: 50,
        backgroundColor: white,
        alignSelf: 'center',
        zIndex: 100
    },
    countryCodeText: {
        fontFamily: 'MADE TOMMY',
        textAlign: 'center',
        marginVertical: 15,
        fontSize: 20,
        color: black_t5
    },
    countryCodeView: {
        width: Layout.screen.width,
        height: 2,
        backgroundColor: black_t5
    },
    countryCodeTouch: { padding: 15 },
    countryCodeTouchAltView: {
        width: Layout.screen.width,
        height: 1,
        backgroundColor: black_t5
    },
})
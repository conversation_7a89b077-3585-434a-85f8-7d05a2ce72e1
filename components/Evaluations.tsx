import React from "react";
import { Image, StyleSheet, Text, View } from "react-native";
import Layout from "../constants/Layout";
import { black, black_t3, gray_t5 } from "../constants/Color";
import { MainStore } from "../stores/MainStore";

export type Evaluations = {
    comments: any
}

const Evaluations: React.FC<Evaluations> = ({
    comments
}) => {
    return (
        <View style={styles.main}>
            <Text style={styles.title}>{MainStore.language.reviews}</Text>
            {
                comments.map((item: {
                    comment: string,
                    firstName: string,
                    lastName: string,
                    rating: number
                }, index: React.Key) => {
                    return (
                        <View
                            key={index}
                            style={
                                [
                                    styles.evaluationView,
                                    {
                                        marginBottom:index==comments?.length-1?200:0
                                    }
                                ]
                            }
                        >
                            <View style={styles.starsNameView}>
                                <Text style={styles.name}>{item.firstName} {item.lastName}</Text>
                                {/* STARS */}
                                <View style={styles.starsView}>
                                    {
                                        item.rating > 0.5 ?
                                            <Image
                                                source={require('../assets/root/fullStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                            :
                                            <Image
                                                source={require('../assets/root/emptyStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                    }
                                    {
                                        item.rating > 1.5 ?
                                            <Image
                                                source={require('../assets/root/fullStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                            :
                                            <Image
                                                source={require('../assets/root/emptyStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                    }
                                    {
                                        item.rating > 2.5 ?
                                            <Image
                                                source={require('../assets/root/fullStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                            :
                                            <Image
                                                source={require('../assets/root/emptyStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                    }
                                    {
                                        item.rating > 3.5 ?
                                            <Image
                                                source={require('../assets/root/fullStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                            :
                                            <Image
                                                source={require('../assets/root/emptyStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                    }
                                    {
                                        item.rating > 4.5 ?
                                            <Image
                                                source={require('../assets/root/fullStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                            :
                                            <Image
                                                source={require('../assets/root/emptyStar.png')}
                                                style={styles.star}
                                                resizeMode="contain"
                                            />
                                    }


                                    <Text style={styles.starPuan}>( {item.rating} )</Text>
                                </View>
                            </View>
                            <Text style={styles.evaluation}>{item.comment}</Text>
                        </View>
                    )
                })
            }

        </View>
    )
}
export default Evaluations;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        top:-40
    },
    title: {
        color: black_t3,
        fontSize: 20,
        fontWeight: 'bold',
    },
    evaluationView: {
        padding: 15,
        backgroundColor: gray_t5,
        opacity: 0.8,
        borderRadius: 8,
        marginVertical: 10
    },
    starsNameView: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    name: {
        fontWeight: 'bold',
        color: black,
        fontSize: 16
    },
    starsView: { flexDirection: 'row' },
    star: {
        width: 15,
        height: 15
    },
    starPuan: {
        color: black,
        fontSize: 10,
        marginLeft: 5
    },
    evaluation: {
        fontWeight: 'bold',
        color: black,
        fontSize: 14,
        marginTop: 10,
        marginBottom: 10
    },
});
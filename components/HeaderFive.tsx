import React from "react";
import { Image, Keyboard, StyleSheet, TouchableOpacity, View } from "react-native";
import Layout from "../constants/Layout";
import { <PERSON><PERSON><PERSON>ie<PERSON> } from "moti";
import { green_t2, red_t1, white } from "../constants/Color";
import { Input } from "native-base";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { MainStore } from "../stores/MainStore";
import { BackIcon, Shaman } from "./Svgs";

export type Header = {
    onMenu: any,
    menuStatus: boolean,
    searchIconColor: string,
    navigation: any,
    logo: any,
    backIcon: any,
    menuIcon?: any,
    searchIcon: any,
    logoStyle: any,
    onSearch?: any,
    headerControl?: boolean
}

const HeaderFive: React.FC<Header> = ({
    onMenu,
    menuStatus = false,
    searchIconColor,
    navigation,
    logo,
    backIcon,
    menuIcon,
    searchIcon,
    logoStyle,
    onSearch,
    headerControl = false
}) => {

    const [searchStatus, setSearchStatus] = React.useState(false);
    const insets = useSafeAreaInsets();

    return (
        <SafeAreaView
            style={
                [
                    styles.safeAreaView,
                    {
                        height: Math.max(insets.bottom, 0) ? 130 : 65,
                        //marginTop: Math.max(insets.bottom, 0) ? 0 : 30,
                        backgroundColor: headerControl ? 'rgba(256,256,256,0.6)' : 'transparent'
                    }
                ]
            }
        >

            <View style={[styles.main, {}]}>

                {/* GO BACK TOUCH */}
                <TouchableOpacity
                    onPress={() => {
                        navigation.goBack();
                    }}
                    style={styles.backIconTouch}
                >
                    <BackIcon
                        size={25}
                        color={backIcon}
                    />
                </TouchableOpacity>
                {
                    logo ?
                        typeof logo == "number" ?
                            <Image
                                source={logo}
                                style={logoStyle}
                                resizeMode="contain"
                            />
                            :
                            <View style={{
                                position: 'absolute',
                                left: 50,
                                top: -5
                            }}>
                                {logo}
                            </View>

                        :
                        <></>

                }

                {/* INPUT */}
                <MotiView
                    from={{
                        width: 0,
                        height: 35
                    }}
                    animate={{
                        height: 35,
                        width: searchStatus ? Layout.screen.width / 1.48 : 0
                    }}
                    style={[styles.moti, { backgroundColor: searchIconColor }]}
                >
                    <Input
                        placeholder={MainStore.language.search}
                        borderWidth={0}
                        onChangeText={(text) => {
                            onSearch(text);
                        }}
                        placeholderTextColor={white}
                        color={white}
                    />
                </MotiView>

                {/* SEARCH AND MENU */}
                {
                    searchIcon ?
                        <TouchableOpacity
                            onPress={() => {
                                setSearchStatus(!searchStatus);
                                Keyboard.dismiss();
                            }}
                            style={[styles.search, { backgroundColor: searchIconColor }]}
                            activeOpacity={1}
                        >
                            <Image
                                source={searchIcon}
                                style={styles.iconSearch}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>
                        :
                        <></>
                }
                {
                    menuIcon ?
                        <TouchableOpacity
                            onPress={() => {
                                onMenu(!menuStatus)
                            }}
                        >
                            <Image
                                source={menuIcon}
                                style={styles.iconMenu}
                                resizeMode="cover"
                            />
                        </TouchableOpacity>
                        :
                        <></>
                }
            </View>
        </SafeAreaView>
    )
}
export default HeaderFive;

// -- STYLES -- //
const styles = StyleSheet.create({
    safeAreaView: {
        position: 'absolute',
        zIndex: 2,
        backgroundColor: 'red',

    },
    main: {
        width: Layout.screen.width / 1,
        paddingRight: 10,
        paddingVertical: 10,
        paddingTop: 17,
        alignSelf: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center'
    },
    backIconTouch: {
        position: 'absolute',
        paddingHorizontal: 15,
        paddingVertical: 10,
        left: 0,
        alignSelf: 'center'
    },
    moti: {
        right: -25,
        justifyContent: 'center',
        borderRadius: 8
    },
    search: {
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
        height: 35,
        width: 35,
        borderRadius: 8
    },
    iconSearch: {
        height: 14,
        width: 14,
    },
    iconMenu: {
        height: 42,
        width: 42,
        borderRadius: 8
    },
});
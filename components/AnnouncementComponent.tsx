import React from "react";
import { FlatList, ImageBackground, StyleSheet, Text, View } from "react-native";
import { Spinner } from "native-base";
import NotifyView from "../../components/NotifyView";
import Header from "../../components/Header";
import { getImageURL } from "../../networking/Server";
import { gray_t8, white } from "../../constants/Color";
import Layout from "../../constants/Layout";
import { AnnouncementStore } from "../../stores/AnnouncementStore";

interface AnnouncementComponentProps {
  annLoading: boolean;
  announcement: any;
  navigation: any;
  menu: boolean;
  setMenu: (set: boolean) => void;
  onSearch: (text: string) => void;
  setIsTyping: (text: string) => void;
  flatListRef: React.RefObject<FlatList>;
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
}

const AnnouncementComponent: React.FC<AnnouncementComponentProps> = ({
  annLoading,
  announcement,
  navigation,
  menu,
  setMenu,
  onSearch,
  setIsTyping,
  flatListRef,
  currentIndex,
  setCurrentIndex,
}) => {
  const renderDot = (index: number) => {
    const isActive = index === currentIndex;
    return (
      <View
        key={index}
        style={[
          styles.dot,
          { backgroundColor: isActive ? "green" : "white", width: isActive ? 20 : 10 },
        ]}
      />
    );
  };

  if (annLoading) {
    return (
      <View style={[styles.topBack, styles.noAnnouncementView]}>
        <Spinner color={white} size={20} mt={Layout.screen.width / 2.8} />
      </View>
    );
  }

  if (announcement && announcement.length > 1) {
    return (
      <View style={styles.topBack}>
        <FlatList
          ref={flatListRef}
          data={AnnouncementStore.announcements}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled
          style={styles.topBack}
          renderItem={({ item }) => (
            <ImageBackground
              source={{ uri: getImageURL(item?.image) }}
              style={styles.topBack}
            >
              <Header
                onChangeText={(text: string) => {
                  setIsTyping(text);
                  onSearch(text);
                }}
                menuStatus={menu}
                onMenu={(set: boolean) => setMenu(set)}
              />
              <NotifyView
                navigation={navigation}
                content={item?.content}
                header={item?.header}
                detail={item?.detail}
                detailId={item?.detailId}
                bottomDesc="antalya"
              />
            </ImageBackground>
          )}
          keyExtractor={(item, index) => index.toString()}
        />
        <View style={styles.dotsContainer}>
          {announcement.map((_, index) => renderDot(index))}
        </View>
      </View>
    );
  }

  if (announcement) {
    return (
      <ImageBackground
        source={{ uri: getImageURL(announcement?.image) }}
        style={styles.topBack}
      >
        <Header
          onChangeText={(text: string) => {
            setIsTyping(text);
            onSearch(text);
          }}
          menuStatus={menu}
          onMenu={(set: boolean) => setMenu(set)}
        />
        <NotifyView
          navigation={navigation}
          content={announcement?.content}
          header={announcement?.header}
          detail={announcement?.detail}
          detailId={announcement?.detailId}
          bottomDesc="antalya"
        />
      </ImageBackground>
    );
  }

  return (
    <View style={[styles.topBack, styles.noAnnouncementView]}>
      <Header
        onChangeText={(text: string) => {
          onSearch(text);
          setIsTyping(text);
        }}
        menuStatus={menu}
        onMenu={(set: boolean) => setMenu(set)}
      />
      <Text style={styles.noAnnouncement}>
        {MainStore.language.no_notify}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  topBack: {
    width: Layout.screen.width,
    height: Layout.screen.height / 2.5,
  },
  noAnnouncementView: {
    alignItems: "center",
    backgroundColor: gray_t8,
  },
  noAnnouncement: {
    color: white,
    fontWeight: "bold",
    fontSize: 14,
    textAlign: "center",
    marginTop: 40,
  },
  dotsContainer: {
    flexDirection: "row",
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    bottom: -180,
    left: 20,
    top: 20,
  },
  dot: {
    width: 20,
    top: 20,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
});

export default AnnouncementComponent;
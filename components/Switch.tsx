import React from "react";
import { StyleSheet, View, Pressable } from "react-native";
import { MotiView } from "moti";
import { gray_t4, green_t1, red_t1, red_t2 } from "../constants/Color";

const _colors = {
    active: green_t1,
    inactive: red_t2
}

type ItemType = {
    size: number,
    onPress: () => void,
    isActive: boolean
}

const Item: React.FC<ItemType> = ({ size, onPress, isActive }) => {

    const trackSize = React.useMemo(() => {
        return size * 2;
    }, [size])

    const trackHeight = React.useMemo(() => {
        return size * .9;
    }, [size])

    const knobSize = React.useMemo(() => {
        return size * .5;
    }, [size])

    return (
        <Pressable
            onPress={onPress}
        >
            <View style={styles.itemMain}>
                {/* ALL OF THE MOTI */}
                <MotiView
                    //transition={_transition}
                    from={{
                        backgroundColor: isActive ? _colors.active : _colors.inactive
                    }}
                    animate={{
                        backgroundColor: isActive ? _colors.active : _colors.inactive
                    }}
                    style={[
                        styles.itemMotiOne,
                        {
                            width: trackSize,
                            height: trackHeight,
                            borderRadius: trackHeight / 2,
                        }
                    ]}
                />

                {/* TOP CIRCLE */}
                <MotiView
                    from={{
                        translateX: isActive ? trackSize / 4 : -trackSize / 4,
                    }}
                    style={[
                        styles.itemMotiTwo,
                        {
                            width: size,
                            height: size,
                            borderRadius: size / 2,
                        }
                    ]}
                >
                    {/* IN CIRCLE */}
                    <MotiView
                        from={{
                            width: isActive ? 0 : knobSize,
                        }}
                        animate={{
                            width: isActive ? 0 : knobSize,
                            borderColor: isActive ? _colors.active : _colors.inactive
                        }}
                        style={{
                            width: knobSize,
                            height: knobSize,
                            borderRadius: knobSize / 2,
                            borderWidth: size * 0.1,
                            borderColor: _colors.active
                        }}
                    />
                </MotiView>
            </View>
        </Pressable>
    )
}

const Switch = ({
    setVerify = (isActive: boolean) => {}
}) => {
    const [isActive, setIsActive] = React.useState(false);
    return (
        <View style={styles.main}>
            <Item
                size={20}
                onPress={() => {
                    setIsActive(!isActive)
                    setVerify(!isActive)
                }}
                isActive={isActive}
            />
        </View>
    )
}
export default Switch;

// --- STYLES --- //
const styles = StyleSheet.create({
    main: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    itemMain: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    itemMotiOne: {
        position: 'absolute',
        backgroundColor: _colors.active
    },
    itemMotiTwo: {
        backgroundColor: '#fff',
        alignItems: 'center',
        justifyContent: 'center'
    }
});
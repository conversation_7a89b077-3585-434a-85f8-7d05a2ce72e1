import React from "react";
import { StyleSheet, View, ImageBackground, Text, TouchableOpacity } from "react-native";
import { black_t6, green_t1, white } from "../constants/Color";
import Layout from "../constants/Layout";

export type GiftCardItem = {
    fromChat?: boolean,
    setZoom?: any,
    type: number,
    value: number,
    nameLastName: string,
    code: string,
    item?: any,
    selectedCard?: boolean,
    onPress?: () => void
}

const GiftCardItem: React.FC<GiftCardItem> = ({
    fromChat = false,
    setZoom = () => { },
    type,
    value,
    nameLastName,
    code,
    item = {},
    selectedCard = null,
    onPress  // Yeni prop
}) => {

    const handlePress = () => {
        if (fromChat && onPress) {
            onPress();  // fromChat true ise ve onPress varsa bu fonksiyon çağrılıyor
        } else if (!fromChat && setZoom) {
            setZoom();  // fromChat false ise setZoom fonksiyonu çalışıyor
        }
    };
    

    return (
        <TouchableOpacity
            onPress={handlePress}
            style={
                [
                    styles.mainAlt,
                    {
                        opacity: selectedCard == item ? 1 : 0.6
                    }
                ]
            }
        >
            <ImageBackground
                style={{
                    height: fromChat ? 74 : 206.16,
                    width: fromChat ? 108 : 300.4
                }}
                source={
                    type == 1 ? require('../assets/menu/goldCard.png')
                        : type == 2 ? require('../assets/menu/silverCard.png')
                            : require('../assets/menu/bronzeCard.png')
                }
            >
                <View style={
                    [
                        styles.discountView,
                        {
                            left: fromChat ? 3 : 10,
                            top: fromChat ? 4 : 10
                        }
                    ]
                }
                >
                    <Text style={[styles.discount, { fontSize: fromChat ? 10 : 26, color: type == 2 ? black_t6 : white, }]}>{
                        type == 1 ? "%15"
                            : type == 2 ? "%10"
                                : "%5"
                    }</Text>
                    <Text style={[styles.discounText, { fontSize: fromChat ? 4 : 12, color: type == 2 ? black_t6 : white }]}>discount</Text>
                </View>
                <View style={
                    [
                        styles.centerView,
                        {
                            top: fromChat ? 15 : 40,
                            right: fromChat ? Layout.screen.width / 30 : Layout.screen.width / 8,
                        }
                    ]
                }
                >
                    <Text
                        style={
                            [
                                styles.centerTitle,
                                {
                                    fontSize: fromChat ? 11 : 30
                                }
                            ]
                        }
                    >
                        {
                            type == 1 ? "Gold Card"
                                : type == 2 ? "Silver Card"
                                    : "Bronz Card"
                        }
                    </Text>
                    <Text
                        style={
                            [
                                styles.centerPrice,
                                {
                                    fontSize: fromChat ? 14 : 24,
                                }
                            ]
                        }
                    >
                        {value}₺
                    </Text>
                </View>
                <View
                    style={
                        [
                            styles.nameView,
                            {
                                bottom: fromChat ? 10 : 24,
                                left: fromChat ? 26 : 68
                            }
                        ]
                    }
                >
                    <Text
                        style={
                            [
                                styles.name,
                                {
                                    fontSize: fromChat ? 6 : 13
                                }
                            ]
                        }
                    >
                        {nameLastName}
                    </Text>
                    <Text
                        style={
                            [
                                styles.cardNo,
                                {
                                    fontSize: fromChat ? 6 : 13
                                }
                            ]
                        }
                    >
                        {code}
                    </Text>
                </View>
            </ImageBackground>
        </TouchableOpacity >
    )
}
export default GiftCardItem;

// -- STYLES -- //
const styles = StyleSheet.create({
    mainAlt: {
        marginTop: 5,
        alignItems: 'center'
    },
    discountView: {
        position: 'absolute',
        alignItems: 'center'
    },
    discount: {
        fontFamily: 'MADE TOMMY'
    },
    discounText: {
        fontFamily: 'MADE TOMMY'
    },
    centerView: {
        position: 'absolute',
        alignItems: 'center'
    },
    centerTitle: {
        color: white,
        letterSpacing: 1.05,
        fontFamily: 'MADE TOMMY'
    },
    centerPrice: {
        color: white,
        fontFamily: 'MADE TOMMY',
        letterSpacing: 1.05
    },
    nameView: {
        position: 'absolute'
    },
    name: {
        color: white,
        fontFamily: 'College'
    },
    cardNo: {
        color: white,
        fontFamily: 'College'
    },
});
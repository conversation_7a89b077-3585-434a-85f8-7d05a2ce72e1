import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, ScrollView, TouchableOpacity, Platform } from "react-native";
import Layout from "../constants/Layout";
import { shadow } from "../constants/Shadow";
import { black, black_t4, blue_t1, blue_t4, white, yellow_t1 } from "../constants/Color";
import { get, getImageURL, post } from "../networking/Server";
import { Spinner } from "native-base";
import WheelCountDown from "./WheelCountDown";
import { MainStore } from "../stores/MainStore";
import { socket } from "../networking/Socket";

const dayjs = require('dayjs');

export type RootList = {
    navigation: any,
    conDai: any,
    refresh:any,
    announcements: any[] // Yeni eklenen prop

}
 
const RootList: React.FC<{ refresh: boolean, setRefresh: (value: boolean) => void, showWarning: boolean, setShowWarning: (value: boolean) => void }> = ({
    refresh,
    setRefresh,
    navigation,
    conDai,
    showWarning, 
    setShowWarning,
    announcements,
}) => {

    // -- DATE -- //
    const date = dayjs();
    const day = date.format("DD");
    const month = date.format("MMM");

    // -- CONCERT TIME AND HOLLY CHAT -- //
    const [data, setData]: any = React.useState({});
    const [loading, setLoading] = React.useState(true);

    const [modules, setModules] = useState([]); // Modülleri burada tutuyoruz

    const [participants, setParticipants]: any = React.useState({});
    const [lastImage, setLastImage]: any = React.useState({});
    const [lastName, setLastName]: any = React.useState({});
    const [daywheel, setDay] = React.useState(6);
    const [hour, setHour] = React.useState(24);
    const [minute, setMinute] = React.useState(59);
    const [second, setSecond] = React.useState(59);



    const [activeUsers, setActiveUsers] = React.useState([]);

    const fetchModules = async () => {
        try {
            const response = await post('get-modules', { cityId: MainStore.city.id });
            if (response.type === 'success') {
                setModules(response.modules);
            } else {
                console.error(response.error);
            }
        } catch (error) {
            console.error('Modüller alınırken hata oluştu:', error);
        }
    };

        const [snaps, setSnaps] = React.useState([]);



    const getSnaps = () => {
        try {
                get("/snaps").then((res: any) => {
                    if (res.type == 'success') {
                        setSnaps(res.snaps);
                    }
                }).catch((error: any) => {
                    console.error(error);
                    navigation.pop();
                    
                });
        } catch (e) {
            console.error(e);
            navigation.pop();
        }
    };


    const fetchAllData = async () => {
       
        await Promise.all([
            getConcertTime(),
            getChatInfo(),
            fetchModules(),
            getSnaps(),
        ]);
    };


    React.useEffect(() => {
        fetchAllData();

        socket.on('updateData', () => {
            fetchAllData();
        });

        return () => {
            socket.off('updateData'); 
        };
    }, []);

    React.useEffect(() => {
        if (refresh) {
            getChatInfo();
            setRefresh(false);
        }
    }, [refresh])


    const getChatInfo = () => {
        get('chat').then((res: any) => {
            if (res.type == "success") {
                setActiveUsers(res.activeUsers);
                setLoading(false);
            } else {
                setTimeout(() => {
                    navigation.pop();
                }, 2000);
            }
        })
    }

    const checkModuleActive = (id: string) => {
        // Eğer modül aktif değilse false döner
        return modules.includes(id);
    };
    
    const handleNavigation = (id: string, screenName: string, params = {}) => {
        if (checkModuleActive(id)) {
            navigation.navigate(screenName, params);
        } else {
            setShowWarning(true); // Uyarıyı göster
            setTimeout(() => {
                setShowWarning(false); // Uyarıyı kaldır
            }, 2000);
        }
    };


    // -- CONCERT TIME | HOLLYCHAT PROFILE -- //
    const getConcertTime = () => {
        try {
            get("get-components").then((res: any) => {
                setData(res)
                setLoading(false);
            })
        } catch (e) {
            setLoading(false);
        }
    }



    const renderChatImages = () => {
        if (loading) {
            return <Spinner size={18} mr={8} color={blue_t4} />;
        }

        if (activeUsers.length === 0) {
            return null;
        }

        if (activeUsers[0].message) {
            return (
                <Text style={styles.chatMessage}>
                    {activeUsers[0].message}
                </Text>
            );
        }

        return activeUsers.slice(0, 6).map((item, index) => (
            <Image
                key={index}
                source={{ uri: getImageURL(item.image) }}
                style={[styles.hollyChatImg, { left: -index * 10 }]}
            />
        ));
    }
    
    


    return (
        <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.main}>
                {/* DAILY PROGRAM */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("0", "DailyActivity", { conDai: conDai.filter((item) => item.type == 2), announcements })}
                    style={[styles.mainBack, shadow]}
                >
                    <Image
                        style={styles.leftConcertView}
                        resizeMode="cover"
                        source={require('../assets/root/leftback1.png')}
                    />
                    <Text style={styles.leftBlackText}><Text style={{ fontFamily: 'Helvetica Neue LT Pro' }}>PERFORMANCE</Text> {MainStore.language.hall}</Text>
                    <View style={styles.leftDateView}>
                        <Text style={styles.leftDateNumber}>{day}</Text>
                        <Text style={styles.leftDate}>{month.toUpperCase()}</Text>
                    </View>
                    {/* ATRIUM LOGO */}
                    <Image
                        style={styles.atriumND}
                        resizeMode="cover"
                        source={require('../assets/root/atriumND.png')}
                    />
                    {/*<Text style={styles.leftConcertAreaText}>ATRIUM</Text>*/}
                    <Text style={styles.rightText}>{MainStore.language.daily_events}</Text>
                </TouchableOpacity>

                {/* HOLLY PUAN */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("1", "HollyPuan")}
                    
                    style={[styles.mainBack, shadow, styles.mainStyleTwoBack]}
                >
                    <Image
                        style={styles.hollypuan}
                        source={require('../assets/root/hollypuan.png')}
                    />
                    <Text style={styles.win}>kazanmaya</Text>
                    <Text style={styles.start}>başla!</Text>
                
                </TouchableOpacity>
            </View>
            <View style={styles.main}>
                {/* CONCERT */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("2", "Concerts", { conDai: conDai.filter((item: any) => item.type == 1), announcements })}
                    style={[styles.mainBack, shadow]}
                >

                    <Image
                        style={styles.leftConcertView}
                        resizeMode="cover"
                        source={require('../assets/root/leftback2.png')}
                    />
                    <View style={styles.leftDateView}>
                        <Text style={styles.leftDateNumber}>{!loading ? dayjs(data?.concertDate).format("DD") : "..."}</Text>
                        <Text style={styles.leftDate}>{!loading ? dayjs(data?.concertDate).format("MMM").toUpperCase() : ""}</Text>
                    </View>
                    <Text style={styles.atriumNDtWO}>KONSER</Text>
                    <Text style={styles.rightText}>{MainStore.language.upcoming_concerts}</Text>
                </TouchableOpacity>

                {/* HOLLY TICKET AND HOLLY SHOP */}
                <View>
                    <TouchableOpacity
                        onPress={() =>
                            handleNavigation("3", "Concerts", { conDai: conDai.filter((item: any) => item.type == 1), announcements })}
                        style={[styles.hollyTicketView, shadow]}
                    >
                        <Image
                            resizeMode="contain"
                            style={styles.hollyTicketLogo}
                            source={require('../assets/root/hollyticket.png')}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
    activeOpacity={0.9}
    onPress={() => navigation.navigate("HollyShop",)}
    style={[styles.hollyShopView, shadow]}
>
    <View style={[styles.hollyShopLeftArea, shadow]}>
        <Image
            resizeMode="contain"
            style={styles.hollyShopLeftTshirt}
            source={require('../assets/root/blackShirt.png')}
        />
    </View>
    <Image
        resizeMode="contain"
        style={styles.hollyShopLogo}
        source={require('../assets/root/hollyshop.png')}
    />
</TouchableOpacity>

                </View>
            </View>
            <View style={styles.main}>
                {/* HOLLY CHAT */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("5", "HollyChat")}
                    style={[styles.hollyChatView, { backgroundColor: white }, shadow]}
                >
                    <Image
                        resizeMode="contain"
                        style={styles.hollyChatLogo}
                        source={require('../assets/root/hollyChat.png')}
                    />
                    <View style={styles.hollyChatImgView}>
                    {renderChatImages()}
                    </View>
                </TouchableOpacity>

                {/* HOLLY SNAP */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    
                    onPress={() => handleNavigation("6", "HollySnap" , { snaps })}
                    style={[styles.hollySnapView, { backgroundColor: white }, shadow]}
                >
                    <Image
                        resizeMode="contain"
                        style={styles.hollySnapLogo}
                        source={require('../assets/root/hollySnap.png')}
                    />
                    <Image
                        resizeMode="contain"
                        style={styles.hollySnapShare}
                        source={require('../assets/root/hollySnapShare.png')}
                    />
                </TouchableOpacity>

            </View>

            <View
                style={styles.main}
            >
                {/* SHAMAN */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => navigation.navigate("Shaman")} style={[styles.shamanView, { backgroundColor: white }, shadow]}
                >
                    {<Image
                        resizeMode="contain"
                        style={styles.shamanLogo}
                        source={require('../assets/root/shaman.png')}
                    />}
            
                </TouchableOpacity>

                {/* WHEEL */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("8", "Wheel")}
                    style={
                        [
                            styles.shamanView,
                            {
                                backgroundColor: white,
                                width: Layout.screen.width / 1.6
                            },
                            shadow
                        ]
                    }
                >
                    <WheelCountDown />
                </TouchableOpacity>
            </View>


            <View
                style={styles.main2}
            >

                {/* Wallet */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("9", "Wallet")} 
                    /*  onPress={() => navigation.navigate("Wallet")} */
                    style={
                        [
                            styles.cuzdanView,
                            {

                            },
                            shadow
                        ]
                    }
                    
                >
                <Image
                    resizeMode="contain"
                    style={styles.cuzdanlogo}
                    source={require('../assets/root/cuzdan.png')}
                />
                </TouchableOpacity>


                {/* THEBAR */}
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => handleNavigation("10", "TheBar")}
                    style={[styles.thebarView, { backgroundColor: '#e0d8cd', }, shadow]}
                >
                    <Image
                        resizeMode="contain"
                        style={styles.thebarLogo}
                        source={require('../assets/root/menuyatay.png')}
                    />
                </TouchableOpacity>
            </View>
            {/* HOLLY GARDEN - New Module */}
            <TouchableOpacity
                activeOpacity={0.9}
                onPress={() => handleNavigation("11", "HollyGarden")}
                style={[styles.hollyGardenView, shadow]}
            >
                <View style={styles.hollyGardenContent}>
                    <Image
                        resizeMode="contain"
                        style={styles.hollyGardenLogo}
                        source={require('../assets/root/hollygarden.png')}
                    />
                    
                </View>
            </TouchableOpacity>
        </ScrollView >
    )
}
export default RootList;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignSelf: 'center',
        width: Layout.screen.width / 1.06,
        marginTop: 10,
    },
    main2: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignSelf: 'center',
        width: Layout.screen.width / 1.06,
        marginTop: 10,
    },
    mainBack: {
        width: Layout.screen.width / 2.2,
        height: 127,
        backgroundColor: '#fbfbfb',
        borderRadius: 10
    },
    mainStyleTwoBack: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    leftConcertView: {
        height: 127,
        width: Layout.screen.width / 3.7,
        alignItems: 'center',
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    leftDateView: {
        position: 'absolute',
        marginLeft: Layout.screen.width / 18,
        marginTop: 10,
        alignItems: 'center'
    },
    leftDateNumber: {
        color: white,
        fontFamily: 'MADE TOMMY',
        fontSize: 24
    },
    leftDate: {
        color: white,
        fontFamily: 'MADE TOMMY',
        fontSize: 14
    },
    leftBlackText: {
        left: 0,
        zIndex: 2,
        color: black_t4,
        bottom: Layout.screen.width / 3.92,
        fontSize: 7,
        transform: [{ rotate: '270deg' }]
    },
    atriumND: {
        width: 4.7,
        height: 60,
        left: Layout.screen.width / 5.5,
        bottom: 2,
        position: 'absolute',
        marginBottom: 5
    },
    atriumNDtWO: {
        left: Layout.screen.width / 8.3,
        bottom: 20,
        position: 'absolute',
        color: white,
        fontWeight: 'bold',
        fontSize: 8,
        letterSpacing: 3,
        marginBottom: 5,
        transform: [{ rotate: '-90deg' }], 
    },
    leftConcertAreaText: {
        position: 'absolute',
        left: 58,
        bottom: 20,
        fontSize: 8,
        color: white,
        fontWeight: 'bold',
        transform: [{ rotate: '270deg' }]
    },
    rightText: {
        width: Layout.screen.width / 5,
        position: 'absolute',
        fontSize: 16,
        textAlign: 'right',
        fontFamily: 'MADE TOMMY',
        bottom: 10,
        color: black_t4,
        right: 10
    },
    hollypuan: {
        alignSelf: 'center',
        marginTop: 10,
        height: 54,
        width: 133,
    },
    win: {
        fontFamily: 'MADE TOMMY',
        color: '#0BDC05',
        fontSize: 18,
        height: 25,
        width: 100,
        marginTop: 3,
        marginRight: 30
    },
    start: {
        fontFamily: 'MADE TOMMY',
        fontSize: 28,
        color: '#303030',
        marginLeft: 65,
        height: 35,
        width: 100,
        marginTop: 3,
        top: -5
    },
    hollyTicketView: {
        width: Layout.screen.width / 2.2,
        height: 40,
        backgroundColor: '#fbfbfb',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    hollyTicketLogo: {
        height: 80,
        width: 120
    },
    hollyShopView: {
        width: Layout.screen.width / 2.2,
        height: 77,
        marginTop: 8,
        backgroundColor: '#fbfbfb',
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    hollyShopLeftArea: {
        height: 77,
        width: 40,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
        backgroundColor: yellow_t1,
        justifyContent: 'center'
    },
    hollyShopLeftTshirt: {
        height: 52,
        width: 43,
        marginLeft: 10,
        marginBottom: 10
    },
    hollyShopLogo: {
        height: 20,
        width: Layout.screen.width / 3.3,
        marginRight: 5,
        marginLeft: 0
    },
    hollyChatView: {
        width: Layout.screen.width / 2.2,
        height: 74,
        backgroundColor: 'rgba(255,255,255,0.5)',
        color: black_t4,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    hollyChatLogo: {
        height: 25,
        width: 113,
        marginBottom: 2
    },
    hollyChatImgView: {
        flexDirection: 'row',
        alignSelf: 'center',
        color: black_t4,
        padding: 3
        
    },
    hollyChatImg: {
        height: 25,
        width: 25,
        borderRadius: 12.5,
        borderWidth: 2,
        borderColor: blue_t1
    },
    hollySnapView: {
        width: Layout.screen.width / 2.2,
        height: 74,
        backgroundColor: 'rgba(255,255,255,0.7)',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    hollySnapLogo: {
        height: 23,
        width: 117
    },
    hollySnapShare: {
        height: 23,
        width: 138
    },
    shamanView: {
        width: Layout.screen.width / 3.5,
        height: 94,
        backgroundColor: 'rgba(255,255,255,0.5)',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    thebarView: {
        width: Layout.screen.width / 2.6,
        height: 94,
        backgroundColor: 'rgba(255,255,255,0.5)',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    
    cuzdanView: {
        width: Layout.screen.width / 1.9,
        height: 94,
        backgroundColor: white,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center'
    },
    hollyGardenView: {
        width: Layout.screen.width / 1.07,
        height: 94,
        backgroundColor: white,
        borderRadius: 15,
        alignSelf: 'center',
        marginTop: 10,
        marginBottom: 100,
    },
    hollyGardenContent: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        backgroundColor: white,
        borderRadius: 10
    },
    hollyGardenLogo: {
        height: 50,
        width: 180
    },
    gardenIconsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10
    },
    plantIcon: {
        height: 40,
        width: 80
    },

    cuzdanlogo: {
        height: 60,
    },

    shamanLogo: {
        height: 20,
        width: 120,
    },
    thebarLogo: {
        height: 60,
        width: 150,
    },
    chatMessage:{
       fontSize: 9,
       fontFamily: 'MADE TOMMY',
       textAlign: 'center',
       color: black_t4
    }
});
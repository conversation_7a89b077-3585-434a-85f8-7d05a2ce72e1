import React, { useState } from "react";
import { Image, ImageBackground, Modal, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from "react-native";
import { getImageURL } from "../networking/Server";
import QRCode from 'react-native-qrcode-svg';
import { white, black } from "../constants/Color";

interface EarnedListProps {
    kazandiklarim: any[];
    onTransfer: (prizeId: number, prizeType: string, closeModal: () => void) => void;
}


const EarnedList: React.FC<EarnedListProps> = ({ kazandiklarim, onTransfer }) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedPrize, setSelectedPrize] = useState<any>(null);
    const [isTransferring, setIsTransferring] = useState(false);

    const handleTransfer = () => {
        setIsTransferring(true);
        // Determine if it's a shaman or wheel prize
        const prizeType = selectedPrize.odul === "shamanPrizeData" ? "shaman" : "wheel";
        onTransfer(selectedPrize.id, prizeType, () => {
            setModalVisible(false);
            setIsTransferring(false);
        });
    };


    if (kazandiklarim === null) {
        return null;
    }


    const sortedKazandiklarim = [...kazandiklarim].sort((a, b) => new Date(b.endDate).getTime() - new Date(a.endDate).getTime());

    const formatDate = (date: Date): string => {
        const formattedDate = new Date(date);
        const day = formattedDate.getDate();
        const month = formattedDate.getMonth() + 1;
        const year = formattedDate.getFullYear();
        return `${day < 10 ? "0" + day : day}.${month < 10 ? "0" + month : month}.${year}`;
    };

    const isExpired = (endDate: Date): boolean => {
        const now = new Date();
        const expirationDate = new Date(endDate);
        expirationDate.setDate(expirationDate.getDate() + 30); // 30 gün ekleyelim
        return now.getTime() > expirationDate.getTime();
    };

    const generateQRCodeText = (prize: any): string => {
        // Daha kapsamlı ve güvenli bir QR kodu metni oluştur
        const prizeData = {
            id: prize.id,
            userId: prize.userId,
            value: prize.value || prize.prizeValue || 0, // Ödülün değeri
        };

        // JSON formatında veriyi döndür
        return JSON.stringify(prizeData);
    };

    const handlePress = (prize: any) => {
        setSelectedPrize(prize);
        setModalVisible(true);
    };

    console.log(sortedKazandiklarim)

    return (
        <View style={styles.container}>

            {sortedKazandiklarim.length === 0 ? (
                <Text style={styles.noRewardsText}>Henüz ödül kazanmadınız.</Text>
            ) : (
                sortedKazandiklarim.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.mainAlt}
                        onPress={() => handlePress(item)}
                        disabled={isExpired(new Date(item.endDate)) || item.delivery !== 0} // Şartlı devre dışı bırakma
                    >
                        <View style={styles.containerfour}>
                            <Image
                                source={item.odul === "shamanPrizeData" ?
                                    require('../assets/root/shaman.png') :
                                    require('../assets/root/giftWheelLogoblack.png')}
                                style={styles.imageLogo}
                            />
                        </View>
                        <View style={styles.mainAlttwo}>
                            <View style={styles.containertwo}>
                                <Image
                                    source={{ uri: item.odul === "shamanPrizeData" ?
                                        getImageURL(item.prizeImage) :
                                        getImageURL(item.image || item.prizeImage) }}
                                    style={styles.image}
                                />
                            </View>
                            <View style={styles.containerthree}>
                                <Text style={styles.rightTitle}> {item.prize || item.name || item.prizeName}</Text>
                                <Text
                                    style={[
                                        styles.rightUsed,
                                        { color: item.delivery === 1 || isExpired(new Date(item.endDate)) ? "red" : "green" },
                                    ]}
                                >
                                    {isExpired(new Date(item.endDate)) ? "SÜRESİ DOLDU" : item.delivery === 0 ? "KULLANILMADI" : "KULLANILDI"}
                                </Text>
                                <Text style={styles.rightTopText}>
                                    Kazanılan Tarih : <Text style={{ fontWeight: 'bold' }}>{formatDate(item.endDate)}</Text>
                                </Text>
                                <Text style={styles.rightTopText}>Son Kullanma Tarihi :{" "}<Text style={{ fontWeight: 'bold' }}>{formatDate(new Date(new Date(item.endDate).getTime() + 30 * 24 * 60 * 60 * 1000))}</Text></Text>
                            </View>
                            <ImageBackground style={styles.containerfive} source={require('../assets/initial/yancentik.png')} >
                                <Image
                                    source={{ uri: item.odul === "shamanPrizeData" ?
                                        getImageURL(item.prizeSponsorImage) :
                                        getImageURL(item.sponsorImage || item.sponsorimage) }}
                                    style={styles.imagetwo}
                                />
                            </ImageBackground>
                        </View>
                    </TouchableOpacity>
                ))
            )}
            {selectedPrize && (
                <Modal
                    animationType="slide"
                    transparent={true}
                    visible={modalVisible}
                    onRequestClose={() => {
                        setModalVisible(!modalVisible);
                    }}
                >
                    <TouchableWithoutFeedback onPress={() => setModalVisible(!modalVisible)}>
                        <View style={styles.centeredView}>
                            <View style={styles.modalView}>
                                <Text style={styles.modalTitle}>{selectedPrize.name || selectedPrize.prizeName} {selectedPrize.prize}</Text>
                                <Text style={styles.modalInfo}>
                                    {selectedPrize.type === "hollyPoints"
                                        ? "Holly Points, hesabınıza aktarıldığında bakiyenizde görüntülenebilir."
                                        : "Lütfen QR kodu kasaya göstererek ödülünüzü teslim alınız."
                                    }
                                </Text>

                                {selectedPrize.type === "hollyPoints" ? (
                                    <View style={styles.buttonView}>
                                    <TouchableOpacity
                                        style={styles.transferButton}
                                        onPress={handleTransfer}                                    >
                                        <Text style={styles.transferButtonText}>
                                    {isTransferring ? "Aktarılıyor..." : "Hesaba Aktar"}
                                </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                    style={styles.cancelButton}
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Text style={styles.cancelButtonText}>İptal Et</Text>
                                </TouchableOpacity>
                                    </View>

                                ) : (
                                    <QRCode value={generateQRCodeText(selectedPrize)} size={200} />
                                )}


                            </View>
                        </View>
                    </TouchableWithoutFeedback>
                </Modal>
            )}
        </View>
    );
};

export default EarnedList;

// -- STYLES -- //
const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    containertwo: {
        width: '30%',
        alignItems: "center",
    },
    buttonView:{
        width: '90%',
        flexDirection: "row",
        justifyContent: 'space-between',
    },
    imagetwo: {
        width: 80,
        height: 140,
        top: 10,
        left: 8,
        resizeMode: 'contain',
        transform: [{ rotate: '90deg' }],
    },
    transferButton: {
        backgroundColor: "green",
        paddingVertical: 10,
        borderRadius: 10,
        width: 120
    },
    transferButtonText: {
        color: "white",
        fontSize: 16,
        textAlignVertical: 'center',
        fontWeight: "bold",
        textAlign: "center",
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: "center",
        marginBottom: 10,
        color: black
    },
    modalInfo: {
        fontSize: 14,
        textAlign: "center",
        marginBottom: 20,
        color: black
    },
    cancelButton: {
        backgroundColor: "red",
        paddingVertical: 10,
        borderRadius: 10,
        width: 120
    },
    cancelButtonText: {
        color: "white",
        fontSize: 16,
        textAlignVertical: 'center',
        fontWeight: "bold",
        textAlign: "center",
    },
    containerthree: {
        width: '55%',
        alignSelf: 'center',
        alignItems: "center"
    },
    containerfive: {
        width: 40,
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
        resizeMode: 'cover',
    },
    containerfour: {
        width: '100%',
        height: 50,
        alignSelf: 'center',
        alignItems: "center",
        justifyContent: 'center'
    },
    image: {
        width: 75,
        height: 75,
    },
    imageLogo: {
        height: 20,
        resizeMode: 'contain',
    },
    rightUsed: {
        fontSize: 15,
        fontWeight: 'bold',
        color: "red",
        marginBottom: 10,
        marginTop: 10,
    },
    rightTopText: {
        fontSize: 12,
        color: "black"
    },
    rightTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: "black",
    },
    mainAlt: {
        height: 170,
        borderRadius: 15,
        backgroundColor: "#e0e0e0",
        marginBottom: 20,
        marginLeft: 25,
        marginRight: 25,
        flexDirection: 'column',
        overflow: "hidden"
    },
    mainAlttwo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    centeredView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 22,
    },
    modalView: {
        margin: 20,
        backgroundColor: white,
        borderRadius: 32,
        padding: 35,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5
    },
    textStyle: {
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'MADE TOMMY',
        letterSpacing: 1.1,
        color: 'black',
        top: 15,
    },
    noRewardsText: {
        textAlign: 'center',
        marginVertical: 20,
        fontSize: 16,
        color: 'gray',
    },
});

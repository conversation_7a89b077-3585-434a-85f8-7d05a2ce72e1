import React from "react";
import { Image, Keyboard, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { black, brown_t2, red_t1, white } from "../constants/Color";
import { <PERSON><PERSON>View } from "moti";
import Layout from "../constants/Layout";
import { Input } from "native-base";
import { MainStore } from "../stores/MainStore";
import { Observer } from "mobx-react-lite";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";
import { BackIcon } from "./Svgs";

export type HeaderThree = {
    basket: boolean,
    search: boolean,
    navigation: any,
    onSearch: any,
    headerStatus?: boolean
}

const HeaderThree: React.FC<HeaderThree> = ({
    basket = true,
    search = true,
    navigation,
    onSearch,
    headerStatus
}) => {

    const [searchStatus, setSearchStatus] = React.useState(false);
    const insets = useSafeAreaInsets();

    return (

        <View style={
            [
                styles.main,
                {
                    height: Math.max(insets.bottom, 0) ? 130 : 65,
                    paddingVertical: 5,
                    width: Layout.screen.width / 1,
                    alignSelf: 'center',
                    paddingHorizontal: 0,
                    paddingTop: Math.max(insets.bottom, 0) ? 50 : 10,
                }, // rgba(0,0,0,0.7)
                { backgroundColor: headerStatus ? 'rgba(256,256,256,0.6)' : "transparent", }
            ]
        }
        >
            {/* GOBACK AND LOGO */}
            <View style={styles.leftMain}>
                <TouchableOpacity
                    style={{ padding: 12,  }}
                    onPress={() => {
                        navigation.goBack();
                    }}
                >
                    <BackIcon
                        size={25}
                        color={brown_t2}
                    />
                </TouchableOpacity>
                <Image
                    style={styles.hollyShopLogo}
                    resizeMode="contain"
                    source={require('../assets/root/hollyshop.png')}
                />
            </View>

            {/* SEARCH AND BASKET (IF BASKET) */}
            <View style={styles.rightMain}>

                {/* SEARCH INPUT WITH ANIMATION */}
                {
                    search ?
                        <MotiView
                            from={{
                                width: 0,
                                height: 35
                            }}
                            animate={{
                                height: 35,
                                width: searchStatus ? Layout.screen.width / 1.35 : 30
                            }}
                            style={styles.moti}
                        >
                            <Input
                                placeholder={MainStore.language.search}
                                borderWidth={0}
                                onChangeText={(text) => {
                                    onSearch(text);
                                }}
                                placeholderTextColor={white}
                                color={white}
                            />
                        </MotiView>
                        :
                        <></>
                }

                {/* SEARCH TOUCH */}
                {
                    search ?
                        <TouchableOpacity
                            activeOpacity={1}
                            onPress={() => {
                                setSearchStatus(!searchStatus);
                                Keyboard.dismiss();
                            }}
                            style={styles.searchTouch}
                        >
                            <Image
                                source={require('../assets/header/search.png')}
                                style={styles.searchIcon}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>
                        :
                        <></>
                }

                {/* BASKET */}
                {
                    basket ?
                        <TouchableOpacity
                            onPress={() => {
                                navigation.navigate("Basket")
                            }}
                        >
                            <Image
                                style={styles.basketIcon}
                                resizeMode="contain"
                                source={require('../assets/header/basket.png')}
                            />
                            <View style={styles.badgeView}>
                                <Observer render={() => <Text style={styles.badgeText}>{MainStore.basket?.length}</Text>} />
                            </View>
                        </TouchableOpacity>
                        :
                        <></>
                }
            </View>
        </View>
    )
}
export default HeaderThree;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        position: 'absolute',
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 50,
        marginHorizontal: 20
    },
    leftMain: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    hollyShopLogo: {
        width: 181,
        height: 21,
        left: -10
    },
    rightMain: {
        flexDirection: 'row',
        alignItems: 'center',
        right : 22
    },
    moti: {
        backgroundColor: brown_t2,
        right: 30,
        justifyContent: 'center',
        borderRadius: 8,
        position: 'absolute'
    },
    searchTouch: {
        width: 35,
        height: 35,
        borderRadius: 8,
        backgroundColor: brown_t2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    searchIcon: {
        height: 14,
        width: 14,
        right: 0
    },
    basketIcon: {
        width: 20,
        height: 19.57,
        marginLeft: 10
    },
    badgeView: {
        height: 15,
        width: 15,
        borderRadius: 7.5,
        backgroundColor: red_t1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: -10,
        top: -10
    },
    badgeText: {
        color: white,
        fontSize: 10
    },
});
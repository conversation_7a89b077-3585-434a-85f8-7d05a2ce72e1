import React from "react";
import { StyleSheet, View } from "react-native";
import { blue_t1, green_t1, green_t2, white, yellow_t1 } from "../constants/Color";
import { MotiView } from "moti";

export type ConcertDetailAnimation = {
    status: number
}

const ConcertDetailAnimation: React.FC<ConcertDetailAnimation> = ({
    status
}) => {
    return (
        <View style={[
            styles.main,
            {
                borderRightColor: status > 2 ? blue_t1 : '#C8C8C8',
                borderRightWidth: status > 2 ? 6 : 4,
                borderBottomWidth: status > 1 ? 6 : 4,
                borderBottomColor: status == 2 ? yellow_t1 : status == 3 ? blue_t1 : '#C8C8C8',
                borderLeftColor: status == 0 ? green_t1 : status == 2 ? yellow_t1 : status == 3 ? blue_t1 : green_t2,
            }
        ]}>
            <MotiView
                from={{
                    borderWidth: 6,
                    //@ts-ignore
                    borderBottomColor: 'transparent',
                    borderRightColor: 'transparent',
                    borderColor: 'gray',
                }}

                animate={{
                    borderWidth: 6,
                    //@ts-ignore
                    borderLeftColor: status == 0 ?
                        green_t1
                        :
                        status == 2 ?
                            yellow_t1
                            :
                            status == 3 ?
                                blue_t1
                                :
                                green_t2
                    ,
                    right: status == 1 ? -45 :
                        status == 2 ? -88
                            :
                            status == 3 ?
                                -58
                                : -3,
                    top: status == 1 ? -28 :
                        status == 2 ? -92
                            : status == 3 ?
                                -130
                                : -4,
                    transform: status == 1 ? [{ rotate: '-40deg' }]
                        :
                        status == 2 ?
                            [{ rotate: '-95deg' }]
                            :
                            status == 3 ?
                                [{ rotate: '-135deg' }]
                                :
                                [{ rotate: '0deg' }],
                    //@ts-ignore
                    borderBottomColor: 'transparent',
                    borderRightColor: "transparent"
                }}

                transition={{
                    type: "timing",
                    duration: 200
                }}

                style={styles.moti}
            >

                {/* DOT */}
                <View
                    style={
                        [styles.dot,
                        {
                            borderColor: status == 1 ? green_t2 : status == 2 ? yellow_t1 : status == 3 ? blue_t1 : green_t1
                        }
                        ]
                    }
                />
            </MotiView>
        </View>
    )
}
export default ConcertDetailAnimation;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        borderWidth: 4,
        borderTopColor: 'transparent',
        width: 303,
        height: 140,
        top: -100,
        alignSelf: 'center',
        borderBottomLeftRadius: 500,
        borderBottomRightRadius: 500,
        borderColor: '#C8C8C8',
        borderLeftWidth: 6,
        zIndex: 1,
    },
    moti: {
        width: 303,
        height: 140,
        top: -2,
        borderTopColor: 'transparent',
        alignSelf: 'center',
        borderBottomLeftRadius: 500,
        borderBottomRightRadius: 500,
        zIndex: 2,
        position: 'absolute',
        bottom: 0
    },
    dot: {
        width: 16,
        height: 16,
        borderRadius: 8,
        borderWidth: 4,
        backgroundColor: white,
        top: 88,
        left: 33,
    }
})
import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, {
  Path,
  G,
  Text as SvgText,
  LinearGradient,
  Stop,
  Defs,
  Image
} from 'react-native-svg';
import PropTypes from 'prop-types';

const CreditCard = ({ cardNumber = '1234 5678 1234 5678', type = 1 }) => {
  // Format the card number with spaces if it's provided as a single string
  const formatCardNumber = (number: string): string => {
    // Remove any existing spaces
    const cleaned = number.replace(/\s/g, '');

    // Add spaces every 4 characters
    return cleaned.match(/.{1,4}/g)?.join(' ') || number;
  };

  const displayNumber = formatCardNumber(cardNumber);

  return (
    <View style={styles.container}>
      <Svg width="100%" height="100%" viewBox="0 0 242.6 153">
        {/* Background */}
        <Path
          d="M231.2,153H11.4C5.1,153,0,147.9,0,141.6V11.4C0,5.1,5.1,0,11.4,0h219.8c6.3,0,11.4,5.1,11.4,11.4v130.2 C242.6,147.9,237.5,153,231.2,153z"
          fill="#000000"
        />

        {/* Google Play and App Store logos at the top */}
        <Image
          x="13"
          y="0"
          width="100"
          height="40"
          href={require('../assets/googleplayappstore.png')}
        />

        <Image
          x="150"
          y="0"
          width="80"
          height="40"
          href={require('../assets/hollyentlogo.png')}
        />

        {/* HollyPay logo in the center */}
        <Image
          x="45"
          y="50"
          width="160"
          height="40"
          href={require('../assets/hollypaycard.png')}
        />

        {/* Card Number - Now using the prop */}
        <SvgText
          x="13.4316"
          y="116.0283"
          fill="#FFFFFF"
          fontFamily="MADETOMMY"
          fontWeight="bold"
          fontSize="13.3702"
        >
          {displayNumber}
        </SvgText>

        {/* Website URL at bottom */}
        <SvgText
          x="170.8477"
          y="143.2324"
          fill="#FFFFFF"
          fontFamily="MADETOMMY"
          fontWeight="bold"
          fontSize="7.3625"
        >
          hollystone.com.tr
        </SvgText>

        {/* Wireless payment icon */}
        <G>
          <Path
            d="M222.1,118.5c1-2.1,1.5-4.4,1.5-6.8c0-2.4-0.5-4.8-1.5-6.8 M218.8,117c0.7-1.6,1.2-3.4,1.2-5.3 s-0.5-3.7-1.2-5.4 M215.6,115.4c0.5-1.2,0.8-2.4,0.8-3.7s-0.3-2.6-0.8-3.7 M212.3,113.8c0.3-0.6,0.5-1.4,0.5-2.1s-0.2-1.5-0.5-2.2"
            fill="none"
            stroke="#FFFFFF"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="133.3333"
          />
        </G>
      </Svg>
    </View>
  );
};

// PropTypes for type checking
CreditCard.propTypes = {
  cardNumber: PropTypes.string,
  type: PropTypes.number
};

const styles = StyleSheet.create({
  container: {
    width: 320,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default CreditCard;
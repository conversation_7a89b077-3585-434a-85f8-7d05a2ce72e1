import React, { useEffect, useRef, useState } from "react";
import {
  Modal,
  View,
  Image,
  TouchableWithoutFeedback,
  Text,
  Animated,
  Dimensions,
  StyleSheet,
  PanResponder,
  Platform,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import { getImageURL, post } from "../networking/Server";

const { width, height } = Dimensions.get("window");
const PRESS_TIMEOUT = 150;
const SWIPE_THRESHOLD = 50;

interface Snap {
  image: string;
  ownerFirstName?: string;
  ownerImage?: string;
  ownerLastName?: string;
  timestamp?: string;
  isOwner?: string;
}

interface SnapViewProps {
  snaps: Snap[];
  isVisible: boolean;
  onClose: () => void;
}

const SnapView: React.FC<SnapViewProps> = ({ snaps, isVisible, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const progress = useRef(new Animated.Value(0)).current;
  const longPressTimeout = useRef<NodeJS.Timeout>();
  const animation = useRef<Animated.CompositeAnimation>();
  const progressInterpolate = progress.interpolate({
    inputRange: [0, width],
    outputRange: ["0%", "100%"],
  });
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    setImageLoaded(false); // Yeni index geldiğinde
  }, [currentIndex]);
  
  <Image
    source={{ uri: getImageURL(snaps[currentIndex]?.image) }}
    style={styles.snapImage}
    onLoad={() => setImageLoaded(true)}
  />
  
  {!imageLoaded && (
    <Image
      source={{ uri: getImageURL(snaps[currentIndex - 1]?.image) }}
      style={[styles.snapImage, StyleSheet.absoluteFill]}
      blurRadius={10} // İstersen bulanıklık ekle
    />
  )}

  useEffect(() => {
    if (!isVisible) {
      resetStates();
    }
  }, [isVisible]);
  
  const resetStates = () => {
    setCurrentIndex(0);
    setIsPaused(false);
    setIsDragging(false);
    progress.setValue(0);
    setImageLoaded(false);
  };
  

  useEffect(() => {
    if (isVisible) {
      startProgress();
    }
    return () => {
      stopAndCleanProgress();
    };
  }, [currentIndex, isVisible]);

  useEffect(() => {
    if (isPaused || isDragging) {
      animation.current?.stop();
    } else {
      startProgress();
    }
  }, [isPaused, isDragging]);

  const stopAndCleanProgress = () => {
    animation.current?.stop();
    clearTimeout(longPressTimeout.current);
  };

  const startProgress = () => {
    stopAndCleanProgress();
    progress.setValue(0);

    animation.current = Animated.timing(progress, {
      toValue: width,
      duration: 5000,
      useNativeDriver: false,
    });

    animation.current.start(({ finished }) => {
      if (finished && !isPaused && !isDragging) {
        goToNext();
      }
    });
  };

 
  const goToNext = () => {
    if (animation.current) {
      animation.current.stop();
    }
  
    if (currentIndex < snaps.length - 1) {
      setCurrentIndex((prev) => prev + 1);
    } else {
      onClose();
    }
  };
  

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleTouchStart = () => {
    setIsPaused(true);
    longPressTimeout.current = setTimeout(() => {
      setIsPaused(true);
    }, PRESS_TIMEOUT);
  };

  const handleTouchEnd = (side: 'left' | 'right') => {
    clearTimeout(longPressTimeout.current);
    setIsPaused(false);

    if (!isDragging) {
      if (side === 'left') {
        goToPrevious();
      } else {
        goToNext();
      }
    }
  };

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, { dx, dy }) => {
        return Math.abs(dx) > 10 && Math.abs(dx) > Math.abs(dy);
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
        setIsPaused(true);
      },
      onPanResponderMove: (_, { dx }) => {
        if (Math.abs(dx) > SWIPE_THRESHOLD) {
          setIsPaused(true);
        }
      },
      onPanResponderRelease: (_, { dx, vx }) => {
        setIsDragging(false);
        setIsPaused(false);

        if (Math.abs(dx) > SWIPE_THRESHOLD || Math.abs(vx) > 0.5) {
          if (dx > 0) {
            goToPrevious();
          } else {
            goToNext();
          }
        } else {
          startProgress();
        }
      },
      onPanResponderTerminate: () => {
        setIsDragging(false);
        setIsPaused(false);
        startProgress();
      },
    })
  ).current;

  const handleDeleteSnap = () => {
    post(`snaps/snap-delete`, { id: snaps[currentIndex].id }).then((res: any) => {
      if (res.type === 'success') {
        snaps.splice(currentIndex, 1);
        if (currentIndex >= snaps.length) {
          onClose();
        } else {
          setCurrentIndex(currentIndex);
        }
      }
    }).catch((error) => {
      console.error("Snap silinemedi:", error);
    });
  };

  const renderProgressBars = () => (
    <View style={styles.progressBarContainer}>
      {snaps.map((_, index) => (
        <View key={index} style={[styles.progressBarBackground, { marginHorizontal: 2 }]}>
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: index === currentIndex 
                  ? progressInterpolate 
                  : index < currentIndex 
                    ? "100%" 
                    : "0%",
              },
            ]}
          />
        </View>
      ))}
    </View>
  );

  return (
    <Modal  visible={isVisible} animationType="fade" transparent>
      <View style={styles.container} {...panResponder.panHandlers}>
        {renderProgressBars()}

        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Image 
              source={{ uri: `https://api.hollystone.com.tr/resources/images/${snaps[currentIndex]?.ownerImage}` }}
              style={styles.avatar} 
            />
            
            <View style={styles.headerInfo}>
              <Text style={styles.username}>
              {(snaps[currentIndex]?.ownerFirstName || "") + " " + (snaps[currentIndex]?.ownerLastName || "")}
              </Text>
              <Text style={styles.timestamp}>
                {snaps[currentIndex]?.timestamp || ""}
              </Text>
            </View>
          </View>
          <TouchableWithoutFeedback onPress={onClose}>
            <View style={styles.closeButton}>
              <Text style={styles.closeText}>✕</Text>
            </View>
          </TouchableWithoutFeedback>
          
        </View>

        <TouchableWithoutFeedback
          onPressIn={handleTouchStart}
          onPressOut={() => handleTouchEnd('left')}
        >
          <View style={styles.leftTouchable} />
        </TouchableWithoutFeedback>

        <TouchableWithoutFeedback
          onPressIn={handleTouchStart}
          onPressOut={() => handleTouchEnd('right')}
        >
          <View style={styles.rightTouchable} />
        </TouchableWithoutFeedback>

        <Image
          source={{ uri: `https://api.hollystone.com.tr/resources/images/${snaps[currentIndex]?.image}` }}
          style={styles.snapImage}
        />
        {snaps[currentIndex]?.isOwner && (
          <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSnap}>
            <Image
              source={require("../assets/root/chattrash.png")}
              style={styles.deleteIcon}
            />
          </TouchableOpacity>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  progressBarContainer: {
    flexDirection: "row",
    position: "absolute",
    top: Platform.OS === 'ios' ? 50 : 30,
    width: "95%",
    alignSelf: "center",
    zIndex: 1,
  },
  deleteButton: {
    position: 'absolute',
    bottom: 30,  // Sağ alt köşeye yerleştirmek için
    right: 20,
    padding: 10,
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 30,
    zIndex: 10, // Görünür olması için z-index artırıldı
},
  deleteIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFF',
  },
  progressBarBackground: {
    flex: 1,
    height: 2,
    backgroundColor: "rgba(255,255,255,0.3)",
    borderRadius: 1,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: "#FFF",
    borderRadius: 1,
  },
  header: {
    position: "absolute",
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 0,
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
    zIndex: 2,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerInfo: {
    marginLeft: 10,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: "#FFF",
  },
  username: {
    color: "#FFF",
    fontSize: 14,
    fontWeight: "600",
  },
  timestamp: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 12,
    marginTop: 2,
  },
  leftTouchable: {
    position: "absolute",
    width: "30%",
    height: "100%",
    left: 0,
    zIndex: 1,
  },
  rightTouchable: {
    position: "absolute",
    width: "70%",
    height: "100%",
    right: 0,
    zIndex: 1,
  },
  snapImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  closeButton: {
    padding: 8,
  },
  closeText: {
    color: "#FFF",
    fontSize: 20,
    fontWeight: "600",
  },
});

export default SnapView;
import React from "react";
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, FlatList } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  useDerivedValue
} from "react-native-reanimated";
import { useNavigation } from '@react-navigation/native';
import { white } from "../constants/Color";
import ConcertTicket from "../screens/Root/Menu/ConcertTicket";
const dayjs = require('dayjs');

const WINDOW_WIDTH = Dimensions.get("window").width;
const CARD_LENGTH = WINDOW_WIDTH * 0.9;
const SPACING = WINDOW_WIDTH * 0.01;
const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);

interface ItemProps {
  index: number;
  scrollX: Animated.SharedValue<number>;
  item: any;
  type: number;
 
}

function Item({ index, scrollX, item, type }: ItemProps) {
  const navigation = useNavigation();
  const animatedStyle = useAnimatedStyle(() => {
    const inputRange = [
      (index - 1) * (CARD_LENGTH + SPACING),
      index * (CARD_LENGTH + SPACING),
      (index + 1) * (CARD_LENGTH + SPACING)
    ];

    const scale = interpolate(
      scrollX.value,
      inputRange,
      [0.9, 1, 0.9],
      Extrapolate.CLAMP
    );

    const opacity = interpolate(
      scrollX.value,
      inputRange,
      [0.5, 1, 0.5],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ scale }],
      opacity,
    };
  });

  return (
    <Animated.View style={[styles.card, animatedStyle]}>
      <TouchableOpacity
        onPress={() => {
          if (item.status !== 'kullanildi') {
            navigation.navigate('TicketPage', {
              eventTitle: item.concertName,
              date: item.concertDate,
              image: item.concertImage,
              qrCodeUri: item.qrCode,
              giftStatus: item.giftStatus,
              payment: item.payment,
              status: item.status,
              ticketId: item.id,
              city: item.city.name,
              lat: item.city.lat,
              long: item.city.long,
              gatedate: item.gatedate,
              refundable: item.refundable,
            });
          }
        }}
        disabled={item.status === 'kullanildi'}
        style={{ marginHorizontal: 5 }}
      >
        <ConcertTicket
          eventTitle={item.concertName}
          image={item.concertImage}
          date={dayjs(item?.concertDate)?.format("DD MMMM dddd")}
          qrCode={item.qrCode}
          status={item.status}
        />
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function Carousel({ data, type }: any) {
  const scrollX = useSharedValue(0);

  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollX.value = event.contentOffset.x;
    },
  });

  if (data.length === 0) {
    return (
      <View style={styles.noTicketContainer}>
        <Text style={styles.noTicketText}>
          {type === 2 ? "Aktif biletiniz bulunamadı" : "Henüz Bilet Kullanılmadı"}
        </Text>
      </View>
    );
  }

  return (
    <Animated.View>
      <AnimatedFlatList
        data={data}
        horizontal
        scrollEventThrottle={16}
        showsHorizontalScrollIndicator={false}
        decelerationRate={0.8}
        snapToInterval={CARD_LENGTH + SPACING}
        disableIntervalMomentum
        disableScrollViewPanResponder
        renderItem={({ item, index }) => (
          <Item
            type={type}
            item={item}
            index={index}
            scrollX={scrollX}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
        onScroll={onScroll}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: CARD_LENGTH,
    height: 180,
    overflow: "hidden",
    borderRadius: 15,
    alignSelf: 'center',
  },
  noTicketContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 150,
    width: '100%',
  },
  noTicketText: {
    fontSize: 14,
    color: white,
    textAlign: 'center',
    padding: 20,
    fontFamily: 'MADE TOMMY',
  },
});

import React from "react";
import { Image, ImageBackground, StyleSheet, View, Text, TouchableOpacity, Platform } from "react-native";
import Layout from "../constants/Layout";
import { black, white } from "../constants/Color";
import { getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";

export type SpecialProducts = {
    navigation: any,
    products: any
}

const SpecialProducts: React.FC<SpecialProducts> = ({
    navigation,
    products
}) => {

    if (!products[0] || !products[1]) {
        return (<></>)
    }
    return (
        <View style={styles.main}>

            {/* SPECIAL TOP */}
            <ImageBackground
                style={styles.specialTop}
                resizeMode="contain"
                source={require('../assets/root/specialTop.png')}

            >
                <Image
                    source={{ uri: getImageURL(products[0]?.images[0]) }}
                    style={styles.blackTshirt}
                    resizeMode="contain"
                />
                <View style={styles.specialTopRightSide}>
                    <View style={styles.sideView}>
                        <Text style={styles.title}>{products[0]?.name.substring(0, 8)}...</Text>
                        {
                            products[0]?.hollyPoints ?
                                <Image
                                    source={require('../assets/root/hP.png')}
                                    style={styles.specialHp}
                                    resizeMode="contain"
                                />
                                :
                                <></>
                        }

                    </View>
                    <Text>{products[0]?.price}₺</Text>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.navigate("HollyShopDetail", { productId: products[0].id });
                        }}
                        style={styles.touch}
                    >
                        <Text style={styles.touchText}>{MainStore.language.see_detail}</Text>
                    </TouchableOpacity>
                </View>
            </ImageBackground>

            {/* SPECIAL BOTTOM */}
            <ImageBackground
                style={styles.specialBottom}
                resizeMode="contain"
                source={require('../assets/root/specialBottom.png')}

            >

                <View style={styles.specialBottomLeftSideView}>
                    <View style={styles.sideView}>
                        <Text style={styles.title}>{products[1]?.name?.substring(0, 8)}...</Text>
                        {
                            products[1]?.hollyPoints ?
                                <Image
                                    source={require('../assets/root/hP.png')}
                                    style={styles.specialHp}
                                    resizeMode="contain"
                                />
                                :
                                <></>
                        }
                    </View>
                    <Text>{products[1]?.price}₺</Text>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.navigate("HollyShopDetail", { productId: products[1].id });
                        }}
                        style={styles.touchBottom}>
                        <Text style={styles.touchTextTwo}>{MainStore.language.see_detail}</Text>
                    </TouchableOpacity>
                </View>
                <Image
                    source={{ uri: getImageURL(products[1]?.images[0]) }}
                    style={{
                        height: 163,
                        width: 136,
                        marginBottom: 30,
                        right: Layout.screen.width > 400 ?
                            Layout.screen.width / 9
                            :
                            Layout.screen.width / 16,
                    }}
                    resizeMode="contain"
                />
            </ImageBackground>
        </View>
    )
}
export default SpecialProducts;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        alignItems: 'center'
    },
    specialTop: {
        height: 153,
        width: Layout.screen.width / 1.1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    blackTshirt: {
        height: 163,
        width: 136,
        left: Layout.screen.width > 400 ?
            Layout.screen.width / 9
            :
            Layout.screen.width / 16,
        marginTop: 30
    },
    specialTopRightSide: {
        right: Layout.screen.width > 400 ?
            Layout.screen.width / 9
            :
            Layout.screen.width / 9,
        width: 100
    },
    sideView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    title: {
        fontWeight: 'bold',
        color: black,
    },
    specialHp: {
        height: 24,
        width: 21.66,
        position: 'absolute',
        left: 65,
        marginLeft: 10
    },
    touch: {
        marginTop: Platform.OS == "android" ? 43 : 49,
        height: 28,
        alignItems: 'center',
        justifyContent: 'center',
        width: Layout.screen.width / 2.6,
        right: 15
    },
    touchBottom: {
        marginTop: Platform.OS == "android" ? 24 : 30,
        paddingLeft: 20,
        height: 28,
        alignItems: 'center',
        justifyContent: 'center',
        width: Layout.screen.width / 2.6,
        right: 30
    },
    touchText: {
        color: white,
        fontSize: 12,
    },
    touchTextTwo: {
        color: white,
        fontSize: 12,
        marginRight: 16
    },
    specialBottom: {
        height: 153,
        width: Layout.screen.width / 1.1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 20
    },
    specialBottomLeftSideView: {
        left: Layout.screen.width > 400 ?
            Layout.screen.width / 9
            :
            Layout.screen.width / 9,
        width: 100
    },
});
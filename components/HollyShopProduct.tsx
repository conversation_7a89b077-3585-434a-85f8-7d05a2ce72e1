import React from "react";
import { StyleSheet, View, Text, Image, Platform, StatusBar, TouchableOpacity } from "react-native";
import Layout from "../constants/Layout";
import { black, black_t2, brown_t2, gray_t1, gray_t2, green_t2, red_t2, white } from "../constants/Color";
import { SafeAreaInsetsContext } from "react-native-safe-area-context";
import { shadow } from "../constants/Shadow";
import { getImageURL } from "../networking/Server";

export type HollyShopProduct = {
    setModal: any,
    details: any,
    discountPercentage: number,
    image: string,
    options: any,
    price: number,
    discountedPrice: number,
    hollyPoints: number,
    selectAtt: string,
    onPressSizes: any
}

const HollyShopProduct: React.FC<HollyShopProduct> = ({
    setModal = (set: boolean) => { },
    details,
    discountPercentage,
    image,
    price,
    options,
    discountedPrice,
    hollyPoints,
    selectAtt,
    name,
    onPressSizes
}) => {

    const detailsAlt = (JSON.stringify(details).replaceAll("{", "").replaceAll("}", "").replaceAll("[", "").replaceAll("]", "").replaceAll('"', '').split(","))



    return (
        <>
            <View style={styles.main}>
                {/* PRODUCT DETAIL */}
                <View>
                    <Text style={styles.normalTitle}>{name}</Text>
                    <Text style={styles.withHpView}>{detailsAlt[0].split(":")[0]}</Text>
                    {
                        detailsAlt.map((item, index) => {
                            const data = item.split(":")
                            if (index == 0)
                                
                                return (
                                    <View
                                        key={index}
                                        style={styles.withHpProperty}
                                    >
                                        <Text style={styles.withHpTitle}>{data[1]}</Text>
                                        <Image
                                            style={styles.hP}
                                            resizeMode="contain"
                                            source={require('../assets/root/hP.png')}
                                        />
                                        <Text style={styles.hPuan}>{hollyPoints}</Text>
                                    </View>
                                )

                            return (
                                <View key={index}>
                                    <Text style={styles.normalProperty}>{data[0]}</Text>
                                    <Text style={styles.normalTitle}>{data[1]}</Text>
                                </View>
                            )
                        })
                    }
                    <Text style={styles.normalProperty}>Fiyat</Text>
                    <Text style={[styles.normalTitle, { textDecorationLine: discountedPrice ? "line-through" : "none", textDecorationColor: red_t2, fontSize: discountedPrice ? 12 : 16 }]}>{price}₺</Text>
                    {
                        discountedPrice ?
                            <Text style={styles.normalTitle}>{discountedPrice}₺</Text>
                            :
                            <></>
                    }
                </View>
                <View>

                    {/* PRODUCT */}
                    <SafeAreaInsetsContext.Consumer>
                        {(insets) => <TouchableOpacity onPress={() => { setModal() }}>
                            <Image
                                source={{ uri: getImageURL(image) }}
                                style={[
                                    styles.rightImg,
                                    {
                                        marginTop: insets?.top ? 0 : 60
                                    }
                                ]}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>}

                    </SafeAreaInsetsContext.Consumer>

                    {/* DISCOUNT */}
                    {
                        discountPercentage ?
                            <View style={styles.discountView}>
                                <Text style={styles.discount}>%{discountPercentage}</Text>
                            </View>
                            :
                            <></>
                    }
                </View>
            </View>

            {/* SIZES (IF WEAR CATEGORIES ) */}
            <View style={styles.sizesView}>

                {
                    options?.map((item: any, index: React.Key) => {
                        return (
                            <TouchableOpacity
                                key={index}
                                onPress={() => {
                                    onPressSizes(item);
                                }}
                                style={[
                                    selectAtt == item ? styles.sizesSelectedView : styles.sizesNoneSelectedView,
                                    shadow
                                ]}
                            >
                                <Text style={selectAtt == item ? styles.sizesSelectedText : styles.sizesNoneSelectedText}>{item}</Text>
                            </TouchableOpacity>
                        )
                    })
                }
            </View>
        </>
    )
}
export default HollyShopProduct;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        flexDirection: 'row',
        width: Layout.screen.width / 1.2,
        paddingVertical: 10,
        alignSelf: 'center',
        justifyContent: 'space-between'
    },
    withHpView: {
        color: gray_t1,
        fontSize: 13
    },
    withHpProperty: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    withHpTitle: {
        color: black,
        fontWeight: 'bold',
        fontSize: 20
    },
    hP: {
        height: 17.37,
        width: 15.68,
    },
    hPuan: {
        color: green_t2,
        fontSize: 20,
        fontWeight: 'bold',
        marginLeft: 8
    },
    normalProperty: {
        color: gray_t1,
        fontSize: 13,
        marginTop: 20
    },
    normalTitle: {
        color: black,
        fontWeight: 'bold',
        fontSize: 20
    },
    rightImg: {
        height: 180,
        width: 150,
    },
    discountView: {
        height: 48,
        width: 48,
        backgroundColor: red_t2,
        position: 'absolute',
        bottom: 25,
        left: -40,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center'
    },
    discount: {
        color: white,
        fontSize: 14,
        fontWeight: 'bold'
    },
    sizesView: {
        flexDirection: 'row',
        width: Layout.screen.width / 1.2,
        paddingVertical: 10,
        alignSelf: 'center',
        left: -10
    },
    sizesNoneSelectedView: {
        height: 30,
        paddingHorizontal: 10,
        backgroundColor: gray_t2,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 10
    },
    sizesNoneSelectedText: {
        color: brown_t2,
        fontWeight: 'bold',
        fontSize: 14
    },
    sizesSelectedView: {
        height: 30,
        paddingHorizontal:10,
        backgroundColor: brown_t2,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 10
    },
    sizesSelectedText: {
        color: gray_t2,
        fontWeight: 'bold',
        fontSize: 14
    },
});
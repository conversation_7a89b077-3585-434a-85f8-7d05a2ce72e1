import React from "react";
import { StyleSheet, View, Image } from "react-native";
import { shadow } from "../constants/Shadow";
import { black, white } from "../constants/Color";
import Layout from "../constants/Layout";
import { TextArea } from "native-base";

export type SettingsSecondInput = {
    icon: any,
    number: number,
    setText: any,
    placeholder?: string
}

const SettingsSecondInput: React.FC<SettingsSecondInput> = ({
    number = 100,
    icon = require('../assets/menu/message.png'),
    setText = () => { },
    placeholder = ""
}) => {
    return (
        <View style={[styles.main, shadow, { minHeight: number }]}>
            {/* ICON */}
            <View style={[styles.iconBack, shadow]}>
                {
                    typeof icon == "number" ?
                        <Image
                            style={{
                                width: 21,
                                height: 21
                            }}
                            resizeMode="contain"
                            source={icon}
                        />
                        :
                        icon
                }
            </View>

            {/* @ts-ignore */}
            <TextArea
                borderWidth={0}
                placeholder={placeholder}
                mt={2}
                backgroundColor={'transparent'}
                onChange={(event) => setText(event.nativeEvent.text)}
                minHeight={
                    number > 100 ?
                        number - (number / 5)
                        :
                        number - (number / 3)
                }
                fontSize={14}
                color={black}
            />
        </View>
    )
}
export default SettingsSecondInput;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        backgroundColor: white,
        padding: 10,
        marginLeft: 5,
        width: (Layout.screen.width / 1.2) - 4,
        borderRadius: 10
    },
    iconBack: {
        height: 43,
        width: 43,
        position: 'absolute',
        top: -15,
        left: -5,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 22.5,
        zIndex: 2
    },
});
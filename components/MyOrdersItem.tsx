import React from "react";
import { StyleSheet, View, TouchableOpacity, Image, Text } from "react-native";
import { black_t3, brown_t2, gray_t6, white } from "../constants/Color";
import Layout from "../constants/Layout";
import { shadow } from "../constants/Shadow";
import { getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";

export type MyOrdersItem = {
    name: string,
    image: any,
    status: number,
    navigation: any,
    orderId: number
}

const MyOrdersItem: React.FC<MyOrdersItem> = ({
    name = "",
    image = "",
    status = 0,
    navigation,
    orderId = 0
}) => {

    return (
        <View style={styles.main}>

            {/* PRODUCT ITEM ANIMATION */}
            <View style={[styles.grayView, shadow]}>
                <View style={[styles.imgWhiteView, shadow]}>
                    <Image
                        source={{ uri: getImageURL(image)}}
                        style={styles.img}
                        resizeMode="contain"
                    />
                </View>
                <View style={styles.rightView}>

                    {/* TITLE */}
                    <Text style={styles.title}>{name}</Text>

                    {/* STATUS, RETURN CODE AND BUTTONS VIEW */}
                    <View style={styles.priceButtonView}>
                        {
                            status != 4 ?
                                <Text style={styles.price}>{
                                    status == 0 ? "Ödeme bekleniyor"
                                        : 1 ?
                                            "Hazırlanıyor"
                                            :
                                            status == 2 ?
                                                "Teslim Edildi"
                                                :
                                                status == 3 ?
                                                    "Kargoda"
                                                    :
                                                    status == 4 ?
                                                        ""
                                                        :
                                                        "İade Edildi"
                                }</Text>
                                :
                                <></>
                        }

                        {/* RETURN CODE */}
                        {
                            status == 4 ?
                                <View style={styles.returnView}>
                                    <Text style={styles.return}>İade Talebi Oluşturuldu</Text>
                                    <View style={styles.returnCodeView}>
                                        <Text style={styles.returnCode}>İade Kodu: </Text>
                                        <Text style={styles.returnCodeText}>HLTS7672ANT</Text>
                                    </View>
                                </View>
                                :
                                <></>
                        }
                        {/* DETAIL BUTTON */}
                        {
                            status == 1 || status == 2 || status == 3 ?
                                <TouchableOpacity
                                    onPress={() => {
                                        navigation.navigate("OrderDetail",{orderId})
                                    }}
                                    style={styles.detailButton}
                                >
                                    <Text style={styles.detail}>{MainStore.language.detail}</Text>
                                </TouchableOpacity>
                                :
                                <></>
                        }
                    </View>
                </View>
            </View>
        </View>
    )
}
export default MyOrdersItem;

// -- STYLES -- // 
const styles = StyleSheet.create({
    main: { marginVertical: 15 },
    grayView: {
        backgroundColor: gray_t6,
        width: Layout.screen.width / 1.1,
        alignSelf: 'flex-end',
        minHeight: 112,
        borderTopLeftRadius: 22,
        borderBottomLeftRadius: 22,
        flexDirection: 'row',
        alignItems: 'center'
    },
    imgWhiteView: {
        width: 100,
        height: 100,
        backgroundColor: white,
        marginLeft: 5,
        borderTopLeftRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomLeftRadius: 20
    },
    img: {
        height: 70,
        width: 80
    },
    rightView: {
        marginLeft: 10,
        width: Layout.screen.width / 1.8
    },
    title: {
        fontWeight: 'bold',
        color: black_t3
    },
    priceButtonView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20
    },
    price: {
        color: brown_t2,
        fontWeight: 'bold',
        alignSelf: 'flex-end'
    },
    returnView: { alignItems: 'flex-start' },
    returnCodeView: {
        flexDirection: 'row',
        marginTop: 5
    },
    returnCode: {
        fontSize: 12,
        fontWeight: 'bold',
        color: brown_t2
    },
    returnCodeText: {
        color: black_t3,
        fontWeight: 'bold',
        fontSize: 12
    },
    detailButton: {
        backgroundColor: brown_t2,
        paddingVertical: 7,
        borderRadius: 15,
        width: 76,
        alignItems: 'center',
        justifyContent: 'center'
    },
    detail: {
        color: white,
        fontWeight: 'bold',
        fontSize: 12
    },
    return: {
        color: brown_t2,
        fontWeight: 'bold',
        fontSize: 12
    },
});
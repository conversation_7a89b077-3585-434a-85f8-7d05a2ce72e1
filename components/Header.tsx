import React from "react";
import { Image, Keyboard, StyleSheet, TouchableOpacity, View } from "react-native";
import Layout from "../constants/Layout";
import { MotiView } from "moti";
import { green_t1, white } from "../constants/Color";
import { Input } from "native-base";
import { SafeAreaView } from "react-native-safe-area-context";
import { MainStore } from "../stores/MainStore";

export type Header = {
    onMenu: any,
    menuStatus: boolean,
    onChangeText: any
}

const Header: React.FC<Header> = ({
    onMenu,
    menuStatus = false,
    onChangeText = (text: string) => {

    }
}) => {

    const [searchStatus, setSearchStatus] = React.useState(false);

    return (
        <SafeAreaView>
            <View style={styles.main}>
                <MotiView
                    from={{
                        width: 0,
                        height: 35
                    }}
                    animate={{
                        height: 35,
                        width: searchStatus ? Layout.screen.width / 1.35 : 0
                    }}
                    style={styles.moti}
                >
                    <Input
                        placeholder={MainStore.language.search}
                        onChangeText={(text) => {
                            onChangeText(text);
                        }}
                        borderWidth={0}
                        placeholderTextColor={white}
                        color={white}
                    />
                </MotiView>
                <TouchableOpacity
                    onPress={() => {
                        setSearchStatus(!searchStatus);
                        Keyboard.dismiss();
                    }}
                    style={styles.search}
                    activeOpacity={1}
                >
                    <Image
                        source={require('../assets/header/search.png')}
                        style={styles.iconSearch}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    onPress={() => {
                        onMenu(!menuStatus)
                    }}
                >
                    <Image
                        source={require('../assets/header/menu.png')}
                        style={styles.iconMenu}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    )
}
export default Header;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 10,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'flex-end'
    },
    moti: {
        backgroundColor: green_t1,
        right: -25,
        justifyContent: 'center',
        borderRadius: 8
    },
    search: {
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
        height: 35,
        width: 35,
        backgroundColor: green_t1,
        borderRadius: 8
    },
    iconSearch: {
        height: 14,
        width: 14,
    },
    iconMenu: {
        height: 42,
        width: 42,
        borderRadius: 8
    },
});
import React, { useRef } from 'react';
import { Animated, StyleSheet, View, Text, PanResponder, Dimensions, Image } from 'react-native';
import { black, white } from '../constants/Color';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BUTTON_WIDTH = SCREEN_WIDTH * 0.8;
const BUTTON_HEIGHT = 50;
const SLIDER_WIDTH = BUTTON_WIDTH * 0.4;
const SLIDER_MOVE_SPACE = (BUTTON_WIDTH - SLIDER_WIDTH) / 2; // Slider'ın sola ve sağa hareket edebileceği maksimum mesafe

const SwipeButtonInitialThree = ({ hollyPoint, instagramPoint, onSwipe }: any) => {
  const translateX = useRef(new Animated.Value(0)).current; // translateX başlangıç değerini sıfır olarak ayarla

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: Animated.event([null, { dx: translateX }], {
        useNativeDriver: false
      }),
      onPanResponderRelease: (evt, gestureState) => {
        // Kullanıcı parmağını bıraktığında, kaydırma mesafesini değerlendir
        if (gestureState.dx < -100) {
          // Sola kaydırma
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true
          }).start();
          onSwipe('left');
        } else if (gestureState.dx > 100) {
          // Sağa kaydırma
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true
          }).start();
          onSwipe('right');
        } else {
          // Yeterli kaydırma olmamışsa, ortaya geri dön
          Animated.spring(translateX, {
            toValue: 0, // Her durumda butonu ortaya geri getir
            useNativeDriver: true
          }).start();
        }
      }
    })
  ).current;

  const animatedSliderStyle = {
    transform: [
      {
        translateX: translateX.interpolate({
          inputRange: [-SLIDER_MOVE_SPACE, SLIDER_MOVE_SPACE],
          outputRange: [-SLIDER_MOVE_SPACE, SLIDER_MOVE_SPACE],
          extrapolate: 'clamp', // Kaydırıcının belirli bir alan dışına çıkmasını engelle
        }),
      },
    ],
  };

  

  const loginOpacity = translateX.interpolate({
    inputRange: [-50, 0],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });
  const registerOpacity = translateX.interpolate({
    inputRange: [0, 50],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });


  return (
    <View style={styles.container}>
      <View style={styles.buttonBackground}>
        <View style={styles.column}>
            <Image
                source={require('../assets/root/instagram.png')}
                style={{
                    width: 63,
                    height: 13,
                    resizeMode: 'contain',
                }}
            />
            <View style={styles.row} >
                 <Image
                     source={require('../assets/root/hPWhite.png')}
                     style={{
                         width: 12.35,
                         height: 13.68,
                         marginRight: 5
                     }}
                 />
                 <Text style={styles.buttonRightText}>{instagramPoint}</Text>
            </View>
        </View>
        <Animated.View
          style={[styles.slider, animatedSliderStyle]}
          {...panResponder.panHandlers}
        >
          <Image
            style={{ width: 35, height: 15 }}
            source={require('../assets/root/initialLeft.png')}
          />
          <Image
            style={{ width: 35, height: 15 }}
            source={require('../assets/root/initialRight.png')}
          />
        </Animated.View>
        <View style={styles.column}>
            <Image
                source={require('../assets/root/hollySnapWhiteTwo.png')}
                style={{
                    width: 63,
                    height: 13,
                    resizeMode: 'contain',
                }}
            />
            <View style={styles.row} >
                 <Image
                     source={require('../assets/root/hPWhite.png')}
                     style={{
                         width: 12.35,
                         height: 13.68,
                         marginRight: 5
                     }}
                 />
                 <Text style={styles.buttonRightText}>{hollyPoint}</Text>
            </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: BUTTON_WIDTH,
    height: BUTTON_HEIGHT,
    backgroundColor: 'rgba(256,256,256,0.2)',
    borderRadius: BUTTON_HEIGHT / 2,
    justifyContent: 'center',
    overflow: 'hidden',
  },
  column: {
    flexDirection: 'column'
  },
  row: {
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center'
  },
  buttonBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontWeight: 'bold',
  },
  slider: {
    width: SLIDER_WIDTH,
    height: '100%',
    paddingHorizontal: 15,
    backgroundColor: 'rgba(256,256,256,0.5)',
    borderRadius: BUTTON_HEIGHT / 2,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonRightText: {
    fontSize: 13,
    fontFamily: 'MADE TOMMY',
    color: white
  }
});

export default SwipeButtonInitialThree;

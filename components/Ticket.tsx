import React from "react";
import { Image, ImageBackground, StyleSheet, Text, View } from "react-native";
import { gray_t7, white } from "../constants/Color";
import { getImageURL } from "../networking/Server";
const dayjs = require('dayjs');

export type Ticket = {
    name: string,
    image: any,
    date: string,
    carousel: boolean
}
const Ticket: React.FC<Ticket> = ({
    name,
    image,
    date,
    carousel = false
}) => {
    return (
        <View style={styles.altThreeView}>
            <View style={styles.leftEmptyView}>
                <View style={styles.leftEmpty} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                {
                    carousel ?
                        <>
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                            <View style={[styles.leftEmpty, { height: 3, marginTop: 3 }]} />
                        </>
                        :
                        <></>
                }
            </View>
            <View>
                <Image
                    source={image ? { uri: getImageURL(image) } : require('../assets/exp/gokhan_turkmen.png')}
                    style={
                        [
                            styles.concertImg,
                            {
                                width: carousel ? 136 : 92.92,
                                height: carousel ? 162 : 100.74
                            }
                        ]
                    }
                />
                <View style={styles.rightTop} />
                <View style={styles.rightBottom} />
                <View style={styles.bottomBlack}>
                    <View style={styles.bottomDescView}>
                        <Text style={styles.bottomTitle}>{name ? name.length > 20 ? name.substring(0, 9) : name : "Konser"}</Text>
                        <Text style={styles.bottomDate}>{date ? dayjs(date).format("DD MMMM dddd") : "21 Mart Cumartesi"}</Text>
                    </View>
                </View>
            </View>

            <ImageBackground
                source={require('../assets/menu/ticketBack.png')}
                resizeMode="contain"
                style={
                    [
                        styles.ticketBack,
                        {
                            width: carousel ? 48 : 34.74,
                            height: carousel ? 156 : 100.74,
                            top: carousel ? 3 : 0
                        }
                    ]
                }
            >
                <Text style={
                    [
                        styles.ticketId,
                        {
                            fontSize: carousel ? 8 : 6,
                            width: carousel ? 55 : 40
                        }
                    ]
                }
                >HL0083647</Text>
                <Image
                    style={[
                        styles.barcode,
                        {
                            width: carousel ? 20 : 10.03,
                            height: carousel ? 75 : 54.6,
                            right: carousel ? 20 : 15
                        }
                    ]}
                    source={require('../assets/menu/hollyStoneBlack.png')}
                    resizeMode="contain"
                />
            </ImageBackground>
        </View>
    )
}
export default Ticket;

// -- STYLES -- //
const styles = StyleSheet.create({
    altThreeView: {
        flexDirection: 'row'
    },
    hollyStoneWhiteLogo: {
        width: 6.08,
        height: 30.4,
        position: 'absolute',
        left: 3,
        zIndex: 2
    },
    leftEmptyView: {
        position: 'absolute',
        left: 0,
        zIndex: 1
    },
    concertImg: {
        width: 92.92,
        height: 100.74
    },
    leftEmpty: {
        width: 1,
        height: 2,
        backgroundColor: gray_t7,
        marginTop: 1
    },
    rightTop: {
        position: 'absolute',
        right: -3,
        top: -3,
        height: 6,
        width: 6, borderRadius: 3,
        backgroundColor: gray_t7
    },
    rightBottom: {
        position: 'absolute',
        right: -3,
        bottom: -3,
        height: 6,
        width: 6,
        borderRadius: 3,
        backgroundColor: gray_t7
    },
    bottomBlack: {
        position: 'absolute',
        bottom: 15,
        width: 130,
        height: 22.84,
        backgroundColor: "rgba(0, 0, 0, 0.64)",
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 5
    },
    qr: {
        width: 15.37,
        height: 15.37
    },
    bottomDescView: {
        marginLeft: 5
    },
    bottomTitle: {
        color: white,
        fontWeight: 'bold',
        fontSize: 10
    },
    bottomDate: {
        color: white,
        fontWeight: 'bold',
        fontSize: 8
    },
    ticketBack: {
        right: 2,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row'
    },
    ticketId: {
        transform: [{ rotate: '90deg' }],
        fontWeight: 'bold',
        top: 5,
    },
    barcode: {
        width: 10.03,
        height: 54.6
    },
});
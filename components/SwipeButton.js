// SwipeButton.js

import React from 'react';
import { StyleSheet, Dimensions, Image, Text } from 'react-native';

import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
  Extrapolate,
  runOnJS,
} from 'react-native-reanimated';
import { useState } from 'react';
import { black, black_t2, white } from '../constants/Color';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const BUTTON_WIDTH = SCREEN_WIDTH * 0.8;
const BUTTON_HEIGHT = 50;
const BUTTON_PADDING = 0;
const SWIPEABLE_DIMENSIONS = 120;

const H_SWIPE_RANGE = BUTTON_WIDTH - 2 * BUTTON_PADDING - SWIPEABLE_DIMENSIONS;

const SwipeButton = ({ onToggle, buttonText }) => {
  // Animated value for X translation
  const X = useSharedValue(0);
  // Toggled State
  const [toggled, setToggled] = useState(false);

  // Fires when animation ends
  const handleComplete = (isToggled) => {
    if (isToggled !== toggled) {
      setToggled(isToggled);
      onToggle(isToggled);
    }
  };

  // Gesture Handler Events
  const animatedGestureHandler = useAnimatedGestureHandler({
    onStart: (_, ctx) => {
      ctx.completed = toggled;
    },
    onActive: (e, ctx) => {
      let newValue;
      if (ctx.completed) {
        newValue = H_SWIPE_RANGE + e.translationX;
      } else {
        newValue = e.translationX;
      }

      if (newValue >= 0 && newValue <= H_SWIPE_RANGE) {
        X.value = newValue;
      }
    },
    onEnd: () => {
      if (X.value < BUTTON_WIDTH / 2 - SWIPEABLE_DIMENSIONS / 2) {
        X.value = withSpring(0);
        runOnJS(handleComplete)(false);
      } else {
        X.value = withSpring(H_SWIPE_RANGE);
        runOnJS(handleComplete)(true);
      }
    },
  });

  const InterpolateXInput = [0, H_SWIPE_RANGE];
  const AnimatedStyles = {
    swipeable: useAnimatedStyle(() => {
      return {
        backgroundColor: 'rgba(256, 256, 256, 0.5)',
        transform: [{ translateX: X.value }],
      };
    }),
    swipeText: useAnimatedStyle(() => {
      return {
        opacity: interpolate(
          X.value,
          InterpolateXInput,
          [0.7, 0],
          Extrapolate.CLAMP,
        ),
      };
    }),
  };

  return (
    <Animated.View style={[styles.swipeCont]}>
      <PanGestureHandler onGestureEvent={animatedGestureHandler}>
        <Animated.View style={[styles.swipeable, AnimatedStyles.swipeable]}>
        <Text style={styles.texttwo}>{buttonText}</Text>
        </Animated.View>
      </PanGestureHandler>
      <Animated.View style={[styles.swipeText, AnimatedStyles.swipeText]}>
      <Image
            style={{
              height: 15,
              width: 35,
              right: 10,
            }}
            resizeMode='contain'
            source={require('../assets/root/initialRight.png')}
          />
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  swipeCont: {
    height: BUTTON_HEIGHT,
    width: BUTTON_WIDTH,
    backgroundColor: 'rgba(256,256,256,0.2)',
    borderRadius: BUTTON_HEIGHT,
    padding: BUTTON_PADDING,
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
  },
  swipeable: {
    position: 'absolute',
    left: BUTTON_PADDING,
    height: 50,
    width: SWIPEABLE_DIMENSIONS,
    borderRadius: SWIPEABLE_DIMENSIONS,
    zIndex: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  swipeText: {
    alignSelf: 'center',
    fontWeight: 'bold',
    fontSize: 15,
    zIndex: 2,
    color: '#fff',
    right: 15,
    top: -1,
  },
  texttwo: {
    color : white,
    fontWeight: 'bold'
  }
});

export default SwipeButton;

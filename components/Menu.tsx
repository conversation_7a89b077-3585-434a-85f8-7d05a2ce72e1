import React from "react";
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Layout from "../constants/Layout";
import { MotiView } from "moti";
import { black, black_t2, blue_t4, brown_t1, pink, white, yellow_t2 } from "../constants/Color";
import { SafeAreaView } from "react-native-safe-area-context";
import { shadow } from "../constants/Shadow";
import { <PERSON><PERSON>, Spinner } from "native-base";
import { get, getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";
import Svg, { Path, Rect } from "react-native-svg";
import { goPage } from "../functions/goPage";
import { useNavigationState } from '@react-navigation/native';



type MenuList = {
    title: string,
    onPress: any,
    backgroundColor: string
}
const MenuList: React.FC<MenuList> = ({
    title = "",
    onPress,
    backgroundColor = "#FFF"
}) => {

    return (
        <TouchableOpacity
            activeOpacity={1}
            onPress={onPress}
            style={[styles.menuListView, shadow, { backgroundColor }]}
        >
            {
                title == "GIFT CARD" ?
                    <Image
                        source={require('../assets/hollypaycard.png')}
                        style={styles.menuListImage}
                        resizeMode="contain"
                    />
                    :
                    <Text style={[
                        styles.menuListTitle,
                        {
                            fontWeight: 'bold',
                            color: backgroundColor == white ? black : white
                        }
                    ]}>{title}</Text>
            }


        </TouchableOpacity>
    )
}


export type Menu = {
    menuStatus: boolean,
    onMenu: any,
    navigation: any,
    menuColor: string
}

const Menu: React.FC<Menu> = ({
    menuStatus = false,
    onMenu = (set: boolean) => { },
    navigation,
    menuColor = '#FFF'
}) => {

    const menuList = [
        {
            id: 1,
            title: MainStore.language.earned,
            backgroundColor: white,
            nav: "Earned"
        },
        {
            id: 2,
            title: "GIFT CARD",
            backgroundColor: black,
            nav: "GiftCard"
        },
        {
            id: 3,
            title: MainStore.language.contact,
            backgroundColor: white,
            nav: "Contact"
        },
        {
            id: 4,
            title: MainStore.language.career,
            backgroundColor: white,
            nav: "Career"
        },
        {
            id: 5,
            title: MainStore.language.artist_application,
            backgroundColor: white,
            nav: "ArtistApplication"
        },
        {
            id: 6,
            title: "franchise",
            backgroundColor: white,
            nav: "Franchise"
        },
        {
            id: 7,
            title: MainStore.language.settings,
            backgroundColor: white,
            nav: "Settings"
        }

    ];

    // -- LOADING -- //
    const [loading, setLoading] = React.useState(false);
    const [user, setUser]: any = React.useState({});

    // -- GET USER INFO -- //
    const getUserData = () => {
        try {
            get("/users/profile").then((res: any) => {
                if (res.type == "success") {
                    setUser(res.user)
                } else {
                }
                setLoading(false);
            })
        } catch (e) {
        }
    }

    React.useEffect(() => {
        getUserData();
    }, []);


    const currentRouteName = useNavigationState(state => state.routes[state.index].name);


    return (
        <MotiView
            from={{
                right: -1000
            }}
            animate={{
                right: menuStatus ? 0 : -1000
            }}
            style={styles.moti}
            transition={{
                type: 'timing'
            }}
        >
            {/* LEFT CLOSE TOUCH AREA */}
            <TouchableOpacity
                activeOpacity={1}
                style={styles.leftArea}
                onPress={() => {
                    onMenu(!menuStatus)
                }}
            />
            <View style={[styles.rightArea, { backgroundColor: menuColor }]}>

                <Image
                    source={
                        menuColor == yellow_t2 ?
                            require('../assets/menu/menuYellow.png')
                            :
                            menuColor == blue_t4 ?
                                require('../assets/menu/menuBlue.png')
                                :
                                menuColor == pink ?
                                require('../assets/menu/menuPink.png')
                                :
                                require('../assets/menu/menuGreen.png')
                    }
                    style={styles.menuLeftImg}
                    resizeMode="cover"
                />
                {/* MENU HEADER */}
                <View style={styles.menuHeader}>
                    <SafeAreaView style={styles.menuSafeAreaView}>
                        <Image
                            source={require('../assets/menu/menuHollyStoneLogo.png')}
                            style={styles.menuHollyStoneLogo}
                            resizeMode="contain"
                        />
                        <View style={styles.menuSafeAreaView2} >
                        {currentRouteName === "Root" || currentRouteName === "Settings" ? (
                                <TouchableOpacity
                                    onPress={() => {
                                        MainStore.setToken("");
                                        goPage(navigation, "Initial", {}, false);
                                    }}
                                >
                                    <View style={styles.svgclose}>
                                        <Svg width="50" height="50" viewBox="0 0 65 65">
                                            <Path
                                                fill="#ffffff"
                                                d="M20.034,2.357v3.824c3.482,1.798,5.869,5.427,5.869,9.619c0,5.98-4.848,10.83-10.828,10.83
                                                c-5.982,0-10.832-4.85-10.832-10.83c0-3.844,2.012-7.215,5.029-9.136V2.689C4.245,4.918,0.731,9.945,0.731,15.801
                                                c0,7.921,6.42,14.342,14.34,14.342c7.924,0,14.342-6.421,14.342-14.342C29.412,9.624,25.501,4.379,20.034,2.357z"
                                            />
                                            <Path
                                                fill="#ffffff"
                                                d="M14.795,17.652c1.576,0,1.736-0.931,1.736-2.076V2.08c0-1.148-0.16-2.08-1.736-2.08
                                                c-1.57,0-1.732,0.932-1.732,2.08v13.496C13.062,16.722,13.225,17.652,14.795,17.652z"
                                            />
                                        </Svg>
                                    </View>
                                </TouchableOpacity>
                            ) : null}
                        <TouchableOpacity
                            onPress={() => {
                                onMenu(!menuStatus)
                            }}
                        >
                            <Image
                                source={
                                    menuColor == yellow_t2 ?
                                        require('../assets/menu/menuIconYellow.png')
                                        :
                                        menuColor == blue_t4 ?
                                            require('../assets/menu/menuIconBlue.png')
                                            :
                                            menuColor == pink ?
                                            require('../assets/menu/menuIconPink.png')
                                            :
                                            require('../assets/menu/menuIconGreen.png')
                                }
                                style={styles.menuIcon}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>
                        </View>

                    </SafeAreaView>
                </View>

                {/* WHITE AREA */}
                <View style={styles.whiteArea}>

                    {/* PROFILE AREA */}
                    <View style={styles.prfInfoView}>
                        <Text style={styles.prfNameSurname}>{user?.firstName} {user?.lastName}</Text>
                        <Text style={styles.prfId}>{user?.referralCode}</Text>
                        <View style={styles.prfAltView}>
                            {/*
                            <View style={styles.prfAltLeftView}>
                                <Text style={styles.prfAltLeftPuan}>12</Text>
                                <Image
                                    source={require('../assets/root/hPWhite.png')}
                                    style={styles.prfAltLeftHP}
                                    resizeMode="contain"
                                />
                            </View>
                            */}
                            <TouchableOpacity
                                onPress={() => {
                                    setTimeout(() => {
                                        onMenu(!menuStatus)
                                    }, 200)
                                    navigation.navigate("Profile")
                                }}
                            >
                                {
                                    loading ?
                                        <View style={styles.loadingView}>
                                            <Spinner color={white} size={18} />
                                        </View>
                                        :
                                        <Image
                                            source={{ uri: getImageURL(user?.image) }}
                                            style={styles.prfImage}
                                            resizeMode="contain"
                                        />
                                }

                            </TouchableOpacity>
                            {/**
                             <View>
                                <Image
                                    source={require('../assets/root/level.png')}
                                    style={styles.prfLevelLogo}
                                    resizeMode="contain"
                                />
                                <Text style={styles.prfLevel}>5</Text>
                            </View>
                             */}
                        </View>
                    </View>

                    {/* MENU LIST */}
                    <View style={styles.menuListMainView}>

                        <ScrollView showsVerticalScrollIndicator={true}>

                            {
                                menuList.map((item, index) => {
                                    return (
                                        <MenuList
                                            key={index}
                                            title={item.title}
                                            onPress={() => {
                                                setTimeout(() => {
                                                    onMenu(!menuStatus)
                                                }, 200)
                                                navigation.navigate(item.nav)
                                            }}
                                            backgroundColor={item.backgroundColor}
                                        />
                                    )
                                })
                            }

                        </ScrollView>
                    </View>
                </View>

                {/* BUTTOM SIDE AREA */}
                <View style={styles.buttomSide}>
                    <Text style={styles.buttonSideText}>{MainStore.city.name}</Text>
                    <Button
                        onPress={() => {
                            setTimeout(() => {
                                onMenu(!menuStatus)
                            }, 200)
                            navigation.navigate("ChangeCity")
                        }}
                        style={styles.button}
                    >
                        <Text style={[styles.buttonText, { color: menuColor }]}>{MainStore.language.change_city}</Text>
                    </Button>
                    <Text style={styles.version}>v.1.1.4</Text>
                </View>
            </View>

            {/* BOTTOM TOUCH AREA */}
            <TouchableOpacity
                activeOpacity={1}
                style={styles.bottomArea}
                onPress={() => {
                    onMenu(!menuStatus)
                }}
            />
        </MotiView >
    )
}

export default Menu;

// -- STYLES -- //
const styles = StyleSheet.create({
    menuListView: {
        height: 60,
        alignItems: 'center',
        justifyContent: 'center'
    },
    menuListTitle: {
        fontSize: 20
    },
    menuListImage: {
        width: 150,
        height: 40,
    },
    moti: {
        flexDirection: 'row',
        zIndex: 110,
        position: 'absolute',
        height: Layout.screen.height,
        width: Layout.screen.width,
    },
    leftArea: {
        height: Layout.screen.height,
        width: '18%',
        left: 0,
    },
    rightArea: {
        height: Layout.screen.height / 1.1,
        borderBottomLeftRadius: 15,
        width: '82%',
        right: 0
    },
    menuLeftImg: {
        height: 289,
        width: 26,
        left: -24,
        position: 'absolute',
        borderTopLeftRadius: 8
    },
    menuHeader: {
        paddingRight: 24,
        paddingTop: 20,
        height: 289
    },
    menuSafeAreaView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuSafeAreaView2: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        alignContent: 'center',
        alignSelf: 'center',
    },
    svgclose: {
        top: 14
    },

    menuHollyStoneLogo: {
        width: 104,
        height: 21
    },
    menuIcon: {
        width: 35,
        height: 35,
        borderRadius: 8
    },
    whiteArea: {
        alignItems: 'center',
        backgroundColor: 'white',
        height: Layout.screen.height - 485
    },
    prfInfoView: {
        top: -120,
        alignItems: 'center',
    },
    prfNameSurname: {
        color: white,
        fontWeight: 'bold',
        fontSize: 18
    },
    prfId: {
        marginTop: 5,
        color: white
    },
    prfAltView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '90%',
        paddingHorizontal: 25,
        marginTop: 10
    },
    prfAltLeftView: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    prfAltLeftPuan: {
        color: white,
        fontSize: 20,
        fontWeight: 'bold'
    },
    prfAltLeftHP: {
        height: 36,
        width: 35.5,
        marginLeft: 8
    },
    loadingView: {
        height: 85,
        width: 85,
    },
    prfImage: {
        height: 85,
        width: 85,
        marginLeft: 8,
        top: 20,
        borderWidth: 3,
        borderColor: white,
        borderRadius: 42.5
    },
    prfLevelLogo: {
        height: 32,
        width: 24.32,
        marginLeft: 20
    },
    prfLevel: {
        color: black_t2,
        top: -3,
        fontWeight: 'bold',
        fontSize: 16,
        position: 'absolute',
        right: 0
    },
    menuListMainView: {
        top: -100,
        width: '100%'
    },
    buttomSide: {
        width: '100%',
        position: 'absolute',
        bottom: 0,
        borderBottomLeftRadius: 15,
        alignItems: 'center'
    },
    buttonSideText: {
        fontWeight: 'bold',
        color: white,
        letterSpacing: 1.2,
        fontSize: 20,
        marginTop: 20
    },
    button: {
        bottom: -15,
        height: 40,
        borderRadius: 15,
        backgroundColor: white
    },
    buttonText: {
        fontSize: 14,
        marginHorizontal: 10,
        fontFamily: 'MADE TOMMY'
    },
    version: {
        fontWeight: 'bold',
        position: 'absolute',
        right: 5,
        bottom: 5,
        color: white
    },
    bottomArea: {
        height: Layout.screen.height / 10.9,
        width: '100%',
        bottom: 0,
        position: 'absolute',
    },
});
import React from "react";
import { View, Image, Text, TouchableOpacity } from "react-native";
import { StyleSheet } from "react-native";
import { black, black_t4, green_t3, white } from "../constants/Color";
import { MotiView } from "moti";
import Layout from "../constants/Layout";
import { useFocusEffect } from "@react-navigation/native";
import { getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";
const dayjs = require("dayjs");

export type ConcertItem = {
    item: any,
    navigation: any
}

const ConcertItem: React.FC<ConcertItem> = ({
    item,
    navigation
}) => {
    // 1 - ATRIUM
    // 2 - PERFORMANCE STAGE
    // 3 - HOLLY GARDEN



    const [status, setStatus] = React.useState(false);

    useFocusEffect(
        React.useCallback(() => {
            setStatus(false);
        }, [])
    );

    return (
        <TouchableOpacity
            onPress={() => {
                setStatus(!status)
                setTimeout(() => {
                    navigation.navigate("ConcertDetail", { concertId: item.id });
                }, 1000);
            }}
            activeOpacity={1}
            style={[styles.mainBack]}
        >
            <Image
                style={styles.leftConcertView}
                resizeMode="cover"
                source={require('../assets/root/concertBackLeftWhite.png')}
            />

            {/* CONCERT NAME | IMAGE */}
            <View style={styles.concertImgView}>
                <Image
                    resizeMode="cover"
                    style={styles.concertImg}
                    source={{ uri: getImageURL(item?.image) }}
                />
                <View style={styles.concertNameView}>
                <Text style={styles.concertName}>
    {
        item?.name?.length > 12
            ? `${item?.name?.substring(0, 12)}..`
            : item?.name
    }
</Text>

                </View>
                {
                    status ?
                        <Image
                            source={require('../assets/root/conDaiLoad.gif')}
                            style={styles.conDaiLoad}
                            resizeMode="contain"
                        />
                        :
                        <></>
                }
            </View>

            {/* MOTI VIEW */}
            <View style={styles.motiView}>
                <MotiView
                    from={{
                        width: 15,
                    }}
                    animate={{
                        width: status ? Layout.screen.width / 3.7 : 15
                    }}

                    transition={{
                        type: 'timing',
                        duration: 200
                    }}
                >

                    {/* CONCERT DETAIL */}
                    {
                        status ?
                            <View style={styles.greenBack}>
                                <View style={{ marginLeft: 15 }}>
                                    <Text style={styles.greenTitle}>{MainStore.language.opening_door}</Text>
                                    <Text style={styles.greenDesc}>{dayjs(item?.gateDateTime).format("HH:mm")}</Text>
                                </View>
                                <View style={styles.greenAltView}>
                                    <Text style={styles.greenTitle}>{MainStore.language.scene}</Text>
                                    <Text style={styles.greenDesc}>{dayjs(item?.dateTime).format("HH:mm")}</Text>
                                </View>
                                {
                                    item?.price ?
                                        <View style={styles.greenAltView}>
                                            <Text style={styles.greenTitle}>{MainStore.language.price}</Text>
                                            <Text style={styles.greenDesc}>{item?.price} ₺</Text>
                                        </View>
                                        :
                                        <></>
                                }
                            </View>
                            :
                            <View style={styles.greenBack} />
                    }
                </MotiView>
                <Image
                    style={styles.concertBackLeft}
                    resizeMode="cover"
                    source={require('../assets/root/concertBackLeft.png')}
                />

                {/* ATRIUM LOGO */}
                {
                
                            <Text style={styles.leftBlackText}>KONSER</Text>
                }
            </View>

            {/* DATE */}
            <View style={styles.leftDateView}>
                <Text style={styles.leftDateNumber}>{dayjs(item?.dateTime).format("DD")}</Text>
                <Text style={styles.leftDate}>{dayjs(item?.dateTime).format("MMM")}</Text>
            </View>

        </TouchableOpacity>
    )
}
export default ConcertItem;

// -- STYLES -- //
const styles = StyleSheet.create({
    mainBack: {
        width: Layout.screen.width / 2.08,
        height: 127,
        borderRadius: 10,
        flexDirection: 'row',
    },
    leftConcertView: {
        height: 120,
        width: 41.5,
        top: 4,
        alignItems: 'center',
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    concertBackLeft: {
        width: 54,
        top: 4,
        height: 120,
        left: -24,
        zIndex: -1,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    concertImgView: {
        zIndex: -1,
        left: -1,
        top: 4,
        height: 120,
    },
    concertImg: {
        height: 120,
        width: Layout.screen.width / 2.9,
        borderRadius: 10
    },
    concertNameView: {
        height: 32,
        width: '100%',
        borderBottomRightRadius: 10,
        backgroundColor: black,
        position: 'absolute',
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center'
    },
    concertName: {
        color: white,
        fontSize: 12,
        left: 5,
        textAlign: 'center',
        fontFamily: 'MADE TOMMY',
        textTransform: 'uppercase'
    },
    conDaiLoad: {
        height: 16,
        width: 16,
        position: 'absolute',
        right: 5,
        top: 5
    },
    motiView: {
        position: 'absolute',
        left: 15,
        zIndex: -1,
        height: 123,
        flexDirection: 'row'
    },
    greenBack: {
        backgroundColor: green_t3,
        height: 120,
        marginTop: 4,
        justifyContent: 'center',
        paddingLeft: 20
    },
    greenAltView: {
        marginTop: 4,
        marginLeft: 15
    },
    greenTitle: {
        fontSize: 10,
        color: white
    },
    greenDesc: {
        fontSize: 10,
        color: white,
        fontWeight: 'bold'
    },
    atriumND: {
        width: 5,
        height: 59,
        left: -43,
        zIndex: -1,
        alignSelf: 'flex-end',
        marginBottom: 5
    },
    leftBlackText: {
        left: -24.6,
        zIndex: 2,
        width: 80,
        color: white,
        fontWeight: 'bold',
        letterSpacing: 3,
        bottom: -16,
        fontSize: 7,
        transform: [{ rotate: '270deg' }]
    },
    leftDateView: {
        position: 'absolute',
        marginLeft: 4,
        marginTop: 10,
        alignItems: 'center'
    },
    leftDateNumber: {
        color: black_t4,
        fontSize: 18,
        fontFamily: 'MADE TOMMY',
        textTransform: 'uppercase'
    },
    leftDate: {
        color: black_t4,
        fontSize: 12,
        fontFamily: 'MADE TOMMY',
        textTransform: 'uppercase'
    }
});
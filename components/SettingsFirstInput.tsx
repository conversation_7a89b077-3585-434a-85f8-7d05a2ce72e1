import React from "react";
import { Image, StyleSheet, View } from "react-native";
import { black, gray_t4, white } from "../constants/Color";
import { shadow } from "../constants/Shadow";
import Layout from "../constants/Layout";
import { Input } from "native-base";
import { WriteIcon } from "./Svgs";

export type SettingsFirstInput = {
    placeHolder: string
    setText: any
}
const SettingsFirstInput: React.FC<SettingsFirstInput> = ({
    placeHolder = "",
    setText = () => {}
}) => {
    return (
        <View style={[styles.main, shadow]}>

            {/* ICON */}
            <View style={[styles.iconBack, shadow]}>
                {/* <Image
                    style={styles.icon}
                    resizeMode="contain"
                    source={require('../assets/menu/subject.png')}
                /> */}
                <WriteIcon size={5} color={'transparent'} />
            </View>

            {/* INPUT */}
            <View style={styles.input}>
                <Input
                    placeholder={placeHolder}
                    onChange={(event) => setText(event.nativeEvent.text)}
                    height={43}
                    borderWidth={0}
                    ml={5}
                    placeholderTextColor={gray_t4}
                    color={black}
                    fontSize={16}
                    fontWeight={'bold'}
                    backgroundColor={'transparent'}
                />
            </View>
        </View>
    )
}
export default SettingsFirstInput;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: { flexDirection: 'row' },
    iconBack: {
        height: 43,
        width: 43,
        backgroundColor: white,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 22.5,
        zIndex: 2
    },
    icon: {
        height: 23.92,
        width: 25,
        marginLeft: 5
    },
    input: {
        width: Layout.screen.width / 1.28,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10,
        backgroundColor: white,
        zIndex: 1,
        left: -22.5

    },
});

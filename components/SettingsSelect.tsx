import React from "react";
import { StyleSheet, View } from "react-native";
import { black, white } from "../constants/Color";
import { CheckIcon, Select } from "native-base";

export type SettingsSelect = {
    variables: any,
    setSelected: any
}

const SettingsSelect: React.FC<SettingsSelect> = ({
    variables = [],
    setSelected
}) => {
    return (
        <View style={styles.main}>
            <Select
                minWidth="200"
                height={50}
                borderWidth={0}
                mb={5}
                fontSize={14}
                fontWeight={'bold'}
                color={black}
                placeholderTextColor={black}
                placeholder="Seçiniz"
                onValueChange={(itemValue: any) => {
                    setSelected(itemValue);
                }}
                _selectedItem={{
                    bg: "teal.600",
                    endIcon: <CheckIcon size={5} />
                }}
            >
                {
                    variables?.length > 0 ?
                        variables?.map((item: any, index: React.Key) => {
                            return (
                                <Select.Item key={index} label={item.title} value={item.title} />
                            )
                        })
                        :
                        <Select.Item label="" value="ux" />
                }
            </Select>
        </View >
    )
}
export default SettingsSelect;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        borderRadius: 10,
        height: 50,
        backgroundColor: white,
    },
});
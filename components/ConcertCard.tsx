import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  Animated,
} from 'react-native';
import { <PERSON><PERSON>View } from 'moti';
import Svg, { Path } from 'react-native-svg';
import { black, white, green_t3 } from '../constants/Color';
import { getImageURL } from '../networking/Server';
import dayjs from 'dayjs';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.9;
const IMAGE_HEIGHT = 200;

export type ConcertCardProps = {
  item: any,
  navigation: any
}

const ConcertCard: React.FC<ConcertCardProps> = ({ item, navigation }) => {
  const [isOpen, setIsOpen] = useState(false);
  const animWidth = useRef(new Animated.Value(31)).current;
  const animOpacity = useRef(new Animated.Value(0)).current;

  const toggleBox = () => {
    Animated.parallel([
      Animated.timing(animWidth, {
        toValue: isOpen ? 31 : 150,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(animOpacity, {
        toValue: isOpen ? 0 : 1,
        duration: 300,
        useNativeDriver: false,
      }),
    ]).start(() => setIsOpen(!isOpen));
  };

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPress={toggleBox}
      style={styles.container}
    >
      {/* Konser Görseli */}
      <Image
        source={{ uri: getImageURL(item?.image) }}
        style={styles.image}
        resizeMode="cover"
      />

      {/* Sol Yeşil Şerit */}
      <View style={styles.greenStrip}>
        <Svg
          width={18}
          height="100%"
          viewBox="0 0 18 100"
          style={styles.greenSvg}
        >
          <Path
            fill={green_t3}
            d="M0 0h14.13v58.9c0 4.25-1.38 8.38-3.97 11.76a44.216 44.216 0 0 0-9.12 26.91v4.61c0 .36-.01.73-.03 1.09l-1 18.1V0Z"
          />
        </Svg>
        
        {/* Tarih Bilgisi */}
        <View style={styles.dateContainer}>
          <Text style={styles.dayText}>{dayjs(item?.dateTime).format("DD")}</Text>
          <Text style={styles.monthText}>{dayjs(item?.dateTime).format("MMM")}</Text>
        </View>
      </View>

      {/* Açılır Panel */}
      <Animated.View style={[styles.panelContainer, { width: animWidth }]}>
        <MotiView
          animate={{ 
            translateX: isOpen ? 148 : 30 
          }}
          transition={{ type: 'timing', duration: 300 }}
        >
          <Svg
            width={18}
            height="100%"
            viewBox="0 0 18 100"
            style={{ left: isOpen ? 148 : 30 }} // isOpen state'ine göre dinamik stil
          >
            <Path
              fill={white}
              d="M0 0h14.13v58.9c0 4.25-1.38 8.38-3.97 11.76a44.216 44.216 0 0 0-9.12 26.91v4.61c0 .36-.01.73-.03 1.09l-1 18.1V0Z"
            />
          </Svg>
        </MotiView>

        {/* Dikey Yazı */}
        {!isOpen && (
          <Text style={styles.verticalText}>KONSER</Text>
        )}

        {/* Detaylar */}
        <Animated.View style={[styles.detailsContainer, { opacity: animOpacity }]}>
          <Text style={styles.detailTitle} numberOfLines={2}>
            {item.name}
          </Text>
          <Text style={styles.detailText}>Tarih: {dayjs(item?.dateTime).format("HH:mm")}</Text>
          <Text style={styles.detailText}>
            Kapı Açılış: {dayjs(item?.gateDateTime).format("HH:mm")}
          </Text>

          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('ConcertDetail', {
              concertId: item.id
            })}
          >
            <Text style={styles.buttonText}>Daha Fazla Bilgi</Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Konser İsmi */}
      <View style={styles.titleContainer}>
        <Text style={styles.titleText} numberOfLines={1}>
          {item.name.length > 20 
            ? `${item.name.slice(0, 20)}..` 
            : item.name}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    height: 280,
    backgroundColor: white,
    borderRadius: 8,
    marginVertical: 8,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: '100%',
    height: IMAGE_HEIGHT,
  },
  greenStrip: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: '100%',
    width: 40,
    backgroundColor: green_t3,
    zIndex: 2,
  },
  greenSvg: {
    position: 'absolute',
    left: 39,
    top: 0,
    zIndex: 1,
  },
  dateContainer: {
    position: 'absolute',
    top: 16,
    left: 8,
    zIndex: 3,
    alignItems: 'center',
  },
  dayText: {
    color: white,
    fontSize: 22,
    fontWeight: 'bold',
  },
  monthText: {
    color: white,
    fontSize: 14,
    fontWeight: '500',
  },
  panelContainer: {
    position: 'absolute',
    left: 40,
    top: 0,
    height: '100%',
    backgroundColor: white,
    paddingTop: 70,
    zIndex: 3,
    overflow: 'hidden',
  },
  verticalText: {
    position: 'absolute',
    bottom: 20,
    right: -24,
    color: black,
    fontSize: 12,
    fontWeight: '600',
    transform: [{ rotate: '270deg' }],
    letterSpacing: -2,
    textTransform: 'uppercase',
  },
  detailsContainer: {
    padding: 8,
    flex: 1,
  },
  detailTitle: {
    color: black,
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
  },
  detailText: {
    color: black,
    fontSize: 12,
    marginBottom: 4,
  },
  button: {
    backgroundColor: green_t3,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginTop: 16,
    alignSelf: 'flex-start',
  },
  buttonText: {
    color: white,
    fontSize: 12,
    fontWeight: '500',
  },
  titleContainer: {
    position: 'absolute',
    bottom: 0,
    left: 60,
    right: 0,
    backgroundColor: black,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  titleText: {
    color: white,
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ConcertCard;
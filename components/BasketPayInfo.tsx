import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "moti";
import React, { useEffect } from "react";
import { black, black_t2, black_t3, brown_t2, gray_t4, white, yellow_t1 } from "../constants/Color";
import Layout from "../constants/Layout";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { shadow } from "../constants/Shadow";
import { get } from "../networking/Server";
import { MainStore } from "../stores/MainStore";

export type BasketPayInfo = {
    card: any,
    hollyPoints: number,
    winHollyPoints: number,
    totalPrice: number,
    hollyPointsValue: number,
    setHPTU: any,
    hollyPointsToUse: any,
    isItemDeleted: any,
    setHollyPointsTo: any,

    
}

const BasketPayInfo: React.FC<BasketPayInfo> = ({
    card,
    hollyPoints,
    winHollyPoints,
    totalPrice,
    hollyPointsValue,
    setHPTU = (set: number) => { },
    hollyPointsToUse,
    isItemDeleted,

}) => {

    

    const [hollyInfoModal, setHollyInfoModal] = React.useState(false);
    const [kullanılanhollypuan, setHollyPointsToUse] = React.useState(hollyPointsToUse);


    React.useEffect(() => {
        setHPTU(kullanılanhollypuan * hollyPointsValue);
    }, [kullanılanhollypuan]);

    useEffect(() => {
        if (isItemDeleted) {
            setHollyPointsToUse(0); // Kullanılanhollypuan değerini sıfırla
        }
    }, [isItemDeleted]);




    
    return (
        <MotiView
            from={{
                height: 299
            }}
            animate={{
                height: hollyInfoModal ? 414 : 306 // H + 3 ( FROM FIGMA )
            }}
            style={styles.main}
        >
            <TouchableOpacity
                onPressIn={() => {
                    setHollyInfoModal(!hollyInfoModal)
                }}
            >
                <Image
                    style={styles.hollyPuanLogo}
                    source={require('../assets/root/hollyPuanLogo.png')}
                />
                <Image
                    style={[
                        styles.basket,
                        { top: !hollyInfoModal ? 5 : 15 }
                    ]}
                    source={
                        !hollyInfoModal ?
                            require('../assets/root/basketUp.png')
                            :
                            require('../assets/root/basketDown.png')
                    }
                />
            </TouchableOpacity>

            <View style={styles.whiteAreaView}>

                {/* HOLLY PUAN */}
                <View style={styles.whiteAreaAltView}>
                    <Text style={styles.whiteAreaTitle}>Holly Puanım</Text>
                    <View style={styles.whiteAreaRightView}>
                        <Text style={styles.hPuan}>{hollyPoints - kullanılanhollypuan}</Text>
                        <Image
                            source={require('../assets/root/hP.png')}
                            style={styles.hP}
                            resizeMode="contain"
                        />
                    </View>
                </View>

                {/* WILL EARN HOLLY PUAN */}
                <View style={[styles.whiteAreaAltView, { marginTop: 5 }]}>
                    <Text style={styles.whiteAreaTitle}>Kazanacağım Holly Puan</Text>
                    <View style={styles.whiteAreaRightView}>
                        <Text style={styles.hPuan}>{winHollyPoints}</Text>
                        <Image
                            source={require('../assets/root/hP.png')}
                            style={styles.hP}
                            resizeMode="contain"
                        />
                    </View>
                </View>

                {/* HOLLY PUAN IN INFO */}
                <View style={[styles.whiteAreaAltView, { marginTop: 10 }]}>
                    <Text style={[styles.whiteAreaTitle, { fontSize: 15 }]}>Holly Puan Kullan</Text>
                    <View style={styles.whiteAreaRightView}>
                    <TouchableOpacity 
                        style={[styles.whiteAreaSelectedView, !MainStore.basket.length && { opacity: 0.5 }]} 
                        onPress={() => {
                            if (kullanılanhollypuan > 0 && MainStore.basket.length) 
                                setHollyPointsToUse(kullanılanhollypuan - 1);
                        }}
                        disabled={!MainStore.basket.length} 
                    >
                        <Text style={styles.whiteAreaSelectedText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.hPN}>{kullanılanhollypuan}</Text>
                    <TouchableOpacity 
                        style={[styles.whiteAreaSelectedView, !MainStore.basket.length && { opacity: 0.5 }]} 
                        onPress={() => {
                            if (kullanılanhollypuan < hollyPoints && MainStore.basket.length) 
                                setHollyPointsToUse(kullanılanhollypuan + 1);
                        }}
                        disabled={!MainStore.basket.length} 
                    >
                        <Text style={styles.whiteAreaSelectedText}>+</Text>
                    </TouchableOpacity>

                    </View>
                </View>
            </View>

            <View
                style={
                    [
                        styles.brownArea,
                        {
                            shadowColor: black,
                            shadowOffset: {
                                width: 0,
                                height: -0.5,
                            },
                            shadowOpacity: 0.3,
                            shadowRadius: 2.1
                        }

                    ]
                }
            >
                <Image
                    style={styles.brownTop}
                    source={require('../assets/root/basketBack.png')}
                />
                <View style={styles.brownView}>

                    {/* PRODUCT TOTAL */}
                    <View style={styles.brownAltView}>
                        <View style={styles.brownListView}>
                            <Text style={styles.brownListTitle}>Ürün Toplam Tutar</Text>
                            <Text style={styles.brownListPrice}>{totalPrice}₺</Text>
                        </View>
                    </View>

                    {/* USED HOLLY PUAN */}
                    <View style={[styles.brownAltView, { marginTop: card ? 7 : 17 }]}>
                        <View style={styles.brownListView}>
                            <Text style={styles.brownListTitle}>Kullanılan Holly Puan</Text>
                            <View style={styles.brownSpecialView}>
                                <Text style={styles.brownListPrice}>{kullanılanhollypuan}</Text>
                                <Image
                                    source={require('../assets/root/hPWhite.png')}
                                    resizeMode="contain"
                                    style={styles.hPWhite}
                                />
                            </View>
                        </View>
                    </View>

                    {/* DISCOUNT TOTAL */}
                    <View style={[styles.brownAltView, { marginTop: card ? 7 : 17 }]}>
                        <View style={styles.brownListView}>
                            <Text style={[styles.brownListTitle, { color: black_t3 }]}>İndirim</Text>
                            <Text style={[styles.brownListPrice, { color: black_t3 }]}>{kullanılanhollypuan > 0 ? (`-${kullanılanhollypuan * hollyPointsValue}₺`) : "-"}</Text>
                        </View>
                    </View>

                    {
                        card ?
                            <>
                                {/* CARD */}
                                <View style={[styles.brownAltView, { marginTop: 7 }]}>
                                    <View style={styles.brownListView}>
                                        <Text style={styles.brownListTitle}>Seçili Kartım</Text>
                                        <View style={[{
                                            borderWidth: 1,
                                            borderColor: yellow_t1,
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            height: 25,
                                            width: 164,
                                            backgroundColor: white,
                                            borderRadius: 10,
                                            alignItems: 'center',
                                            paddingHorizontal: 4
                                        }, shadow]}>
                                            <Text style={{
                                                fontWeight: 'bold',
                                                fontSize: 10,
                                                color: gray_t4
                                            }}>**** **** **** {card?.last_4}</Text>
                                            <Image
                                                style={{
                                                    width: 20,
                                                    height: 12
                                                }}
                                                source={card?.schema == "MASTERCARD" ?
                                                    require('../assets/exp/masterCard.png')
                                                    :
                                                    card?.schema == "VISA" ?
                                                        require('../assets/exp/visa.png')
                                                        :
                                                        require('../assets/exp/cardIcon.png')}
                                            />
                                        </View>
                                    </View>
                                </View>
                            </>
                            :
                            <></>
                    }

                    <View style={{
                        width: Layout.screen.width,
                        alignSelf: 'center',
                        height: 0.5,
                        backgroundColor: black,
                        opacity: 0.35,
                        marginTop: 14
                    }} />

                    {/* TOTAL */}
                    <View style={[styles.brownAltView, { marginTop: 12 }]}>
                        <View style={styles.brownListView}>
                            <Text style={[styles.brownListTitle, { fontSize: 14 }]}>Ödenecek Tutar</Text>
                            <Text style={[styles.brownListPrice, { fontSize: 20 }]}>{totalPrice - (kullanılanhollypuan * hollyPointsValue)}₺</Text>
                        </View>
                    </View>
                </View>
            </View>
        </MotiView>
    )
}
export default BasketPayInfo;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        backgroundColor: white,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        position: 'absolute',
        bottom: 0,
        width: Layout.screen.width
    },
    hollyPuanLogo: {
        width: 123,
        height: 21,
        position: 'absolute',
        left: 20,
        top: 15
    },
    basket: {
        width: 25,
        height: 19,
        position: 'absolute',
        right: 25,
    },
    whiteAreaView: {
        alignSelf: 'center',
        width: Layout.screen.width / 1.2,
        marginTop: 50
    },
    whiteAreaAltView: {
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row'
    },
    whiteAreaTitle: {
        fontWeight: 'bold',
        fontSize: 14,
        color: black_t3
    },
    whiteAreaRightView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    hPuan: {
        fontWeight: 'bold',
        color: black_t3,
        fontSize: 16
    },
    hP: {
        width: 15.37,
        height: 17,
        marginLeft: 3
    },
    whiteAreaSelectedView: {
        backgroundColor: brown_t2,
        height: 24,
        width: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center'
    },
    whiteAreaSelectedText: {
        fontSize: 10,
        color: white,
        fontWeight: 'bold'
    },
    hPN: {
        fontSize: 12,
        marginHorizontal: 15,
        fontWeight: 'bold',
        color: black_t3
    },
    brownArea: {
        position: 'absolute',
        bottom: 0
    },
    brownTop: {
        height: 39,
        width: Layout.screen.width,
    },
    brownView: {
        backgroundColor: yellow_t1,
        height: 240
    },
    brownAltView: {
        width: Layout.screen.width / 1.2,
        alignSelf: 'center',
        marginTop: 7
    },
    brownListView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    brownListTitle: {
        color: white,
        fontSize: 12,
        fontWeight: 'bold'
    },
    brownSpecialView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    hPWhite: {
        width: 15,
        height: 17,
        marginLeft: 3
    },
    brownListPrice: {
        color: white,
        fontSize: 16,
        fontWeight: 'bold'
    },
});
import { Mo<PERSON>View } from "moti";
import React from "react";
import { StyleSheet } from "react-native";
import { TouchableOpacity, Image, Text, View } from "react-native";
import Layout from "../constants/Layout";
import { white } from "../constants/Color";


export type HollyPuanList = {
    rightImg: any,
    status: boolean,
    setStatus: Function,
    indexNo: Number | any,
    logo: {
        type: Number,
        logoAlt: any,
        style: Object
    },
    children: any,
    backgroundColor: string
}

const HollyPuanList: React.FC<HollyPuanList> = ({
    rightImg = "",
    status = false,
    setStatus = () => { },
    indexNo = 0,
    logo: {
        type = 0,
        logoAlt = "",
        style = {}
    },
    children = <></>,
    backgroundColor = 'black'
}) => {

    const [statusTwo, setStatusTwo] = React.useState(false); // CONTROLLING THE CHILDREN VIEWING

    React.useEffect(() => {
        if (status) {
            setTimeout(() => {
                setStatusTwo(true);
            }, 400);
        } else {
            setStatusTwo(false);
        }
    }, [status]);

    return (
        <TouchableOpacity
            onPress={() => {
                setStatus(!status)
            }}
            activeOpacity={1}
            style={[
                styles.allEarningsAltView,
                {
                    marginLeft: 48 * indexNo,
                    zIndex: -indexNo,
                    width: Layout.screen.width > 400 ?
                        status ?
                            Layout.screen.width / 2.3
                            : 48
                        :
                        status ?
                            Layout.screen.width / 2.6
                            : 48,
                    left: -indexNo * 22
                }
            ]}
        >
            <MotiView
                from={{
                    width: 10,
                }}
                animate={{
                    width: status ?
                        Layout.screen.width - 187
                        :
                        10,
                }}
                transition={{ type: 'timing' }}
                style={[
                    styles.allEarningsMoti,
                    {
                        backgroundColor: backgroundColor,
                        borderTopLeftRadius: indexNo == 0 ? 10 : 0,
                        borderBottomLeftRadius: indexNo == 0 ? 10 : 0,
                        paddingLeft: status ? indexNo != 0 ? 40 : 0 : 0
                    }
                ]}
            >
                {
                    status && statusTwo ?
                        children
                        :
                        <></>
                }
            </MotiView>
            <View>
                <Image
                    source={rightImg}
                    style={styles.allEarningsRightImg}
                    resizeMode="cover"
                />
                {
                    type == 1 ?
                        <Text style={[
                            styles.allEarningsLogoAltText,
                            {
                                left: indexNo == 0 ? -25 : -15,
                            },
                            style
                        ]}>{logoAlt}</Text>
                        :
                        <Image
                            style={[styles.allEarningsLogoAltImg, style]}
                            source={logoAlt}
                        />
                }
            </View>
        </TouchableOpacity>
    )
}
export default HollyPuanList;

// -- STYLES -- //
const styles = StyleSheet.create({
    allEarningsAltView: {
        flexDirection: 'row',
        position: 'absolute',
    },
    allEarningsMoti: {
        zIndex: 2,
        height: 183
    },
    allEarningsRightImg: {
        width: 48,
        height: 184,
        bottom: 0,
        right: 10,
        zIndex: 1
    },
    allEarningsLogoAltText: {
        fontSize: 13,
        transform: [{ rotate: '270deg' }],
        position: 'absolute',
        zIndex: 2,
        top: 38,
        color: white,
        width: 80,
        fontFamily: 'MADE TOMMY'
    },
    allEarningsLogoAltImg: {
        zIndex: 2,
        position: 'absolute',
        top: 20,
        left: 18
    },
});
import React from "react";
import { View, Image, Text, TouchableOpacity } from "react-native";
import { StyleSheet } from "react-native";
import { black, green_t1, white } from "../constants/Color";
import { MotiView } from "moti";
import Layout from "../constants/Layout";
import { useFocusEffect } from "@react-navigation/native";
import { getImageURL } from "../networking/Server";
import { MainStore } from "../stores/MainStore";
const dayjs = require("dayjs");

export type DailyItem = {
    item: any,
    navigation: any
}

const DailyItem: React.FC<DailyItem> = ({
    item,
    navigation
}) => {

    // 1 - ATRIUM
    // 2 - PERFORMANCE STAGE
    // 3 - HOLLY GARDEN


    const [status, setStatus] = React.useState(false);

    useFocusEffect(
        React.useCallback(() => {
            setStatus(false);
        }, [])
    );

    const getDateFromDayOfWeek = (dayOfWeek: number) => {
        return dayjs().startOf('week').add(dayOfWeek - 1, 'day'); 
    };

    return (
        <TouchableOpacity
            onPress={() => {
                setStatus(!status)
                setTimeout(() => {
                    navigation.navigate("DailyActivityDetail", { activityId: item.id });
                }, 1000);
            }}
            activeOpacity={1}
            style={[styles.mainBack]}
        >
            <Image
                style={styles.leftDailyView}
                resizeMode="cover"
                source={require('../assets/root/dailyBackLeftGreen.png')}
            />

            

            {/* CONCERT NAME | IMAGE */}
            <View style={styles.dailyImgView}>
                <Image
                    resizeMode="cover"
                    style={styles.dailyImg}
                    source={{ uri: getImageURL(item?.image) }}
                />
                {
                    status ?
                        <Image
                            source={require('../assets/root/conDaiLoad.gif')}
                            style={styles.conDaiLoad}
                            resizeMode="contain"
                        />
                        :
                        <></>
                }
                <View style={styles.dailyNameView}>
                    <Text style={styles.dailyName}>
                        {
                            item?.name?.length > 12 ?
                                item?.name?.substring(0, 12)
                                :
                                item?.name
                        }
                    </Text>
                </View>
            </View>

            {/* MOTI VIEW */}
            <View style={styles.motiView}>
                <MotiView
                    from={{
                        width: 15,
                    }}
                    animate={{
                        width: status ? Layout.screen.width / 3.7 : 15
                    }}

                    transition={{
                        type: 'timing',
                        duration: 200
                    }}
                >

                    {/* CONCERT DETAIL */}
                    {
                        status ?
                            <View style={[styles.whiteBack, item?.stage === 3 ? { backgroundColor: "#666666" } : {}]}>
                                <View style={{ marginLeft: 15 }}>
                                <Text style={[styles.greenTitle, item?.stage === 3 ? { color: white } : {}]}>
                                {MainStore.language.opening_door}</Text>
                                <Text style={[styles.greenDesc, item?.stage === 3 ? { color: white } : {}]}>
                                {dayjs(item?.gateDateTime).format("HH:mm")}</Text>
                                </View>
                                <View style={styles.greenAltView}>
                                <Text style={[styles.greenTitle, item?.stage === 3 ? { color: white } : {}]}>{MainStore.language.scene}</Text>
                                <Text style={[styles.greenDesc, item?.stage === 3 ? { color: white } : {}]}>{dayjs(item?.dateTime).format("HH:mm")}</Text>
                                </View>
                                {
                                    item?.price ?
                                        <View style={styles.greenAltView}>
                                            <Text style={[styles.greenTitle, item?.stage === 3 ? { color: white } : {}]}>{MainStore.language.price}</Text>
                                            <Text style={[styles.greenDesc, item?.stage === 3 ? { color: white } : {}]}>{MainStore.language.free}</Text>
                                        </View>
                                        :
                                        <></>
                                }
                            </View>
                            :
                            <View style={[styles.whiteBack, item?.stage === 3 ? { backgroundColor: "#666666" } : {}]} />
                    }
                </MotiView>
                <Image
    style={styles.dailyBackLeft}
    resizeMode="cover"
    source={
        item?.stage === 3
            ? require('../assets/root/dailyBackLeftTwo.png')
            : require('../assets/root/dailyBackLeft.png')
    }
/>


                {/* ATRIUM LOGO */}
                {
    (() => {
        switch (item?.stage) {
            case 1:
                return (
                    <Image
                        style={styles.atriumND}
                        resizeMode="cover"
                        source={require('../assets/root/atriumNDGreen.png')}
                    />
                );
            case 2:
                return (
                    <Text style={styles.leftBlackText}>
                        <Text style={{ fontWeight: 'bold' }}>PERFORMANS</Text> SAHNE
                    </Text>
                );
            case 3:
                return (
                    <Text style={styles.leftBlackTextTwo}>
                    <Text style={{ fontWeight: 'bold'
                     }}>AFTER</Text> PARTY
                </Text>
                    
                );
            case 4:
                return (
                    <Text style={styles.leftBlackText}>
                    <Text style={{ fontWeight: 'bold' }}>MAGNUS</Text>
                </Text>
                );
            case 5:
                return (
                    <Text style={styles.leftBlackText}>
                    <Text style={{ fontWeight: 'bold' }}>PUB</Text> SAHNE
                </Text>
                );
            case 6:
                return (
                    <Text style={styles.leftBlackText}>
                        <Text style={{ fontWeight: 'bold' }}>PERFORMANS</Text> SAHNE 2
                    </Text>
                );
            default:
                return (
                    <Text style={styles.leftBlackText}></Text>
                );
        }
    })()
}

            </View>

            {/* DATE */}
            <View style={styles.leftDateView}>
    {(() => {
        if (item?.dayofweek !== undefined) {
            const eventDate = getDateFromDayOfWeek(item.dayofweek);
            return (
                <>
                    <Text style={styles.leftDateNumber}>{eventDate.format("DD")}</Text>
                    <Text style={styles.leftDate}>{eventDate.format("MMM")}</Text>
                </>
            );
        }
        return <Text style={styles.leftDate}>Tarih Yok</Text>;
    })()}
</View>

        </TouchableOpacity>
    )
}
export default DailyItem;

// -- STYLES -- //
const styles = StyleSheet.create({
    mainBack: {
        width: Layout.screen.width / 2,
        height: 127,
        borderRadius: 10,
        flexDirection: 'row',
       
    },
    leftDailyView: {
        height: 120,
        width: 41.5,
        top: 4,
        alignItems: 'center',
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    dailyImgView: {
        zIndex: -1,
        left: -1,
        top: 4,
        height: 120,

    },
    dailyImg: {
        height: 120,
        width: Layout.screen.width / 2.9,
        borderRadius: 10
    },
    dailyNameView: {
        height: 32,
        width: '100%',
        borderBottomRightRadius: 10,
        backgroundColor: black,
        position: 'absolute',
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center'
    },
    dailyName: {
        fontWeight: 'bold',
        color: white,
        fontSize: 12,
        textAlign: 'center'
    },
    conDaiLoad: {
        height: 16,
        width: 16,
        position: 'absolute',
        right: 5,
        top: 5
    },
    motiView: {
        position: 'absolute',
        left: 15,
        zIndex: -1,
        height: 123,
        flexDirection: 'row'
    },
    whiteBack: {
        backgroundColor: white,
        height: 120,
        marginTop: 4,
        justifyContent: 'center',
        paddingLeft: 20
    },
    greenAltView: {
        marginTop: 4,
        marginLeft: 15
    },
    greenTitle: {
        fontSize: 10,
        color: black
    },
    greenDesc: {
        fontSize: 10,
        color: black,
        fontWeight: 'bold'
    },
    dailyBackLeft: {
        width: 58,
        height: 120,
        left: -31,
        top:4,
        zIndex: -1,
        borderTopLeftRadius: 15,
        borderBottomLeftRadius: 15,
    },
    atriumND: {
        width: 4,
        height: 47,
        left: -48,
        zIndex: -1,
        alignSelf: 'flex-end',
        marginBottom: 5
    },
    leftBlackText: {
        left: -29,
        zIndex: 2,
        width: 80,
        color: green_t1,
        bottom: -18,
        fontSize: 6,
        transform: [{ rotate: '270deg' }]
    },
    leftBlackTextTwo: {
        left: -29,
        zIndex: 2,
        width: 80,
        color: white,
        bottom: -18,
        fontSize: 6,
        transform: [{ rotate: '270deg' }]
    },
    leftDateView: {
        position: 'absolute',
        marginLeft: 5,
        marginTop: 10,
        alignItems: 'center'
    },
    leftDateNumber: {
        color: white,
        fontSize: 18,
        fontFamily: 'MADE TOMMY',
        
    },
    leftDate: {
        color: white,
        fontWeight: 'bold',
        fontSize: 12,
        fontFamily: 'MADE TOMMY',
        textTransform: 'uppercase'

    }
});
import React, { useState } from 'react';
import { View, Text, Dimensions, Image } from 'react-native';
import {
  PanGestureHandler,
  TapGestureHandler,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  useAnimatedGestureHandler,
  runOnJS,
} from 'react-native-reanimated';

const AccordionItem = ({ title, content, imageSource, imageWidth, imageHeight, kavisrengi, color, width, handleToggle, itemsLength, index, offsetX }) => {
  const expandedWidth = Dimensions.get('window').width - itemsLength * 40;

// AccordionItem component içinde gestureHandler objesi tanımlandığı bölümde
const gestureHandler = useAnimatedGestureHandler({
  onStart: (_, ctx) => {
    ctx.startWidth = width.value;
    ctx.startX = offsetX.value;
  },
  onActive: (event, ctx) => {
    const diffX = event.translationX - ctx.startX;
    const newWidth = ctx.startWidth + diffX;
    
    // offsetX değerini sadece sürükleme sırasında güncelleyin
    offsetX.value = event.translationX;
    
    // Yeni genişlik değeri, 40 ile expandedWidth arasında olmalıdır
    if (newWidth <= expandedWidth && newWidth >= 40) {
      width.value = newWidth;
      const otherItemWidth = expandedWidth - newWidth;
      runOnJS(handleToggle)(otherItemWidth);
    } else if (newWidth < 40) { // Eğer yeni genişlik değeri 40'ın altındaysa
      runOnJS(handleToggle)(expandedWidth); // Açık olan öğeyi kapat
    }
  },
  onEnd: (_) => {
    if (width.value > 0) {
      runOnJS(handleToggle)();
    } else {
      width.value = withSpring(40);
    }
    offsetX.value = 0;
  },
});



  const animatedStyle = useAnimatedStyle(() => {
    
    return {
      width: width.value,
      backgroundColor: color,
      
    };
  });

  return (
    <TapGestureHandler>
      <Animated.View>
        <PanGestureHandler
          onGestureEvent={gestureHandler}
          onHandlerStateChange={({ nativeEvent }) => {
            if (nativeEvent.state === State.ACTIVE) {
              offsetX.value = nativeEvent.translationX;
            }
          }}
        >
          <Animated.View
            style={[
              animatedStyle,
              {
                height: 200,
                margin: 0,
              },
            ]}
          >
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View>
                <Text>{content}</Text>
              </View>
              <Image
                source={imageSource}
                style={{ width: imageWidth, height: imageHeight, resizeMode: 'cover', top: 10, right: 10 }}
              />
              <View style={{
                borderTopLeftRadius: 100,
                width: 10,
                height: 80,
                backgroundColor: kavisrengi,
                position: 'absolute',
                right: 0,
                top: 120
              }}>
              </View>
            </View>
          </Animated.View>
        </PanGestureHandler>
      </Animated.View>
    </TapGestureHandler>
  );
};

const AccordionMenu = ({ items }) => {
  const expandedWidth = Dimensions.get('window').width - items.length * 40;
  const [widths] = useState(items.map((_, i) => useSharedValue(i === 4 ? expandedWidth : 40)));
  const offsetX = useSharedValue(0);

  const handleToggle = (index, newWidth) => {
    const currentWidth = widths[index].value;
    
    // Eğer sürüklenen öğe zaten açıksa, kapatma işlemi yapma
    if (currentWidth === expandedWidth) return;
  
    // Diğer tüm öğeleri kapat
    widths.forEach((width, i) => {
      if (i !== index && width.value !== 40) {
        width.value = withSpring(40);
      }
    });
  
    // Sürüklenen öğenin genişliğini arttır
    widths[index].value = withSpring(expandedWidth);
  };
  

  return (
    <View style={{ flexDirection: 'row', backgroundColor: 'transparent' }}>
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          title={item.title}
          content={item.content}
          color={item.color}
          imageWidth={item.imageWidth}
          imageHeight={item.imageHeight}
          kavisrengi={item.kavisrengi}
          imageSource={item.imageSource}
          width={widths[index]}
          handleToggle={(newWidth) => handleToggle(index, newWidth)} // newWidth parametresini ekledim
          itemsLength={items.length}
          index={index}
          offsetX={offsetX}
        />
      ))}
    </View>
  );
};


export default AccordionMenu;

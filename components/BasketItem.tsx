import React from "react";
import { StyleSheet, View, Animated, TouchableOpacity, Image, Text } from "react-native";
import { black, black_t3, brown_t2, gray_t6, red_t3, white } from "../constants/Color";
import Layout from "../constants/Layout";
import { shadow } from "../constants/Shadow";
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { MainStore } from "../stores/MainStore";

export type BasketItem = {
    name: string,
    image: any,
    price: number,
    id: number,
    quantity: number,
    item: any,
    whenQuantity: any,
    onItemDeleted: any,
    countAgain: (counting: number, price: number) => void

}

const BasketItem: React.FC<BasketItem> = ({
    name = "",
    image = "",
    price = 0,
    quantity,
    id,
    item,
    whenQuantity,
    onItemDeleted,
    countAgain,

}) => {

    const translateX = new Animated.Value(0);
    let buttonWidth = 0;

    const handleGesture = Animated.event(
        [
            {
                nativeEvent: {
                    translationX: translateX,
                },
            },
        ],
        { useNativeDriver: true },
    );

    const onGestureStateChange = ({ nativeEvent }: any) => {
        if (nativeEvent.oldState === State.ACTIVE) {


            if (nativeEvent.translationX > buttonWidth / 2) {
                onSwipeRight();
            } else {
                onSwipeLeft();
            }
        }
    };

    const onSwipeLeft = () => {
        Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
        }).start();
    }


    const onSwipeRight = () => {
        Animated.spring(translateX, {
            toValue: 85,
            useNativeDriver: true,
        }).start();
    }

    return (
        <View style={styles.main}>

            {/* PRODUCT ITEM ANIMATION */}
            <PanGestureHandler
                onGestureEvent={handleGesture}
                onHandlerStateChange={onGestureStateChange}
            >
                {/* ANIMATED AREA */}
                <Animated.View style={{ transform: [{ translateX }] }}>
                    <View style={[styles.grayView, shadow]}>
                        <View style={[styles.imgWhiteView, shadow]}>
                            <Image
                                source={image}
                                style={styles.img}
                                resizeMode="contain"
                            />
                        </View>
                        <View style={styles.rightView}>

                            {/* TITLE */}
                            <Text style={styles.title}>{name}</Text>

                            {/* PRICE AND BUTTONS VIEW */}
                            <View style={styles.priceButtonView}>
                                <Text style={styles.price}>{price} ₺</Text>
                                <View style={styles.buttonsView}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            if (quantity > 1) {
                                                whenQuantity();
                                                MainStore.setQuantity(id, quantity - 1)
                                            }
                                        }}
                                        style={styles.minusView}
                                    >
                                        <Text style={{ fontSize: 18, fontWeight: '800', marginTop: -2, color: black }}>-</Text>
                                    </TouchableOpacity>
                                    <Text style={styles.buttonText}>{quantity}</Text>
                                    <TouchableOpacity
                                        onPress={() => {
                                            whenQuantity();
                                            MainStore.setQuantity(id, quantity + 1)
                                        }}
                                        style={styles.plusView}
                                    >
                                        <Text style={styles.plus}>+</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </Animated.View>
            </PanGestureHandler>

            {/* DELETE FROM BASKET TOUCH */}
            <TouchableOpacity
                onPress={() => {
                    MainStore.setBasket(item, true, "");
                    countAgain(0, 0);
                    onItemDeleted();
                }}
                style={styles.redView}
            >
                <Image
                    source={require('../assets/root/delete.png')}
                    style={styles.delete}
                />
            </TouchableOpacity>
        </View>
    )
}
export default BasketItem;

// -- STYLES -- // 
const styles = StyleSheet.create({
    main: { marginVertical: 15 },
    grayView: {
        backgroundColor: gray_t6,
        width: Layout.screen.width / 1.1,
        alignSelf: 'flex-end',
        minHeight: 112,
        borderTopLeftRadius: 22,
        borderBottomLeftRadius: 22,
        flexDirection: 'row',
        alignItems: 'center'
    },
    imgWhiteView: {
        width: 100,
        height: 100,
        backgroundColor: white,
        marginLeft: 5,
        borderTopLeftRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomLeftRadius: 20
    },
    img: {
        height: 70,
        width: 80
    },
    rightView: {
        marginLeft: 10,
        width: Layout.screen.width / 1.8
    },
    title: {
        fontWeight: 'bold',
        color: black_t3
    },
    priceButtonView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20
    },
    price: {
        color: brown_t2,
        fontWeight: 'bold'
    },
    buttonsView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    minusView: {
        width: 30,
        height: 30,
        borderRadius: 30,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    buttonText: {
        marginHorizontal: 15,
        fontSize: 14,
        fontWeight: '900',
        color: black
    },
    plusView: {
        width: 30,
        height: 30,
        borderRadius: 25,
        backgroundColor: brown_t2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    plus: { 
        color: white,
        fontSize: 17,
        marginTop: -2,
        fontWeight: '700'

    },
    redView: {
        backgroundColor: red_t3,
        height: 112,
        width: Layout.screen.width / 1.1,
        alignSelf: 'flex-end',
        borderTopLeftRadius: 22,
        borderBottomLeftRadius: 22,
        position: 'absolute',
        zIndex: -1,
        justifyContent: 'center'
    },
    delete: {
        height: 26.81,
        width: 26.81,
        marginLeft: 32.5
    },
});
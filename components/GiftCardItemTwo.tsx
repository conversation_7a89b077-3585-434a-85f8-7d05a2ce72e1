import React from "react";
import { StyleSheet, View, ImageBackground, Text } from "react-native";
import { black_t6, green_t1, white } from "../constants/Color";
import Layout from "../constants/Layout";

export type GiftCardItem = {
  type: number;
  value: number;
  nameLastName: string;
  code: string;
  item?: any;
  selectedCard?: boolean;
};

const GiftCardItemTwo: React.FC<GiftCardItem> = ({
  type,
  value,
  nameLastName,
  code,
  item = {},
  selectedCard = null,
}) => {
  return (
    <View
      style={[
        styles.mainAlt,
        {
          
        },
      ]}
    >
      <ImageBackground
        style={styles.cardSize} // Sabit küçük boyut
        source={
          type === 1
            ? require("../assets/menu/goldCard.png")
            : type === 2
            ? require("../assets/menu/silverCard.png")
            : require("../assets/menu/bronzeCard.png")
        }
      >
        <View style={styles.discountView}>
          <Text
            style={[styles.discount, { color: type === 2 ? black_t6 : white }]}
          >
          </Text>
        </View>
        <View style={styles.centerView}>
          <Text style={styles.centerTitle}>
            {type === 1 ? "Gold Card" : type === 2 ? "Silver Card" : "Bronz Card"}
          </Text>
          <Text style={styles.centerPrice}>{value}₺</Text>
        </View>
        <View style={styles.nameView}>
          <Text style={styles.name}>{nameLastName}</Text>
          <Text style={styles.cardNo}>{code}</Text>
        </View>
      </ImageBackground>
    </View>
  );
};

export default GiftCardItemTwo;

// -- STYLES -- //
const styles = StyleSheet.create({
  mainAlt: {
    marginTop: 5,
    alignItems: "center",
  },
  cardSize: {
    height: 171, // Sabit küçük yükseklik
    width: 250, // Sabit küçük genişlik
  },
  discountView: {
    position: "absolute",
    alignItems: "center",
    left: 3,
    top: 4,
  },
  discount: {
    fontFamily: "MADE TOMMY",
    fontSize: 10,
  },
  discounText: {
    fontFamily: "MADE TOMMY",
    fontSize: 4,
  },
  centerView: {
    position: "absolute",
    alignItems: "center",
    top: 50,
    right: Layout.screen.width / 6,
  },
  centerTitle: {
    color: white,
    letterSpacing: 1.05,
    fontFamily: "MADE TOMMY",
    fontSize: 18,
  },
  centerPrice: {
    color: white,
    fontFamily: "MADE TOMMY",
    letterSpacing: 1.05,
    fontSize: 20,
  },
  nameView: {
    position: "absolute",
    bottom: 20,
    left: 60,
  },
  name: {
    color: white,
    fontFamily: "College",
    fontSize: 12,
  },
  cardNo: {
    color: white,
    fontFamily: "College",
    fontSize: 16,
  },
});
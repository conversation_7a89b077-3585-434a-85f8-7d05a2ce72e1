import React, { useRef } from 'react';
import { Animated, StyleSheet, View, Text, PanResponder, Dimensions, Image, Platform } from 'react-native';
import { white } from '../constants/Color';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const BUTTON_WIDTH = SCREEN_WIDTH * 0.8;
const BUTTON_HEIGHT = 50;
const SLIDER_WIDTH = BUTTON_WIDTH * 0.4;
const SLIDER_MOVE_SPACE = BUTTON_WIDTH - SLIDER_WIDTH;

const SwipeButtonInitial = ({ onSwipe, buttonText }: any) => {
  const translateX = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > 5 && Math.abs(gestureState.dy) < 5;
      },
      onPanResponderMove: (evt, gestureState) => {
        const newX = gestureState.dx > SLIDER_MOVE_SPACE ? SLIDER_MOVE_SPACE : gestureState.dx;
        translateX.setValue(newX < 0 ? 0 : newX);
      },
      onPanResponderRelease: (evt, gestureState) => {
        if (gestureState.dx > SLIDER_MOVE_SPACE * 0.75) {
          Animated.spring(translateX, {
            toValue: SLIDER_MOVE_SPACE,
            useNativeDriver: true,
          }).start(() => {
            onSwipe('right');
            setTimeout(() => {
              Animated.spring(translateX, {
                toValue: 0,
                useNativeDriver: true,
              }).start();
            }, 300); // Buttonun geri kaymasını sağlamak için kısa bir gecikme
          });
        } else {
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const animatedSliderStyle = {
    transform: [
      {
        translateX: translateX.interpolate({
          inputRange: [0, SLIDER_MOVE_SPACE],
          outputRange: [0, SLIDER_MOVE_SPACE],
          extrapolate: 'clamp',
        }),
      },
    ],
  };

  const registerOpacity = translateX.interpolate({
    inputRange: [0, SLIDER_MOVE_SPACE * 0.5],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      <View style={styles.buttonBackground}>
        <Animated.View
          style={[styles.slider, animatedSliderStyle]}
          {...panResponder.panHandlers}
        >
          <Text style={styles.sliderText}>{buttonText}</Text>
        </Animated.View>
        <Animated.Text style={[styles.text, { opacity: registerOpacity }]}>
          <Image
            style={{
              height: 15,
              width: 35,
              marginTop: Platform.OS === 'android' ? 0 : 45,
            }}
            resizeMode='contain'
            source={require('../assets/root/initialRight.png')}
          />
        </Animated.Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: BUTTON_WIDTH,
    height: BUTTON_HEIGHT,
    backgroundColor: 'rgba(256,256,256,0.2)',
    borderRadius: BUTTON_HEIGHT / 2,
    justifyContent: 'center',
    overflow: 'hidden',
  },
  buttonBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  text: {
    height: 50,
    width: 35,
    paddingTop: Platform.OS === "android" ? 14 : 0,
    position: 'absolute',
    top: 2,
    right: 20,
  },
  slider: {
    width: SLIDER_WIDTH,
    height: '100%',
    backgroundColor: 'rgba(256,256,256,0.5)',
    borderRadius: BUTTON_HEIGHT / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderText: {
    color: white,
    fontWeight: 'bold',
  },
});

export default SwipeButtonInitial;

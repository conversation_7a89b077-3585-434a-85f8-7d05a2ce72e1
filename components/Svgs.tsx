import React from "react";
import { Icon, IIconProps } from "native-base";
import { <PERSON>, <PERSON>, Circle, Defs, Rect, ClipPath } from "react-native-svg";
import { white } from "../constants/Color";

export function BackIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 19 25" size={size} color={color}>
            <Path d="M1 12.5L18.5 1L6 12.5L18.5 24L1 12.5Z" fill={color} stroke={color} />
        </Icon>
    )
}

export function LogOut({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M533.8,296.3c0.5-16.7,0.4-33.3,0-50c0-2.3,0-4.7,0-7c0.1-14.3,0.3-28.6,0.2-43c0-5.8-4.3-9.7-10.5-9.7
		c-10.5-0.1-21,0-31.5,0c-29.3,0-58.6-0.1-87.9,0c-9.4,0-12.8,3.6-12.8,13.1c0,19.2,0,38.3,0,57.5c0,4.4-1.6,7.7-5.6,9.8
		c-3.5,1.8-6.8,1.4-9.9-0.6c-3.2-2.1-4.6-5.3-4.6-8.9c-0.1-20.2-0.4-40.3,0.1-60.5c0.4-17.8,13.9-30.4,31.8-30.4
		c39.8-0.1,79.7-0.1,119.5,0c18.5,0,31.7,13,31.7,31.4c0.1,67.5,0.1,135-0.1,202.5c0,18-13.4,30.9-31.6,30.9
		c-39.7,0.1-79.3,0.1-119,0c-18.6,0-31.8-12.2-32.3-30.9c-0.5-20-0.2-40-0.1-60c0-6.2,4.7-11,10.1-10.9c5.7,0,10,4.8,10,11.4
		c0.1,19.2,0,38.3,0,57.5c0,9.1,3.5,12.6,12.7,12.7c16.3,0.3,32.6,1,48.8,0.7c11-0.2,22-1.1,32.9-0.3c12.4,0.9,24.6-0.3,36.9-0.5
		c8-0.1,11.5-3.8,11.5-11.8c0-29.3,0-58.6,0-87.9c0-1.3-0.1-2.6-0.2-4C533.8,303.6,533.8,299.9,533.8,296.3z"/>
                <Path d="M324.9,287.9c1.9,1.3,3.6,0.8,5.2,0.8c49,0.1,97.9,0.1,146.9,0.2c2,0,4,0,6,0.1c6,0.4,10,4.3,10,9.7
		c0,5.7-4,9.8-10.2,9.9c-23.7,0.2-47.3,0.3-71,0.3c-27.7,0.1-55.3,0-83,0c-1.1,0-2.3,0.1-4.4,1.2c4.3,4,8.6,8,12.8,12
		c3.5,3.4,6.9,6.7,10.2,10.3c3.8,4.2,3.8,10.2,0,13.9c-3.9,3.9-9.7,4.3-13.9,0.2c-13.8-13.5-27.4-27.1-40.9-40.9
		c-4.3-4.4-4.1-9.8,0.2-14.2c13.3-13.5,26.7-26.9,40.2-40.2c4.5-4.5,10.6-4.4,14.7-0.2c4,4.1,3.7,10-0.9,14.7
		C339.8,273.1,332.4,280.3,324.9,287.9z"/>
            </G>
        </Icon >
    )
}


export function QrCode({ size, color }: any) {
    return (
        <Icon viewBox="0 0 35 35" size={size} color={color}>
            <G>
            <path d="M12.3332 8.20034V11.3004C12.3332 11.8711 11.8706 12.3337 11.2999 12.3337H8.19985C7.62915 12.3337 7.1665 11.8711 7.1665 11.3004V8.20034C7.1665 7.62964 7.62915 7.16699 8.19985 7.16699H11.2999C11.8706 7.16699 12.3332 7.62964 12.3332 8.20034Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.1665 17.5H12.3332" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M22.667 17.5V22.6667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 27.834H22.6667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 17.52L17.518 17.5" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8335 17.52L27.8515 17.5" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 22.687L17.518 22.667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8335 22.687L27.8515 22.667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8335 27.854L27.8515 27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 12.353L17.518 12.333" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.5 7.187L17.518 7.16699" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.3332 23.7003V26.8004C12.3332 27.3711 11.8706 27.8337 11.2999 27.8337H8.19985C7.62915 27.8337 7.1665 27.3711 7.1665 26.8004V23.7003C7.1665 23.1296 7.62915 22.667 8.19985 22.667H11.2999C11.8706 22.667 12.3332 23.1296 12.3332 23.7003Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8337 8.20034V11.3004C27.8337 11.8711 27.3711 12.3337 26.8004 12.3337H23.7003C23.1296 12.3337 22.667 11.8711 22.667 11.3004V8.20034C22.667 7.62964 23.1296 7.16699 23.7003 7.16699H26.8004C27.3711 7.16699 27.8337 7.62964 27.8337 8.20034Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8335 2H33.0002V7.16674" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.8335 33.0007H33.0002V27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.16674 2H2V7.16674" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.16674 33.0007H2V27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </G>
        </Icon >
    )
}





export function Calendar({ size, color }: any) {
    return (
        <Icon viewBox="0 0 29 32" size={size} color={color}>
            <Path d="M22 4.7998H7C4.23858 4.7998 2 7.30701 2 10.3998V24.3998C2 27.4926 4.23858 29.9998 7 29.9998H22C24.7614 29.9998 27 27.4926 27 24.3998V10.3998C27 7.30701 24.7614 4.7998 22 4.7998Z" stroke="black" stroke-width="2.22" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M9.5 2V7.6" stroke="black" stroke-width="2.22" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M19.5 2V7.6" stroke="black" stroke-width="2.22" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M2 13.2002H27" stroke="black" stroke-width="2.22" stroke-linecap="round" stroke-linejoin="round" />
        </Icon >
    )
}

export function CreditCard({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M560,365.3c-2.6,3-3.2,7.1-5.8,10.2c-6.1,7.3-13.9,10.5-23.3,10.5c-50.1,0-100.3,0-150.4,0
		c-21.8,0-43.6-0.1-65.5,0c-14.6,0.1-25-5.6-30-19.9c-0.1-0.4-0.7-0.6-1-0.9c0-44.7,0-89.3,0-134c1-0.2,1.1-1.1,1.4-1.8
		c5.1-12.6,14.1-18.9,27.6-18.9c72.6,0,145.2,0,217.9-0.1c14.3,0,25.8,9.2,28.2,20.8c0.1,0.4,0.6,0.7,0.9,1
		C560,276.6,560,320.9,560,365.3z M422.3,316.9c-41.8,0-83.6,0-125.5-0.1c-3.5,0-4.9,0.6-4.8,4.5c0.3,11.8,0.1,23.7,0.1,35.5
		c0.1,12.9,7.6,20.7,20.4,20.7c73,0.1,146,0.1,218.9,0c12.8,0,20.3-7.8,20.4-20.7c0.1-11.7-0.2-23.3,0.1-35c0.1-4.3-1.2-5.1-5.3-5.1
		C505.2,317,463.8,316.9,422.3,316.9z M422,270.3c41.5,0,83-0.1,124.5,0.1c4.8,0,5.7-1.4,5.5-5.8c-0.3-7.8-0.1-15.7-0.1-23.5
		c0-15.1-7.2-22.3-22.4-22.3c-23.2,0-46.3,0-69.5,0c-48.5,0-97,0-145.5,0c-15.2,0-22.4,7.2-22.4,22.2c0,8.2,0.3,16.3-0.1,24.5
		c-0.2,4.2,1.3,4.8,5,4.8C338.7,270.3,380.3,270.3,422,270.3z M422,278.4c-41.8,0-83.6,0-125.4-0.1c-3.4,0-4.7,0.6-4.5,4.3
		c0.3,6.8,0.3,13.7,0,20.5c-0.2,4.2,0.7,5.6,5.3,5.5c31.5-0.2,63-0.1,94.5-0.1c51.6,0,103.3,0,154.9,0.1c4,0,5.6-0.7,5.3-5.1
		c-0.4-6.5-0.4-13,0-19.5c0.2-4.4-0.8-5.8-5.6-5.8C504.9,278.5,463.4,278.4,422,278.4z"/>
                <Path d="M378.7,344.4c-3.6,0-7.3,0-10.9,0c-2.6,0-4.1-1.4-4.2-3.9c-0.1-2.5,1.6-4.1,4-4.1c7.6-0.1,15.2-0.1,22.8,0
		c2.5,0,4,1.7,3.9,4.2c-0.1,2.5-1.7,3.8-4.2,3.8C386.3,344.4,382.5,344.4,378.7,344.4z"/>
                <Path d="M326.5,344.4c-3.6,0-7.3,0-10.9,0c-2.5,0-4.2-1.4-4.3-3.9c-0.1-2.5,1.6-4.1,4.1-4.1c7.6-0.1,15.2-0.1,22.9,0
		c2.5,0,4,1.6,3.9,4.2c-0.1,2.6-1.7,3.8-4.2,3.9C334.1,344.4,330.3,344.4,326.5,344.4z"/>
                <Path d="M484.3,244.6c0-10.7,11.3-18.4,21.1-14.1c2.6,1.1,4.5,1.4,7.2,0.1c6.2-3,13.7-0.8,17.9,4.6
		c4.4,5.6,4.4,13.2,0,18.9c-4.1,5.4-11.6,7.6-17.9,4.6c-2.7-1.3-4.7-1-7.3,0.1C495.4,263,484.3,255.4,484.3,244.6z M529.7,244.6
		c0-8.5-9.6-14-16.9-9.5c-2.8,1.7-4.6,2-7.4,0.1c-4.8-3.2-10.8-1.9-14.3,2.4c-3.4,4.1-3.4,10.2,0.1,14.2c3.6,4.2,9.7,5.4,14.4,2.2
		c2.6-1.8,4.4-2,7-0.1c3.5,2.6,7.5,2.4,11.3,0.4C527.7,252.3,529.4,248.8,529.7,244.6z"/>
            </G>
        </Icon >
    )
}

export function Home({ size, color }: any) {
    return (
        <Icon viewBox="0 0 27 26" size={size} color={color}>
            <Path d="M20.0296 26.0076H6.00645C2.68837 26.0076 0 23.3072 0 19.9891V11.0157C0 9.36877 1.01722 7.29799 2.32508 6.28077L8.85224 1.19467C10.814 -0.331157 13.9505 -0.403815 15.9849 1.02513L23.4687 6.26866C24.9098 7.27377 26.036 9.42931 26.036 11.1853V20.0012C26.036 23.3072 23.3476 26.0076 20.0296 26.0076ZM9.96634 2.62362L3.43918 7.70973C2.57938 8.38787 1.81647 9.92581 1.81647 11.0157V19.9891C1.81647 22.302 3.69348 24.1912 6.00645 24.1912H20.0296C22.3425 24.1912 24.2195 22.3142 24.2195 20.0012V11.1853C24.2195 10.0227 23.384 8.41209 22.4273 7.75817L14.9435 2.51463C13.5629 1.54585 11.2863 1.59429 9.96634 2.62362Z" fill={color} />
            <Path d="M13.0186 21.1642C12.5221 21.1642 12.1104 20.7525 12.1104 20.256V16.6231C12.1104 16.1266 12.5221 15.7148 13.0186 15.7148C13.5151 15.7148 13.9268 16.1266 13.9268 16.6231V20.256C13.9268 20.7525 13.5151 21.1642 13.0186 21.1642Z" fill={color} />
        </Icon >
    )
}

export function Messages({ size, color }: any) {
    return (
        <Icon viewBox="0 0 23 22" size={size} color={white}>
            <Path d="M1 14.1538V4C1 2.34315 2.34315 1 4 1H19C20.6569 1 22 2.34315 22 4V14.1538C22 15.8107 20.6569 17.1538 19 17.1538H10.1846C10.0654 17.1538 9.95025 17.1964 9.8597 17.2738L6.32486 20.295C6.00038 20.5723 5.5 20.3417 5.5 19.9149V17.6538C5.5 17.3777 5.27614 17.1538 5 17.1538H4C2.34315 17.1538 1 15.8107 1 14.1538Z" stroke={color} stroke-width="2" />
        </Icon >
    )
}

export function Qr({ size, color }: any) {
    return (
        <Icon viewBox="0 0 35 35" size={size} color={color}>
            <Path d="M12.3337 8.20034V11.3004C12.3337 11.8711 11.8711 12.3337 11.3004 12.3337H8.20034C7.62964 12.3337 7.16699 11.8711 7.16699 11.3004V8.20034C7.16699 7.62964 7.62964 7.16699 8.20034 7.16699H11.3004C11.8711 7.16699 12.3337 7.62964 12.3337 8.20034Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M7.16699 17.5H12.3337" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M22.667 17.5V22.6667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M17.5 27.834H22.6667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M17.5 17.52L17.518 17.5" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.834 17.52L27.852 17.5" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M17.5 22.687L17.518 22.667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.834 22.687L27.852 22.667" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.834 27.854L27.852 27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M17.5 12.353L17.518 12.333" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M17.5 7.187L17.518 7.16699" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M12.3337 23.7003V26.8004C12.3337 27.3711 11.8711 27.8337 11.3004 27.8337H8.20034C7.62964 27.8337 7.16699 27.3711 7.16699 26.8004V23.7003C7.16699 23.1296 7.62964 22.667 8.20034 22.667H11.3004C11.8711 22.667 12.3337 23.1296 12.3337 23.7003Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.8337 8.20034V11.3004C27.8337 11.8711 27.3711 12.3337 26.8004 12.3337H23.7003C23.1296 12.3337 22.667 11.8711 22.667 11.3004V8.20034C22.667 7.62964 23.1296 7.16699 23.7003 7.16699H26.8004C27.3711 7.16699 27.8337 7.62964 27.8337 8.20034Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.834 2H33.0007V7.16674" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M27.834 33.0007H33.0007V27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M7.16674 2H2V7.16674" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <Path d="M7.16674 33.0007H2V27.834" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
        </Icon >
    )
}

export function Notifications({ size, color }: any) {
    return (
        <Icon viewBox="0 0 22 26" size={size} color={white}>
            <Path d="M10.5699 23.2589C7.76914 23.2589 4.96839 22.8141 2.31189 21.9246C1.30218 21.576 0.532872 20.8668 0.196301 19.9412C-0.15229 19.0157 -0.0320865 17.9939 0.520851 17.0804L1.9032 14.7845C2.19168 14.3037 2.45613 13.342 2.45613 12.7771V9.30321C2.45613 4.83163 6.09831 1.18945 10.5699 1.18945C15.0415 1.18945 18.6837 4.83163 18.6837 9.30321V12.7771C18.6837 13.33 18.9481 14.3037 19.2366 14.7965L20.607 17.0804C21.1238 17.9458 21.22 18.9916 20.8714 19.9412C20.5228 20.8908 19.7655 21.6121 18.8159 21.9246C16.1714 22.8141 13.3707 23.2589 10.5699 23.2589ZM10.5699 2.99251C7.096 2.99251 4.25919 5.8173 4.25919 9.30321V12.7771C4.25919 13.6546 3.89858 14.9528 3.45383 15.7101L2.07148 18.0059C1.80703 18.4507 1.73491 18.9195 1.89118 19.3162C2.03542 19.7249 2.39603 20.0374 2.88887 20.2057C7.91339 21.8885 13.2385 21.8885 18.263 20.2057C18.6957 20.0614 19.0323 19.7369 19.1885 19.3042C19.3448 18.8714 19.3088 18.4026 19.0683 18.0059L17.686 15.7101C17.2292 14.9287 16.8806 13.6425 16.8806 12.7651V9.30321C16.8806 5.8173 14.0558 2.99251 10.5699 2.99251Z" fill={color} />
            <Path d="M12.806 3.31763C12.7219 3.31763 12.6377 3.30561 12.5536 3.28157C12.205 3.1854 11.8684 3.11328 11.5439 3.0652C10.5221 2.93297 9.53646 3.0051 8.61089 3.28157C8.27432 3.38975 7.91374 3.28157 7.68535 3.02914C7.45697 2.77671 7.38484 2.4161 7.51707 2.09155C8.0099 0.829407 9.21191 0 10.5822 0C11.9526 0 13.1546 0.817386 13.6474 2.09155C13.7676 2.4161 13.7075 2.77671 13.4791 3.02914C13.2988 3.22146 13.0464 3.31763 12.806 3.31763Z" fill={color} />
            <Path d="M10.5691 25.9998C9.37911 25.9998 8.22516 25.519 7.38377 24.6776C6.54234 23.8362 6.06152 22.6822 6.06152 21.4922H7.86458C7.86458 22.2014 8.15303 22.8986 8.65789 23.4034C9.16275 23.9083 9.85993 24.1968 10.5691 24.1968C12.0597 24.1968 13.2737 22.9827 13.2737 21.4922H15.0768C15.0768 23.9804 13.0574 25.9998 10.5691 25.9998Z" fill={color} />
        </Icon >
    )
}

export function Profile({ size, color }: any) {
    return (
        <Icon viewBox="0 0 21 24" size={size} color={white}>
            <Path d="M10.5 12.4444C11.8924 12.4444 13.2277 11.8825 14.2123 10.8823C15.1969 9.88215 15.75 8.5256 15.75 7.11111C15.75 5.69662 15.1969 4.34007 14.2123 3.33988C13.2277 2.33968 11.8924 1.77778 10.5 1.77778C9.10761 1.77778 7.77226 2.33968 6.78769 3.33988C5.80312 4.34007 5.25 5.69662 5.25 7.11111C5.25 8.5256 5.80312 9.88215 6.78769 10.8823C7.77226 11.8825 9.10761 12.4444 10.5 12.4444ZM10.5 14.2222C8.64348 14.2222 6.86301 13.473 5.55025 12.1394C4.2375 10.8058 3.5 8.9971 3.5 7.11111C3.5 5.22513 4.2375 3.41639 5.55025 2.0828C6.86301 0.749204 8.64348 0 10.5 0C12.3565 0 14.137 0.749204 15.4497 2.0828C16.7625 3.41639 17.5 5.22513 17.5 7.11111C17.5 8.9971 16.7625 10.8058 15.4497 12.1394C14.137 13.473 12.3565 14.2222 10.5 14.2222ZM19.25 23.1111V20.4444C19.25 19.7372 18.9734 19.0589 18.4812 18.5588C17.9889 18.0587 17.3212 17.7778 16.625 17.7778H4.375C3.67881 17.7778 3.01113 18.0587 2.51884 18.5588C2.02656 19.0589 1.75 19.7372 1.75 20.4444V23.1111C1.75 23.3469 1.65781 23.573 1.49372 23.7397C1.32962 23.9063 1.10706 24 0.875 24C0.642936 24 0.420376 23.9063 0.256282 23.7397C0.0921874 23.573 0 23.3469 0 23.1111V20.4444C0 19.2657 0.460936 18.1352 1.28141 17.3017C2.10188 16.4683 3.21468 16 4.375 16H16.625C17.7853 16 18.8981 16.4683 19.7186 17.3017C20.5391 18.1352 21 19.2657 21 20.4444V23.1111C21 23.3469 20.9078 23.573 20.7437 23.7397C20.5796 23.9063 20.3571 24 20.125 24C19.8929 24 19.6704 23.9063 19.5063 23.7397C19.3422 23.573 19.25 23.3469 19.25 23.1111Z" fill={color} />
        </Icon>
    )
}

export function Location({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M416.9,164c51.2,0.1,90.7,39.8,90.1,87.4c-0.2,14.7-6.1,27.8-12.3,40.8c-14.4,30.2-33.8,57.2-53.9,83.8
		c-4.9,6.5-10,12.8-15.2,19.1c-3.4,4.1-7.1,4.2-10.4,0.1c-27.5-33.9-53.3-68.9-72.1-108.7c-16-34-11.3-65.2,12.9-93.2
		C372.7,173.9,394.7,164.6,416.9,164z M420.4,382.3c9.1-11.6,17.8-22.8,26-34.3c15.1-21.3,29.7-43,40.6-66.9
		c4.2-9.1,7.7-18.6,8-28.7c1.6-52.7-52.2-90.5-101.4-71.3c-34.3,13.4-61.9,55-39.1,101.5C372,318.6,395.5,350.6,420.4,382.3z"/>
                <Path d="M426.9,433.6c-28.3-0.1-49.6-2.4-69.9-10.4c-6.5-2.6-12.6-6-17.6-11.1c-7.6-7.8-8-18.7-0.3-26.4
		c11.5-11.4,26.4-15.7,41.9-18.2c3-0.5,5.6,1.3,6.1,4.7c0.6,3.6-0.9,6.2-4.6,7.2c-6.1,1.7-12.2,3.2-18.3,5.1
		c-5.3,1.7-10.2,4-14.5,7.7c-5.4,4.6-5.3,8.7,0.2,13.2c6.8,5.5,14.8,8.5,23,10.4c35.2,8.1,70.3,8.4,104.9-3.4
		c4.6-1.6,8.9-3.8,12.7-6.9c5.5-4.5,5.6-8.4,0.3-13.2c-5.2-4.7-11.7-6.9-18.2-9c-4.3-1.4-8.7-2.3-13-3.4c-4-1-6.2-3.5-5.3-7.6
		c0.8-4,3.8-5.1,7.7-4.3c13.4,2.5,26.1,6.8,36.8,15.6c10.5,8.6,11,20.4,1.1,29.7c-10,9.3-22.7,13-35.6,15.9
		C450,432.5,435.2,433.7,426.9,433.6z"/>
                <Path d="M457.4,244.2c-0.1,19.9-16.1,36.1-35.7,36.1c-20,0-36.1-16.3-36-36.3c0.1-19.7,16.3-35.8,36-35.7
		C441.5,208.3,457.4,224.3,457.4,244.2z M397.8,243.6c0,13.6,10.3,24.3,23.7,24.5c13.2,0.1,23.8-10.7,23.8-24.3
		c0-13-10.8-23.7-23.9-23.6C408.4,220.3,397.9,230.7,397.8,243.6z"/>
            </G>
        </Icon>
    )
}

export function Contract({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M436.7,377.4c-4.5,13.5-11.3,16.8-24.2,11.5c-1,2.4-1.7,5-3.4,7.2c-5,6.4-13.9,7.7-20.5,3
		c-1.5-1.1-3-2.2-4.2-3.5c-3.1-3.4-5.8-4.9-10.9-2.6c-5.8,2.7-13.3-1.7-16.2-8.3c-1-2.4-1.8-3.9-5.1-3.9
		c-6.3-0.1-10.9-4.3-12.6-10.9c-0.5-1.8-0.8-2.6-3-2.8c-8.9-1-12.9-5.3-13.7-14.2c-0.2-2-0.7-2.2-2.5-2.6c-9.6-2-13.8-9-11.4-18.4
		c0.2-0.6,0.7-1.6,0.5-1.8c-4-4.2-5.6-10.2-10.4-13.8c-1.6-1.2-2.7-0.9-4.2,0.5c-4.1,3.6-8.7,3.7-13.4,1.2
		c-7.5-3.9-15.1-7.7-22.6-11.7c-6.5-3.4-8.5-9.4-5.1-16c12.7-24.7,25.4-49.4,38.2-74c3.1-5.9,9.2-7.9,15.3-4.9
		c8.1,4,16,8.1,23.9,12.3c4.2,2.2,6.5,5.8,5.9,10.6c-0.3,2.7,1,3.6,2.9,5c10.6,7.8,22.6,5.3,34.2,5.3c1.5,0,2.5-1,3.4-2
		c11.1-12.2,25-13.1,40-10.2c10.3,2,20.6,3.7,30.9,5.5c4.3,0.7,8.5,1,12.8-0.4c4.1-1.3,7.6-2,7.1-7.9c-0.3-3.9,3-6.9,6.9-8.3
		c9-3,17.9-6.1,27-8.9c5.6-1.7,11.3,1.3,13.2,6.9c9,26.8,17.9,53.6,26.6,80.4c2,6.2-1,11.7-7.2,13.9c-8.5,3-17,5.8-25.5,8.6
		c-4.5,1.5-8.8,0.7-11.8-3c-2.2-2.6-4-2.2-5.6,0c-4.6,6.1-10.3,10.8-16.7,14.9c-1.8,1.2-1.5,1.9-0.2,3.4c4.9,5.4,5.8,11.4,2.5,17.9
		c-3.3,6.3-8.9,8.8-17.2,8c-1.3-0.1-2.2-0.6-2.7,1.5C454.9,376.2,448.2,380,436.7,377.4z M374.9,362.8c1.4,0.6,2.8,1.1,4.1,1.8
		c5.9,3.3,9.8,10.8,6.6,16c-3.2,5.2-0.1,6.9,2.8,9.3c1,0.9,2,1.8,3,2.6c4.3,3.5,8.8,3.4,12-0.2c3.2-3.7,2.6-8.5-1.7-12.3
		c-4.9-4.3-9.8-8.4-14.8-12.7c-2.2-1.9-4.7-4-2-7c2.5-2.8,4.8-0.5,6.8,1.3c7.6,6.5,15.1,13.1,22.7,19.6c4.9,4.3,9.8,4.5,13.1,0.5
		c3.3-3.9,2.5-8.3-2.3-12.5c-8-7-16.1-14-24.2-20.9c-2.1-1.8-4.4-3.6-2-6.6c2.4-2.9,4.7-1.1,6.8,0.8c0.7,0.7,1.5,1.3,2.3,2
		c9.6,8.3,19.1,16.6,28.8,24.8c4.1,3.5,9.2,3,12.4-0.8c3.2-3.7,2.9-8.4-0.8-12.1c-1.4-1.4-3-2.6-4.5-3.9c-9-7.9-18.1-15.7-27.1-23.6
		c-2-1.7-3-3.9-0.9-6c1.8-1.9,4-1.1,5.8,0.5c0.5,0.4,1,0.9,1.5,1.3c11.2,9.7,22.4,19.4,33.6,29c4.9,4.2,10.1,4.4,13.7,0.4
		c3.8-4.2,3.1-9.4-2.1-13.8c-24-20.8-48-41.6-72.1-62.4c-3.7-3.2-5.5-3-8.4,1c-1.2,1.7-1.7,3.5-1.9,5.6c-0.6,6.9-2.6,13.4-6.4,19.2
		c-4.3,6.5-9.9,11.3-18.5,10.5c-5.6-0.5-9.4-4.2-8.8-9.4c1.2-10,0.5-20-0.1-29.9c-0.4-6.6,2.1-11.9,7.1-16.2
		c2.4-2.1,4.7-4.3,7.1-6.4c-11.3,0.6-21.4-1-30.4-7.5c-2.6-1.9-3.3,0.4-4.2,2.2c-9.8,19.1-19.6,38.1-29.6,57.1
		c-1.6,3.1-3.5,5.6,1.6,7.3c2,0.6,3.3,2.7,4.3,4.7c1.6,3.1,3.5,6,6.1,8.3c10.5-10.4,15.7-10.3,25.4,0.4c1.6,1.7,2.2,0.4,3.2-0.2
		c10.9-5.7,21.4,0.5,21.4,12.8c0,1.3-0.5,2.2,1.5,2.9C377.9,344.4,380.6,351.1,374.9,362.8z M402.8,237.4
		c-5.9-0.4-10.8,1.9-15.1,5.6c-7.9,6.7-15.6,13.5-23.5,20.2c-4.1,3.4-5.7,7.5-5.2,12.8c0.8,9.1,1.2,18.3,0.1,27.4
		c-0.6,4.7,2.7,4.4,5.5,3.9c4.5-0.9,7.5-4.1,9.8-7.9c3.2-5.4,4.6-11.3,5.4-17.5c0.7-5.9,4-10.3,9.6-12.4c5.1-2,8.8,0.9,12.5,4.1
		c18.5,16.1,37,32.1,55.6,47.9c3.7,3.2,6.8,9.2,12,8.1c4.9-1.1,8.9-5.6,12.8-9.2c2.7-2.5,4.1-6.5,7.5-7.9c4.1-1.7,3.3-3.9,2.3-7
		c-6.8-20.2-13.5-40.3-20-60.6c-1-3.1-2.4-3.7-5-2.4c-8,4-16.2,2.7-24.5,1.1C429.4,241.1,416.2,238.5,402.8,237.4z M330.7,234.1
		c-0.1-2.8-1.4-4-3.3-5c-7-3.5-13.9-7.2-20.8-10.8c-5.6-2.9-6.7-2.6-9.7,3.1c-12.3,23.6-24.5,47.2-36.8,70.8
		c-1.9,3.6-1.6,6.2,2.3,8.1c7.4,3.7,14.8,7.6,22.1,11.5c3.6,1.9,5.8,0.7,7.6-2.8c12.3-24,24.7-47.9,37.1-71.8
		C329.9,236.2,330.3,234.9,330.7,234.1z M535.9,301.9c0-0.1-0.3-1.2-0.7-2.3c-2.8-8.5-5.6-17-8.4-25.5
		c-5.6-16.9-11.2-33.7-16.8-50.6c-1.1-3.3-2.5-5.9-6.7-4.5c-8.2,2.7-16.4,5.4-24.6,8.1c-3.5,1.2-4.5,3.4-3.3,7
		c8.6,26,17.2,52,25.8,77.9c1,3.1,2.9,4.7,6.5,3.5c8.3-2.8,16.7-5.5,25-8.3C534.7,306.7,536.2,305.5,535.9,301.9z M357.7,336.1
		c-0.2-2.9-1.6-5.3-4.4-6.6c-3.1-1.3-5.9-0.7-8.1,1.9c-4.5,5.5-9,11.1-13.4,16.7c-3,3.7-2.7,8,0.5,10.6c3.3,2.7,7.3,2.2,10.4-1.6
		c4.3-5.2,8.5-10.6,12.8-15.9C356.7,339.7,357.4,338.1,357.7,336.1z M371,353.5c-0.1-3-1.5-5-4.2-6.2c-3-1.3-5.8-0.8-7.8,1.5
		c-4.2,4.7-8.2,9.6-12,14.7c-1.9,2.5-1.5,5.4,0.5,7.9c2.1,2.6,5.2,4.1,7.9,2.2c6.7-4.7,11.4-11.3,15.4-18.5
		C371.1,354.6,371,354,371,353.5z M314.9,336.7c0.4,3.1,2,5.1,4.8,6.4c2.8,1.3,5.2,0.8,7.2-1.4c2.1-2.3,4.1-4.8,6.1-7.2
		c2-2.3,2.1-4.9,0.5-7.4c-1.6-2.5-4-4-7.1-3.7C323.1,323.7,314.8,333.3,314.9,336.7z M372.8,369.5c-3.1,0-8.9,5.1-9.7,8.3
		c-0.9,3.4,0.7,5.8,3.4,7.6c3.8,2.6,6.8,1.3,11.4-4.4c1.9-2.3,2.6-4.5,1.1-7.2C377.5,371.2,375.3,369.6,372.8,369.5z"/>
            </G>
        </Icon>
    )
}

export function Language({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M519,417.3c-8-9.6-16-19.1-23.9-28.8c-1.6-2-3.5-2.1-5.7-2.1c-20.8,0-41.7,0-62.5,0
		c-20.2,0-35.5-14.9-35.7-35.1c-0.2-20.3-0.2-40.7,0-61c0.2-18.1,12.8-32.8,29.4-35.2c3.3-0.5,5.2-0.1,4.6,3.8c-0.1,0.6-0.1,1.3,0,2
		c0.6,5.1,0.1,8.3-6.4,10c-8,2.1-12.7,8.9-12.8,17.3c-0.3,21.7-0.4,43.3,0,65c0.2,10.8,9,18.5,20.2,18.5c23,0.1,46,0.1,69,0
		c3.3,0,5.6,0.9,7.6,3.5c6.2,7.6,12.6,15,19.9,23.7c0-8.3,0.2-15.3-0.1-22.3c-0.2-3.8,0.8-5.2,4.9-5c7.6,0.3,15.3,0.2,23,0
		c10.8-0.2,19.4-8.4,19.5-19.3c0.2-21.2,0.2-42.3,0-63.5c-0.1-11-8.8-19.4-19.9-19.5c-26.3-0.1-52.7-0.1-79,0c-3.9,0-5.5-0.6-5.3-5
		c0.4-9.6,0.1-9.7,9.7-9.7c24,0,48-0.1,72,0c18.9,0.1,30.5,8.6,36.3,26.7c0.2,0.7,0.1,1.7,1.2,1.9c0,1.3,0,2.7,0,4
		c-1.1,0.6-1,1.6-1,2.6c0,13.4,0.1,26.9,0.1,40.3c0,2.5-0.3,5-0.1,7.5c0.4,5.2-1.2,10.5,0.9,15.6c0,2,0,4,0,6c-1,0-1.3,0.8-1.6,1.6
		c-7.7,19-17.3,25.5-37.9,25.5c-8.3,0-8.1,0-8.4,8.2c-0.3,7.6,1.3,15.2-1.1,22.7C530.3,417.3,524.7,417.3,519,417.3z"/>
                <Path d="M437,180.3c4.2,4.6,9.3,8.3,11.9,14.2c2.4,5.4,4,10.9,4,16.9c0,20.2,0.1,40.3-0.1,60.5
		c-0.1,17.8-12.3,31.9-29.6,35.1c-3.7,0.7-4.9-0.3-4.5-3.9c0.1-0.8,0.1-1.7,0-2.5c-0.6-5,0.1-8,6.3-9.5c7.7-1.8,12.6-8.3,12.8-16.2
		c0.5-22.3,0.5-44.6,0-67c-0.2-9.7-9.2-17.7-18.9-17.7c-42-0.1-84-0.1-125.9,0c-10.2,0-19,8.4-19.1,18.7c-0.3,21.7-0.3,43.3,0,65
		c0.2,10.4,8.7,18.4,19.1,18.7c8,0.2,16,0.2,24,0c3.2-0.1,4.4,0.7,4.3,4.1c-0.3,7.2-0.1,14.4-0.1,23.1c7.3-8.7,13.8-16.1,20.1-23.9
		c2-2.4,4-3.4,7.2-3.3c8.7,0.2,17.3,0.2,26,0c2.9-0.1,3.6,0.9,3.6,3.7c0.3,11.2,0.3,12-10.7,10.9c-11.9-1.2-20.6,1.8-27.2,12.2
		c-4.4,7-10.6,12.8-15.9,19.2c-3.1,3.8-6.9,5.4-11.5,3.5c-4.3-1.7-6.1-5.2-6-9.9c0.1-6.8-0.2-13.7,0-20.5c0.1-3.4-0.6-4.7-4.3-4.5
		c-4.5,0.3-9.1,0.4-13.5-0.3c-14.1-2.3-23.3-10.5-28.2-23.8c-0.5-1.3-0.2-3.1-2-3.7c0-1.7,0-3.3,0-5c1-0.6,1-1.6,1-2.6
		c0-7.5-0.1-15-0.1-22.4c0-6.6,0-13.3,0-19.9c-0.3-7,1.2-14.1-0.9-21c0-1.7,0-3.3,0-5c1.1-0.1,1.2-1,1.4-1.7
		c2.5-9.1,7.9-16,15.6-21.3C329.7,180.3,383.3,180.3,437,180.3z"/>
                <Path d="M467.5,298.8c-7.8,0.5-10-2.2-8.4-9.2c0.3-1.3,0.9-1.7,2-1.7c1.5,0,3,0,4.5,0c8.1-0.1,8.2-0.1,9.2-7.8
		c0.3-2.3,1.2-2.8,3.2-2.4c1.1,0.2,2.3,0.5,3.5,0.4c4.4-0.3,6.6,1.2,5.6,5.9c-0.7,3.2,1,3.6,3.6,3.2c6.3-0.8,12.6-1.3,18.8-2.4
		c3.1-0.6,4.1,0.1,4.5,3.1c0.9,7.8,1,7.8-6.9,8.7c-6,0.6-12,0.9-17.9,1.5c-6,0.7-2.5,5.4-2.9,8.1c-0.5,3.5,2.4,1.2,3.8,1.5
		c4.5,1,8.3-4.1,12.9-1.6c6.1,3.3,12.9,5.7,17.1,11.7c3.8,5.4,4,11.6,2.9,17.8c-1,5.3-4,9.5-8.6,12.3c-4.7,2.9-10,4.4-15.5,5.3
		c-1.7,0.3-3,0.2-4.4-1.4c-5-5.9-4.9-6.3,2.5-8.3c3.4-0.9,6.7-2.2,9.4-4.4c5.5-4.3,6.2-12.1,1.6-16.7c-2.1-2.1-3.7-3-5.6,0.5
		c-3,5.6-7,10.6-11.5,15c-6.4,6.2-14.2,10-23.2,10.2c-7.7,0.2-12.6-4.7-12.4-12.4c0.2-10.9,6.8-17.6,15.7-22.6
		c2.7-1.5,5.3-11.8,3.1-13.9C471.9,297.4,469.2,299.1,467.5,298.8z M475,323c-5.7,2.7-8.6,6.7-8.3,10.9c0.3,3,2.2,3.7,4.5,3.1
		c2-0.5,5.6,0.4,5.1-3.7C476,329.9,475.5,326.6,475,323z M487,326.2c2.5-2.3,4.3-4.7,4.9-8C485.1,318.2,485.1,318.2,487,326.2z"/>
                <Path d="M386.2,268.2c-4.7,0-8.9-0.1-13,0c-2.2,0.1-2.7-1.2-3.4-2.9c-1.7-4-1.3-10-5.1-11.9c-3.4-1.8-8.5-0.4-12.8-0.4
		c-12-0.1-12.1-0.1-15.8,11.1c-1.1,3.4-2.8,4.2-6.1,4.1c-10.6-0.2-10.6,0-6.8-10c6.9-18.2,13.9-36.3,20.7-54.5c1.1-3,2.5-4,5.7-4
		c11.1,0,11.1-0.1,14.9,10.1c6.6,17.6,13.2,35.2,19.7,52.8C384.8,264.3,385.4,266,386.2,268.2z M353.5,217.6c-0.4,0-0.8-0.1-1.2-0.1
		c-2.5,7.3-5,14.7-7.7,22c-0.7,1.8-0.2,2.4,1.6,2.4c4.3,0,8.7-0.1,13,0c1.8,0,2.5-0.4,1.8-2.3C358.5,232.3,356,224.9,353.5,217.6z"
                />
            </G>
        </Icon>
    )
}

export function Refund({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M284,350.3c3.9-7,9.7-9.6,17.7-9c6.8,0.5,13.7,0.1,20.5,0.1c3.7,0,7.3,0.9,9.4,3.8c2.3,3,4.2,2,6.7,0.7
		c8.8-4.7,17.9-8.8,27.5-11.6c6.3-1.9,12.5-1.5,18.6,0.7c20.7,7.5,40,17.9,59.4,28.4c3.2,1.7,5.7,4.3,6.9,7.6c1,2.9,2.3,2.8,4.8,2
		c18-6.3,36.1-12.5,54.2-18.7c1.7-0.6,3.4-1.3,5.2-1.7c8.3-1.8,15.3,1.5,18.1,8.6c3,7.3,0.2,14.9-7.3,19.1
		c-17.9,10.2-35.9,20.3-54,30.3c-15.4,8.5-31,16.7-47.5,23.1c-2.4,0.9-4.9,1.8-7.3,2.7c-3.3,0-6.7,0-10,0c-4.2-2.1-8.6-4-12.6-6.5
		c-6.3-4-12.6-8.1-18.5-12.6c-7.6-5.8-15.7-8.1-25.2-5.8c-3.9,0.9-7.8,1.5-11.8,2c-2.1,0.3-2.9,1.1-3.4,3.3
		c-1.8,7.6-6,10.8-13.8,10.9c-7,0.1-14-0.3-20.9,0.1c-7.9,0.5-13.5-2.3-16.8-9.5C284,395.6,284,372.9,284,350.3z M335.9,405.2
		c3-0.5,5.5-0.8,7.9-1.4c15.2-4,28.9-1.5,41,9.1c3.4,2.9,7.4,5,11.1,7.7c10,7.4,20,8.2,31.6,2.7c31.9-15,62.4-32.6,93.1-50
		c3.6-2,6.1-4.6,4.4-8.8c-1.8-4.6-5.7-4.4-9.7-3.1c-0.6,0.2-1.3,0.4-1.9,0.7c-19.2,6.6-38.3,13.2-57.5,19.8
		c-2.6,0.9-4.4,2.1-5.9,4.7c-4.5,8.3-14.4,11.2-22.8,6.7c-8.8-4.7-17.3-9.9-26-14.8c-2.6-1.5-4.4-3.4-2.7-6.4
		c1.9-3.3,4.5-2.5,7.3-0.9c7.7,4.5,15.5,9,23.3,13.5c5.7,3.3,10.9,2.3,13.5-2.3c2.6-4.7,0.8-9.4-5-12.6c-13.6-7.4-27.8-13.6-41.3-21
		c-16-8.7-30.4-7.1-46.4,0.5c-10.7,5.1-16.4,10.6-14.2,22.9c0.9,4.8,0.1,10,0.1,15C335.9,393,335.9,399,335.9,405.2z M327.1,384.6
		c0-9.5-0.1-19,0-28.4c0.1-4.2-1.5-6.1-5.8-6.1c-7.6,0.2-15.3,0.1-23,0c-4.1-0.1-5.6,1.8-5.6,5.7c0.1,19.1,0.1,38.3,0,57.4
		c0,4.3,1.9,6,6.1,5.9c7.5-0.1,15-0.1,22.5,0c4.2,0,5.9-1.9,5.9-6.1C327,403.5,327.1,394.1,327.1,384.6z"/>
                <Path d="M560,264.3c-2.3,2.1-1.8,5.1-2.4,7.7c-11.2,43.9-49.1,71.9-94.2,69.5c-1.8-0.1-3.6,0-5.5,0
		c0.1,2.3,1.9,3.5,2.9,5.1c1.5,2.3,1.5,4.5-0.7,6.2c-2.1,1.6-4.4,1.4-6.1-0.9c-3.5-4.8-7.1-9.7-10.5-14.6c-1.7-2.4-1.3-4.8,1.2-6.5
		c4.7-3.4,9.4-6.8,14.2-10.2c2.4-1.7,4.8-1.6,6.5,1c1.6,2.4,0.6,4.4-1.5,6c-1.8,1.3-3.6,2.7-6.2,4.7c24.1,2.8,44.9-2.8,63-17.3
		c26.4-21.1,36.7-55.1,27.2-88.1c-8.8-30.3-36.6-53.6-68.9-57.5c-2.5-0.3-5-0.3-7.5-0.4c-2.4-0.1-4.8,0-6.1-2.5
		c-1.3-2.5,0.3-4.4,1.6-6.3c4.3,0,8.7,0,13,0c0.2,0.8,0.8,0.9,1.5,1c37.4,5.3,66.3,31.3,75.8,67.8c0.9,3.4,0.6,7.2,2.7,10.2
		C560,247.6,560,255.9,560,264.3z"/>
                <Path d="M469.5,311.5c-33.2,0.1-60.3-26.9-60.5-60.2c-0.1-33.6,26.9-60.9,60.3-60.9c33.1,0,60.4,27.1,60.6,60.3
		C530.1,284.1,502.9,311.5,469.5,311.5z M417.9,251c0,28.8,22.9,52,51.4,52.1c28.4,0.1,51.8-23.2,51.9-51.7
		c0.1-29-22.8-52.1-51.8-52.1C440.6,199.2,417.9,222,417.9,251z"/>
                <Path d="M305.3,410.4c-2.6-0.4-4.2-1.9-4-4.6c0.1-2.4,1.8-4,4.2-3.9c2.6,0,4.4,1.5,4.4,4.3
		C309.8,408.9,308.1,410.3,305.3,410.4z"/>
                <Path d="M459,238.7c-0.2,2.5,0.8,4.1,3.3,4.9c3.4,1,6.8,1.3,10.3,1.7c9.7,1,14.5,4.6,15.8,11.7
		c1.4,7.6-2.9,15.6-10.4,19.2c-2.5,1.2-4.5,2.1-4.3,5.5c0.1,2.2-1.7,3.6-4,3.7c-2.5,0.1-4.5-1.5-4.4-3.8c0.2-3.5-1.9-4.3-4.3-5.5
		c-4.9-2.3-7.9-6.5-9.7-11.5c-0.8-2.4-0.6-4.8,2.1-5.9c2.8-1.1,4.9-0.1,6,2.8c2,5.1,5.2,8.5,11.2,8.1c4.2-0.3,7.2-2.4,8.8-6.3
		c2-5,0.6-7.6-4.6-8.7c-2.9-0.6-5.9-0.8-8.9-1.1c-8.8-1-13.5-4.4-15-10.8c-1.7-7.3,1.8-15.2,8.7-19.1c3.3-1.9,6.7-3,6-8
		c-0.3-1.9,2-3.3,4.3-3.1c2.7,0.2,4.2,2,4,4.3c-0.4,3.8,1.6,5,4.6,6.3c4.2,1.9,7,5.5,8.8,9.8c1.2,2.8,2.1,5.8-1.3,7.4
		c-3.6,1.8-5.3-0.7-6.7-3.8c-3.1-6.8-9.8-9-15.6-5.3C460.6,232.7,459.3,235.4,459,238.7z"/>
            </G>
        </Icon>
    )
}

export function Lock({ size, color }: any) {
    return (
        <Icon viewBox="0 0 841.9 595.3" size={size} color={color}>
            <G>
                <Path d="M410,180.3c1.3,0,2.7,0,4,0c5.3,1.7,10.7,1.7,16,0c0.7,0,1.3,0,2,0c1.3,1.9,3.5,1.5,5.3,1.9
		c28.3,6.6,49.2,30.9,50.9,59.8c0.6,9.8,0.4,19.6,0.3,29.4c0,3.5,0.5,5.2,4.6,5.8c7.9,1.1,13.1,6,16.2,13.2c0.6,1.3,0.1,3.1,1.8,3.8
		c0,0.3,0,0.7,0,1c-1.6,1.1-1,2.7-1,4.1c0,31.1,0,62.2,0,93.3c0,1.6-0.7,3.4,1,4.6c0,1,0,2,0,3c-3.6,8.2-9.3,14.2-18,17
		c-0.7,0-1.3,0-2,0c-1.5-1.7-3.4-1-5.2-1c-43.3,0-86.6,0-129.9,0c-1.5,0-3,0.2-4.5,0.3c-6.8-1-11.7-4.7-15.3-10.5
		c-1.2-1.9-1.2-4.4-3.2-5.8c0-0.7,0-1.3,0-2c1.6-1.1,1-2.7,1-4.1c0-31.6,0-63.2,0-94.8c0-1.4,0.6-3-1-4.1c0-0.7,0-1.3,0-2
		c0.4-0.3,0.9-0.5,1.1-0.9c3.1-9,9.3-14.4,18.8-15.6c3.1-0.4,2.6-2.3,2.6-4.2c0.1-10-0.3-20,0.3-29.9c1.7-29.5,21.9-53.3,50.7-60.4
		C407.8,182,409.6,182.4,410,180.3z M422.2,402.3c21.3,0,42.7,0,64,0c7.6,0,9.8-2.3,9.8-9.8c0-30.5,0-61,0-91.5
		c0-7.5-2.3-9.7-9.9-9.7c-42.7,0-85.3,0-128,0c-7.9,0-10.1,2.2-10.1,10c0,30.3,0,60.7,0,91c0,7.8,2.2,10,10.1,10
		C379.5,402.3,400.8,402.3,422.2,402.3z M421.7,276.5c16,0,32-0.1,48,0.1c3.2,0,4.4-0.6,4.3-4.1c-0.3-9,0.1-18-0.2-27
		c-0.8-26.3-20.6-47.5-46.7-50.1c-25.8-2.6-49.2,14.1-55.4,39.6c-2.9,12.2-0.9,24.6-1.6,36.8c-0.2,3.7,0.8,4.8,4.6,4.7
		C390.4,276.4,406,276.5,421.7,276.5z"/>
                <Path d="M351.5,416.6c1.5-0.1,3-0.3,4.5-0.3c43.3,0,86.6,0,129.9,0c1.7,0,3.7-0.7,5.2,1c-46.3,0-92.7,0-139,0
		C351.8,417,351.7,416.8,351.5,416.6z"/>
                <Path d="M333,295.3c1.6,1.1,1,2.7,1,4.1c0,31.6,0,63.2,0,94.8c0,1.4,0.7,3-1,4.1C333,363.9,333,329.6,333,295.3z" />
                <Path d="M511,397.3c-1.6-1.3-1-3.1-1-4.6c0-31.1,0-62.2,0-93.3c0-1.4-0.7-3,1-4.1C511,329.3,511,363.3,511,397.3z" />
                <Path d="M430,180.3c-5.3,1.7-10.7,1.7-16,0C419.3,180.3,424.7,180.3,430,180.3z" />
                <Path d="M414.6,366c0-1.8-0.1-3.7,0-5.5c0.2-2.6-0.5-4.1-3-5.5c-9.6-5.2-13.7-15.1-11-25.3
		c2.7-9.7,11.2-16.3,21.3-16.3c10.1,0,18.7,6.6,21.4,16.2c2.8,10.2-1.3,20.1-10.9,25.3c-2.4,1.3-3.3,2.7-3.1,5.4
		c0.2,4.1,0.2,8.3,0,12.5c-0.2,4.6-3,7.2-7.3,7.2c-4.3,0-7.1-2.6-7.4-7.1C414.5,370.7,414.6,368.3,414.6,366
		C414.6,366,414.6,366,414.6,366z M429.4,335.7c0-4.3-2.9-7.2-7.1-7.3c-4.5-0.1-7.6,2.9-7.6,7.4c0,4.4,3.2,7.5,7.6,7.4
		C426.4,343,429.4,339.9,429.4,335.7z"/>
            </G>
        </Icon>
    )
}

export function PasswordLock({ size, color }: any) {
    return (
        <Icon viewBox="0 0 25 29" size={size} color={color}>
            <Path d="M23.6111 10.2727H20.8333V7.0625C20.8333 5.18941 20.0285 3.39303 18.596 2.06856C17.1634 0.744083 15.2204 0 13.1944 0H11.8056C9.7796 0 7.83661 0.744083 6.40405 2.06856C4.97148 3.39303 4.16667 5.18941 4.16667 7.0625V10.2727H1.38889C1.02053 10.2727 0.667263 10.408 0.406796 10.6488C0.146329 10.8896 0 11.2163 0 11.5568V26.9659C0 27.3065 0.146329 27.6331 0.406796 27.8739C0.667263 28.1147 1.02053 28.25 1.38889 28.25H23.6111C23.9795 28.25 24.3327 28.1147 24.5932 27.8739C24.8537 27.6331 25 27.3065 25 26.9659V11.5568C25 11.2163 24.8537 10.8896 24.5932 10.6488C24.3327 10.408 23.9795 10.2727 23.6111 10.2727ZM6.94444 7.0625C6.94444 5.87053 7.4566 4.72739 8.36823 3.88454C9.27987 3.04169 10.5163 2.56818 11.8056 2.56818H13.1944C14.4837 2.56818 15.7201 3.04169 16.6318 3.88454C17.5434 4.72739 18.0556 5.87053 18.0556 7.0625V10.2727H6.94444V7.0625ZM22.2222 25.6818H2.77778V12.8409H22.2222V25.6818Z" fill={color} />
            <Path d="M6.94466 21.1872C8.09525 21.1872 9.02799 20.3249 9.02799 19.2611C9.02799 18.1973 8.09525 17.335 6.94466 17.335C5.79407 17.335 4.86133 18.1973 4.86133 19.2611C4.86133 20.3249 5.79407 21.1872 6.94466 21.1872Z" fill={color} />
            <Path d="M12.5003 21.1872C13.6509 21.1872 14.5837 20.3249 14.5837 19.2611C14.5837 18.1973 13.6509 17.335 12.5003 17.335C11.3497 17.335 10.417 18.1973 10.417 19.2611C10.417 20.3249 11.3497 21.1872 12.5003 21.1872Z" fill={color} />
            <Path d="M18.055 21.1872C19.2056 21.1872 20.1383 20.3249 20.1383 19.2611C20.1383 18.1973 19.2056 17.335 18.055 17.335C16.9044 17.335 15.9717 18.1973 15.9717 19.2611C15.9717 20.3249 16.9044 21.1872 18.055 21.1872Z" fill={color} />
        </Icon>
    )
}


export function MessagesIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 26 26" size={size} color={color}>
            <Path d="M12.9638 26C12.1317 26 11.3479 25.5779 10.7931 24.8423L8.98423 22.4304C8.94805 22.3822 8.80334 22.3219 8.74304 22.3098H8.14007C3.11132 22.3098 0 20.9471 0 14.1698V8.14007C0 2.80983 2.80983 0 8.14007 0H17.7876C23.1178 0 25.9276 2.80983 25.9276 8.14007V14.1698C25.9276 19.5 23.1178 22.3098 17.7876 22.3098H17.1846C17.0881 22.3098 17.0037 22.3581 16.9434 22.4304L15.1345 24.8423C14.5798 25.5779 13.7959 26 12.9638 26ZM8.14007 1.80891C3.82282 1.80891 1.80891 3.82282 1.80891 8.14007V14.1698C1.80891 19.6206 3.67811 20.5009 8.14007 20.5009H8.74304C9.35807 20.5009 10.0575 20.8507 10.4314 21.3451L12.2403 23.757C12.6623 24.3117 13.2653 24.3117 13.6874 23.757L15.4963 21.3451C15.8942 20.8145 16.5213 20.5009 17.1846 20.5009H17.7876C22.1048 20.5009 24.1187 18.487 24.1187 14.1698V8.14007C24.1187 3.82282 22.1048 1.80891 17.7876 1.80891H8.14007Z" fill={color} />
            <Path d="M12.9637 12.9646C12.2884 12.9646 11.7578 12.4219 11.7578 11.7587C11.7578 11.0954 12.3005 10.5527 12.9637 10.5527C13.627 10.5527 14.1697 11.0954 14.1697 11.7587C14.1697 12.4219 13.6391 12.9646 12.9637 12.9646Z" fill={color} />
            <Path d="M17.788 12.9646C17.1126 12.9646 16.582 12.4219 16.582 11.7587C16.582 11.0954 17.1247 10.5527 17.788 10.5527C18.4512 10.5527 18.9939 11.0954 18.9939 11.7587C18.9939 12.4219 18.4633 12.9646 17.788 12.9646Z" fill={color} />
            <Path d="M8.14051 12.9646C7.46518 12.9646 6.93457 12.4219 6.93457 11.7587C6.93457 11.0954 7.47724 10.5527 8.14051 10.5527C8.80377 10.5527 9.34644 11.0954 9.34644 11.7587C9.34644 12.4219 8.81583 12.9646 8.14051 12.9646Z" fill={color} />
        </Icon>
    )
}

export function MailIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 29 21" size={size} color={color}>
            <Path d="M27.1875 0H1.8125C1.3318 0 0.870779 0.201136 0.530869 0.55916C0.190959 0.917184 0 1.40277 0 1.90909L0 19.0909C0 19.5972 0.190959 20.0828 0.530869 20.4408C0.870779 20.7989 1.3318 21 1.8125 21H27.1875C27.6682 21 28.1292 20.7989 28.4691 20.4408C28.809 20.0828 29 19.5972 29 19.0909V1.90909C29 1.40277 28.809 0.917184 28.4691 0.55916C28.1292 0.201136 27.6682 0 27.1875 0ZM24.8312 2.38636L15.2613 11.5691C15.0538 11.7703 14.782 11.882 14.5 11.882C14.218 11.882 13.9462 11.7703 13.7387 11.5691L4.16875 2.38636H24.8312ZM2.26562 18.6136V3.78L12.2162 13.3255C12.8387 13.9292 13.6539 14.2643 14.5 14.2643C15.3461 14.2643 16.1613 13.9292 16.7838 13.3255L26.7344 3.78V18.6136H2.26562Z" fill={color}/>
        </Icon>
    )
}

export function PhoneIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 22 25" size={size} color={color}>
            <Path d="M11.7618 21.1095C13.6673 22.273 15.9107 23.1121 18.5422 23.471C19.1808 23.5582 19.7243 22.9374 19.7243 22.1578V19.2521C19.7243 18.6326 19.3768 18.0927 18.8815 17.9425L15.8851 17.0336C15.5059 16.9186 15.1048 17.0535 14.8285 17.3888L11.7618 21.1095ZM11.7618 21.1095C8.29527 18.9929 5.94722 15.8022 4.41644 12.4732M4.41644 12.4732C2.95801 9.30166 2.2414 6.00464 2.00618 3.39147C1.93789 2.63286 2.44425 2 3.07324 2H5.46119C5.99154 2 6.44815 2.45417 6.55217 3.08511L7.3691 8.04084C7.44205 8.4834 7.32787 8.94091 7.06484 9.26004L4.41644 12.4732Z" stroke={color} stroke-width="3"/>
        </Icon>
    )
}

export function WriteIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 25 24" size={size} color={color}>
            <Rect x="1" y="5.125" width="17.8595" height="17.7984" rx="1" stroke="black" stroke-width="2"/>
            <Rect width="14.634" height="5.29219" transform="matrix(0.708195 -0.706016 0.708195 0.706016 7.96973 13.2275)" fill="black"/>
            <Path d="M18.6934 2.55078L20.5436 0.706269C20.9347 0.316346 21.5688 0.316346 21.96 0.706269L24.2915 3.03061C24.6826 3.42053 24.6826 4.05272 24.2915 4.44264L22.4413 6.28715L18.6934 2.55078Z" fill="black"/>
            <Path d="M6.00341 18.8676L7.36922 13.786L11.1007 17.5059L6.00341 18.8676Z" fill="black"/>
        </Icon >
    )
}

export function LinkIcon({ size, color }: any) {
    return (
        <Icon viewBox="0 0 22 20"  size={size} color={color}>
            <Path d="M16.1692 0C15.6729 0.0251426 15.1767 0.0754279 14.708 0.226284C13.9637 0.47771 13.2469 0.85485 12.6404 1.40799C12.4658 1.50888 12.3184 1.64426 12.209 1.80411C12.0996 1.96396 12.031 2.1442 12.0083 2.33149C11.9856 2.51879 12.0093 2.70834 12.0777 2.88614C12.1462 3.06394 12.2576 3.22544 12.4037 3.35868C12.5498 3.49193 12.7269 3.59351 12.9218 3.65593C13.1168 3.71834 13.3246 3.73999 13.53 3.71926C13.7354 3.69854 13.933 3.63597 14.1083 3.5362C14.2835 3.43642 14.432 3.30198 14.5426 3.14283C14.8459 2.86626 15.2043 2.71541 15.5902 2.58969C16.5551 2.28798 17.7406 2.41369 18.5125 3.14283C19.5877 4.12339 19.5877 5.75766 18.5125 6.76337L14.3772 10.5348C13.1642 11.641 12.1717 11.7416 11.4549 11.7165C10.7381 11.6913 10.3246 11.3896 10.3246 11.3896C10.1653 11.3071 9.9897 11.254 9.80789 11.2333C9.62608 11.2126 9.44159 11.2248 9.26495 11.2692C9.08831 11.3135 8.92298 11.3892 8.7784 11.4918C8.63382 11.5945 8.51282 11.722 8.42231 11.8673C8.3318 12.0126 8.27355 12.1727 8.25089 12.3385C8.22823 12.5043 8.2416 12.6726 8.29024 12.8337C8.33888 12.9948 8.42184 13.1456 8.53437 13.2774C8.64691 13.4093 8.78682 13.5196 8.94612 13.6022C8.94612 13.6022 9.88346 14.1553 11.2619 14.2307C12.6404 14.3062 14.5702 13.8285 16.2519 12.2696L20.3872 8.49821C22.5376 6.53709 22.5376 3.36911 20.3872 1.43313C19.6153 0.729137 18.7055 0.301712 17.713 0.100571C17.2168 0 16.6654 0 16.1692 0.0251426V0ZM10.6554 5.80795C9.27694 5.75766 7.37469 6.18509 5.74812 7.69365L1.61278 11.465C-0.537594 13.4262 -0.537594 16.5941 1.61278 18.5301C3.15664 19.9381 5.36216 20.3404 7.29198 19.7118C8.03634 19.4604 8.75313 19.0833 9.35965 18.5301C9.53416 18.4292 9.68157 18.2939 9.79098 18.134C9.90038 17.9742 9.96898 17.7939 9.99171 17.6066C10.0144 17.4193 9.9907 17.2298 9.92226 17.052C9.85383 16.8742 9.74244 16.7127 9.59633 16.5794C9.45023 16.4462 9.27315 16.3446 9.07819 16.2822C8.88324 16.2198 8.67539 16.1981 8.47002 16.2189C8.26465 16.2396 8.06702 16.3021 7.89174 16.4019C7.71646 16.5017 7.56802 16.6361 7.45739 16.7953C7.15414 17.0719 6.79574 17.2227 6.40977 17.3484C5.44486 17.6501 4.2594 17.5244 3.48747 16.7953C2.41228 15.8147 2.41228 14.1804 3.48747 13.1747L7.62281 9.40335C8.72556 8.39764 9.69048 8.27193 10.4624 8.29707C11.2343 8.32221 11.7581 8.52336 11.7581 8.52336C11.9197 8.63384 12.1058 8.71061 12.3035 8.74824C12.5011 8.78588 12.7054 8.78346 12.9019 8.74115C13.0984 8.69885 13.2823 8.61769 13.4407 8.50342C13.599 8.38915 13.7278 8.24455 13.8181 8.07986C13.9084 7.91516 13.9579 7.73439 13.9632 7.55031C13.9684 7.36624 13.9293 7.18336 13.8485 7.01461C13.7677 6.84586 13.6472 6.69536 13.4956 6.57374C13.3441 6.45212 13.165 6.36236 12.9712 6.3108C12.9712 6.3108 12.0338 5.80795 10.6554 5.75766V5.80795Z" fill="black"/>
        </Icon >
    )
}

export function Shaman({ size, color }: any) {
    return (
        <Icon viewBox="0 0 236 66"  size={size} color={color}>
            <Path d="M13.2903 27.1768H15.7608V34.5882H13.5903C13.4246 34.5882 13.2903 34.4539 13.2903 34.2882V27.1768Z" fill="#E74C3B"/>
            <Rect x="13.2903" y="51.8838" width="2.47049" height="7.41148" fill="#E74C3B"/>
            <Path d="M23.1716 41.998L23.1716 44.4685L16.9954 44.4685L16.9954 41.998L23.1716 41.998Z" fill="#E74C3B"/>
            <Path d="M18.743 17.2363C17.5862 15.7353 10.2607 11.4975 9.31547 19.8453C8.86246 23.7592 13.2897 25.9405 19.9493 27.6723C26.107 29.2735 30.5831 35.4984 30.5831 43.3244C30.5831 61.5851 12.86 64.1936 3.40771 56.3676" stroke="#303030" stroke-width="5.5" stroke-linecap="round"/>
            <Path d="M7.7632 49.9369C8.91305 51.0868 10.3371 51.9245 11.9007 52.3709C13.4644 52.8173 15.1161 52.8576 16.6996 52.4881C18.2832 52.1186 19.7465 51.3514 20.9511 50.2591C22.1557 49.1668 23.0619 47.7853 23.5842 46.2453C24.1064 44.7053 24.2273 43.0576 23.9356 41.4579C23.6438 39.8581 22.949 38.3592 21.9168 37.1027C20.8846 35.8461 19.5491 34.8735 18.0364 34.2767C16.5237 33.68 14.8839 33.4787 13.2718 33.692L13.5957 36.14C14.7916 35.9818 16.008 36.1311 17.1302 36.5738C18.2523 37.0165 19.243 37.738 20.0087 38.6701C20.7744 39.6022 21.2899 40.7142 21.5063 41.9009C21.7227 43.0876 21.633 44.3099 21.2456 45.4523C20.8582 46.5947 20.186 47.6195 19.2923 48.4298C18.3987 49.2401 17.3133 49.8092 16.1385 50.0833C14.9638 50.3574 13.7386 50.3275 12.5786 49.9964C11.4187 49.6652 10.3623 49.0438 9.5093 48.1908L7.7632 49.9369Z" fill="#E74C3B"/>
            <Path d="M18.2315 43.2311C18.2315 45.2778 16.5724 46.9369 14.5258 46.9369C12.4792 46.9369 10.8201 45.2778 10.8201 43.2311C10.8201 41.1845 12.4792 39.5254 14.5258 39.5254C16.5724 39.5254 18.2315 41.1845 18.2315 43.2311ZM12.6504 43.2311C12.6504 44.2669 13.4901 45.1065 14.5258 45.1065C15.5615 45.1065 16.4012 44.2669 16.4012 43.2311C16.4012 42.1954 15.5615 41.3558 14.5258 41.3558C13.4901 41.3558 12.6504 42.1954 12.6504 43.2311Z" fill="#E74C3B"/>
            <Path d="M56 32.1198V40.7666V44.5M43.5 27.1788H67.5M55.288 27V22.2379M55.288 17.2969V22.2379M55.288 22.2379H60.229" stroke="#42B3BA" stroke-opacity="0.992157" stroke-width="50" stroke-linecap="round"/>
            <Path d="M42.9363 49.4111H56.524M67.6412 49.4111H56.524M56.524 49.4111V54.3521M56.524 59.2931V54.3521M56.524 54.3521H51.583" stroke="#43B4BB" stroke-width="50" stroke-linecap="round"/>
            <Path d="M41.7012 38.292H68.8766" stroke="#303030" stroke-width="5.5" stroke-linecap="round"/>
            <Path d="M41.7012 14.8232V62.9979M68.8766 14.8232V62.9979" stroke="#303030" stroke-width="5.5" stroke-linecap="round"/>
            <Path d="M91.1314 29.1805C87.9364 28.3604 85.1062 26.4981 83.0889 23.8884C81.0716 21.2786 79.9824 18.0705 79.9937 14.772L82.4518 14.7804C82.4423 17.532 83.3509 20.2081 85.0337 22.3851C86.7165 24.5621 89.0773 26.1156 91.7425 26.7996L91.1314 29.1805Z" fill="#DFAB13"/>
            <Path d="M98.5024 29.1805C101.697 28.3604 104.528 26.4981 106.545 23.8884C108.562 21.2786 109.651 18.0705 109.64 14.772L107.182 14.7804C107.191 17.532 106.283 20.2081 104.6 22.3851C102.917 24.5621 100.556 26.1156 97.8913 26.7996L98.5024 29.1805Z" fill="#DFAB13"/>
            <Path d="M94.8164 22.2344V48.5364M94.8164 48.5364H90.1653M94.8164 48.5364H99.7578M84.9348 49.4098L94.8168 59.2918L105.934 49.4098" stroke="#DFAB13" stroke-width="50" stroke-linecap="round"/>
            <Path d="M175.127 29.1805C171.933 28.3604 169.102 26.4981 167.085 23.8884C165.068 21.2786 163.978 18.0705 163.99 14.772L166.448 14.7804C166.438 17.532 167.347 20.2081 169.03 22.3851C170.713 24.5621 173.073 26.1156 175.739 26.7996L175.127 29.1805Z" fill="#32ADBA"/>
            <Path d="M182.498 29.1805C185.693 28.3604 188.524 26.4981 190.541 23.8884C192.558 21.2786 193.647 18.0705 193.636 14.772L191.178 14.7804C191.188 17.532 190.279 20.2081 188.596 22.3851C186.913 24.5621 184.553 26.1156 181.887 26.7996L182.498 29.1805Z" fill="#32ADBA"/>
            <Path d="M178.813 22.2344V48.5364M178.813 48.5364H174.162M178.813 48.5364H183.754M168.931 49.4098L178.813 59.2918L189.931 49.4098" stroke="#32ADBA" stroke-width="50" stroke-linecap="round"/>
            <Path d="M133.11 21.0024L129.404 14.8262" stroke="#F04538" stroke-linecap="round"/>
            <Path d="M141.139 21.1236L144.635 14.8262" stroke="#F04538" stroke-linecap="round"/>
            <Path d="M136.816 14.8262V21.0024V24.7081" stroke="#F04538" stroke-linecap="round"/>
            <Path d="M141.757 29.649C141.757 32.3778 139.545 34.59 136.816 34.59C134.087 34.59 131.875 32.3778 131.875 29.649C131.875 26.9202 134.087 24.708 136.816 24.708C139.545 24.708 141.757 26.9202 141.757 29.649ZM133.77 29.649C133.77 31.331 135.134 32.6945 136.816 32.6945C138.498 32.6945 139.862 31.331 139.862 29.649C139.862 27.967 138.498 26.6035 136.816 26.6035C135.134 26.6035 133.77 27.967 133.77 29.649Z" fill="#F04538"/>
            <Path d="M125.068 28.2964C125.89 25.7689 127.505 23.5738 129.674 22.0372C131.842 20.5006 134.449 19.7045 137.106 19.7669C139.763 19.8294 142.329 20.7471 144.423 22.3839C146.517 24.0207 148.027 26.2893 148.729 28.8526L146.372 29.4982C145.809 27.442 144.597 25.6222 142.918 24.3093C141.238 22.9963 139.18 22.2601 137.048 22.21C134.917 22.1599 132.826 22.7985 131.087 24.0311C129.347 25.2638 128.051 27.0246 127.392 29.0521L125.068 28.2964Z" fill="#F04538"/>
            <Path d="M121.992 51.8838H128.168C128.992 54.3543 131.874 59.2953 136.815 59.2953M136.815 59.2953C141.756 59.2953 144.638 54.3543 145.462 51.8838H151.638M136.815 59.2953V63.001" stroke="#F04538" stroke-width="2.5" stroke-linecap="round"/>
            <Path d="M79.9937 61.7626L94.7253 16.3401C94.7548 16.2491 94.883 16.2475 94.9148 16.3378L110.875 61.7626M120.757 61.7626V15.2761C120.757 15.169 120.902 15.1368 120.947 15.234L136.728 49.222C136.763 49.2979 136.87 49.2995 136.908 49.2246L153.919 15.2021C153.966 15.1077 154.108 15.1413 154.108 15.2468V61.7626M164.216 61.7626L171.515 39.2584L178.813 16.7542C178.886 16.5293 179.203 16.5255 179.281 16.7485L195.097 61.7626" stroke="#303030" stroke-width="50" stroke-linecap="round"/>
            <Path d="M218.341 37V25.9436M232.5 36.5L218.521 17.5417C218.464 17.464 218.341 17.5045 218.341 17.601V25.9436M218.341 25.9436L224.517 34.5903" stroke="#F5B011" stroke-width="50" stroke-linecap="round"/>
            <Path d="M218.341 37.5V49.4074M205 41L218.16 59.0415C218.217 59.1197 218.341 59.0793 218.341 58.9826V49.4074M218.341 49.4074L213.4 43.2311" stroke="#F5B011" stroke-width="50" stroke-linecap="round"/>
            <Path d="M204.753 61.7626V15.1816C204.753 15.0804 204.887 15.0432 204.939 15.1298L232.979 61.4561C233.031 61.5427 233.164 61.5055 233.164 61.4043V14.8232" stroke="#303030" stroke-width="5.5" stroke-linecap="round"/>
        </Icon >
    )
}
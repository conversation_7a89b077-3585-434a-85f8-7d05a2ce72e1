import React from "react";
import { ImageBackground, StyleSheet, Text, TouchableOpacity, View, Image } from "react-native";

import { getImageURL, post } from "../networking/Server";
import Layout from "../constants/Layout";
import { black_t2, black_t3, blue_t1, gray_t1, red_t1, white } from "../constants/Color";
import { Spinner } from "native-base";


export type HollySnapModal = {
    array: any,
    close: any
}

const HollySnapModal: React.FC<HollySnapModal> = ({
    array,
    close
}) => {

    const [index, setIndex] = React.useState(0);

    const setSeen = () => {
        post("chat/seen-post", {
            id: array[index]?.id
        }).then((res)=>{
           
        })
    }

    React.useEffect(() => {
        setSeen();
    }, [index]);

    const renderBlurOverlay = (isFriend: boolean) => {
        if (!isFriend) {
            return (
                <View style={styles.blurOverlay}>
                    <Text style={styles.addFriendText}>Bu Snapi Görebilmek İçin Arkadaş Olarak Ekle</Text>
                </View>
            );
        }
        return null;
    };


    return (
        <ImageBackground
            style={styles.main}
            resizeMode="cover"
            source={{ uri: getImageURL(array[index].image) }}
        >
            
            <View style={styles.topbbar}>
                <TouchableOpacity
                    onPress={() => {
                        close();
                    }}
                    style={styles.closeButtonTouch}>
                    <Text style={styles.closeIcon}>×</Text>
                </TouchableOpacity>
            </View>
            {/* LOADING */}
            <View style={styles.loadingView}>
                <Spinner
                    size={22}
                    color={white}
                />
            </View>

            {/* CLOSE BUTTON */}
            

            {/* HOLLY SNAPS INDICATOR */}
            <View style={styles.view}>
                {
                    array?.map((_: any, index_: React.Key | null | undefined) => {
                        return (
                            <View
                                style={
                                    [
                                        {
                                            backgroundColor: index == index_ ? white : gray_t1,
                                            opacity: index == index_ ? 1 : 0.5,

                                        },
                                        styles.altView
                                    ]
                                }
                                key={index_}
                            >
                            </View>
                        )
                    })
                }
            </View>

            {/* NEXT - PREVIEW BUTTONS */}
            <View style={styles.leftRightView}>

                {/* PREVIEW BUTTON */}
                <TouchableOpacity
                    onPress={() => {
                        if (index != 0) {
                            setIndex(index - 1)
                        }
                    }}
                    style={styles.leftTouch}
                />

                {/* NEXT BUTTON */}
                <TouchableOpacity
                    onPress={() => {
                        if (array.length - 1 != index) {
                            setIndex(index + 1)
                        }
                    }}
                    style={styles.rightTouch}
                />
            </View>
            {renderBlurOverlay(array[index]?.isFriend)}
        </ImageBackground>
    )
}
export default HollySnapModal;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        backgroundColor: blue_t1,
        flex: 1,
    },
    loadingView: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        zIndex: -1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    closeButtonTouch: {
        width: 50,
        height: 50,

    },
    topbbar: {
        width: '100%',
        height: 50,
        alignContent: 'flex-end',
        justifyContent: 'flex-end',
        flexDirection: 'row',
        zIndex: 10
    },
    closeIcon: {
        fontSize: 50,
        color: black_t3,

    },
    view: {
        position: 'absolute',
        left: 10,
        top: 10,
        marginTop: 70,
        zIndex: 2
    },
    altView: {
        height: 20,
        width: 20,
        borderRadius: 15,
        marginTop: 10
    },
    leftRightView: {
        height: Layout.screen.height,
        width: Layout.screen.width,
        flexDirection: 'row',
        position: 'absolute',
        zIndex: 3
    },
    leftTouch: {
        height: Layout.screen.height,
        width: Layout.screen.width / 2
    },
    rightTouch: {
        height: Layout.screen.height,
        width: Layout.screen.width / 2
    },

    blurOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 1)',
    },
    addFriendText: {
        color: white,
        fontSize: 18,
        fontWeight: 'bold',
        fontFamily: 'MADE TOMMY',
        textAlign: 'center',
        width:'80%',
    },
});

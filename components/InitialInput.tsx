import React from "react";
import { Image, StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { Input } from "native-base";
import { red_t1, white } from "../constants/Color";
import Layout from "../constants/Layout";

export type InitialInputProps = {
    image: any,
    placeHolder: string, 
    onChangeText_: (text: string) => void,
    keyboardType?: string,
    secureTextEntry?: boolean,
    setModal?: () => void,
    countryCode?: string,
    value?: string,
    isPhone?: boolean,
    isDateField?: boolean,
    required?: boolean,

};

const InitialInput: React.FC<InitialInputProps> = ({
    image,
    placeHolder,
    onChangeText_,
    keyboardType = "default",
    secureTextEntry = false,
    setModal = () => { },
    countryCode,
    value = "",
    isPhone = false,
    isDateField = false,
    required = false,
}) => {

    // Telefon numarasını 10 haneyle sınırla
    const handlePhoneChange = (text: string) => {
        let cleanedText = text.replace(/\D/g, ''); // Sadece rakamları al
        if (cleanedText.length > 10) cleanedText = cleanedText.slice(0, 10);
        onChangeText_(cleanedText);
    };

    const handleDateChange = (text: string) => {
        let cleanedText = text.replace(/\D/g, ''); // Sadece rakamları al
        let formattedText = "";

        if (cleanedText.length > 0) formattedText = cleanedText.slice(0, 2); // Gün
        if (cleanedText.length > 2) formattedText += "." + cleanedText.slice(2, 4); // Ay
        if (cleanedText.length > 4) formattedText += "." + cleanedText.slice(4, 8); // Yıl

        if (formattedText.length > 10) formattedText = formattedText.slice(0, 10); // Max 10 karakter

        console.log("Formatted Date:", formattedText);
        onChangeText_(formattedText);
    };

    return (
        <View style={styles.main}>
            <View style={styles.leftArea}>
                <Image source={image} style={styles.image} resizeMode="contain" />
            </View>
            <View style={styles.rightArea}>
                {keyboardType === "phone-pad" && countryCode && (
                    <TouchableOpacity onPress={() => setModal()}>
                        <Text style={styles.countryCodeText}>+{countryCode}</Text>
                    </TouchableOpacity>
                )}
                <Input
                    alignSelf={'center'}
                    secureTextEntry={secureTextEntry}
                    opacity={1}
                    ml={keyboardType === "phone-pad" ? -2 : 3}
                    backgroundColor={'transparent'}
                    onChangeText={(text) => {
                        if (isPhone) {
                            handlePhoneChange(text);
                        } else if (isDateField) {
                            handleDateChange(text);
                        } else {
                            onChangeText_(text);
                        }
                    }}
                    value={value}
                    fontSize={15}
                    keyboardType={keyboardType === "phone-pad" ? "numeric" : "default"}
                    color={white}
                    placeholder={placeHolder}   // ✅ Yıldız ekleniyor
                    placeholderTextColor={white}
                    fontWeight={'bold'}
                    borderWidth={0}
                    autoCorrect={false}
                    spellCheck={false}
        autoComplete="off"
        textContentType="none"
                />
                {required && (
                    <Text style={styles.requiredMark}>*</Text>
                )}
            </View>
        </View>
    );
};

export default InitialInput;

const styles = StyleSheet.create({
    main: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    leftArea: {
        backgroundColor: white,
        height: 60,
        width: 60,
        borderRadius: 30,
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center'
    },
    requiredMark: {
        position: 'absolute',
        right: 20,
        top: -20,
        color: white,
        fontSize: 18,
        fontWeight: 'bold'
    },
    image: {
        height: 25,
        width: 25
    },
    rightArea: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255,255,255,0.2)',
        width: Layout.screen.width / 1.6,
        height: 45,
        right: 15,
        zIndex: 1,
        borderTopRightRadius: 20,
        borderBottomRightRadius: 20,
    },
    countryCodeText: {
        marginLeft: 20,
        fontWeight: 'bold',
        color: white
    }
});

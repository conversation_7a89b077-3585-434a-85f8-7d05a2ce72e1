import React from 'react';
import { Modal, Text, TouchableOpacity, View, StyleSheet } from 'react-native';

const GiftCancelModal = ({ visible, onClose, onCancelGift }) => {
    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.centeredView}>
                <View style={styles.modalView}>
                    <Text style={styles.modalText}>Hediyeyi iptal etmek istediğinizden emin misiniz?</Text>
                    <TouchableOpacity style={styles.cancelButton} onPress={onCancelGift}>
                        <Text style={styles.buttonText}>Hediyeyi iptal et</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                        <Text style={styles.buttonText}>Kapat</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    centeredView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalView: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 20,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5
    },
    modalText: {
        marginBottom: 15,
        textAlign: 'center',
        fontSize: 16,
    },
    cancelButton: {
        backgroundColor: 'red',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: '100%',
        marginBottom: 10,
    },
    closeButton: {
        backgroundColor: 'gray',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: '100%',
    },
    buttonText: {
        color: 'white',
        fontWeight: 'bold',
        textAlign: 'center',
    },
});

export default GiftCancelModal;
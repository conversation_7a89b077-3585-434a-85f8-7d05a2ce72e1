import React from "react";
import { Animated, Image, StyleSheet, Text, View } from "react-native";
import { green_t1, red_t1 } from "../constants/Color";
import Layout from "../constants/Layout";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export type Toast = {
    type: string,
    subtitle: string,
    status: boolean,
    successColor: string
}

const Toast: React.FC<Toast> = ({ type, subtitle, status, successColor }) => {
    const slideAnim = React.useRef(new Animated.Value(-250)).current;

    const animateToast = () => {
        if (status == null)
            return;

        Animated.timing(slideAnim, {
            toValue: 10,
            duration: 400,
            useNativeDriver: true,
        }).start();

        setTimeout(() => {
            Animated.timing(slideAnim, {
                toValue: -250,
                duration: 200,
                useNativeDriver: true,
            }).start();
        }, 2500);
    };

    React.useEffect(() => {
        animateToast();
    }, [status == true]);

    const insets = useSafeAreaInsets();

    return (
        <Animated.View
            style={{ transform: [{ translateY: slideAnim }], position: 'absolute', zIndex: 4, alignSelf: 'center', marginTop: Math.max(insets.bottom, 0) ? 50 : 0 }}>
            <View style={[styles.toastBox]}>
                {
                    type === 'success' ?
                        <View style={styles.altView}>
                            <View style={[styles.leftView, { backgroundColor: successColor }]}>
                                <Image
                                    style={styles.leftLogo}
                                    resizeMode='contain'
                                    source={require('../assets/root/hollystone_vertical.png')}
                                />
                            </View>
                        </View>
                        :
                        <View style={styles.altView}>
                            <View style={[styles.leftView, { backgroundColor: red_t1 }]}>
                                <Image
                                    style={styles.leftLogo}
                                    resizeMode='contain'
                                    source={require('../assets/root/hollystone_vertical.png')}
                                />
                            </View>
                        </View>
                }
                <View>
                    <Text style={styles.toatMsg}>{subtitle}</Text>
                </View>
            </View>
        </Animated.View>
    )
}
export default Toast;

const styles = StyleSheet.create({
    toastBox: {
        width: Layout.screen.width / 1.1,
        height: 80,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        backgroundColor: '#F9FCFA',
        borderRadius: 8,
        elevation: 1,
        marginHorizontal: 10,
    },
    altView: { flexDirection: 'row' },
    leftView: {
        height: 80,
        padding: 10,
        borderTopLeftRadius: 8,
        borderBottomLeftRadius: 8
    },
    leftLogo: {
        height: 60,
        width: 20
    },
    uiLine: {
        width: 4,
        height: '100%',
        borderRadius: 3,
    },
    toatTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#000',
    },
    toatMsg: {
        fontSize: 13,
        fontWeight: 'bold',
        color: '#010101',
        width: Layout.screen.width / 1.35,
        marginTop: 3,
        marginLeft: 15
    },
})
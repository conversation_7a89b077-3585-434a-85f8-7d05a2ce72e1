import React, { useState, useEffect, useRef, useCallback } from "react";
import { Image, ImageBackground, StyleSheet, TouchableOpacity, View, Text, Modal, Alert, Linking, Platform } from "react-native";
import Layout from "../constants/Layout";
import { <PERSON><PERSON><PERSON>ie<PERSON> } from "moti";
import { shadow } from "../constants/Shadow";
import { blue_t1, brown_t1, gray_t1, green_t1, pink, red_t1, white, yellow_t1, yellow_t2 } from "../constants/Color";
import { Home, Messages, MessagesIcon, Notifications, Profile } from "./Svgs";
import { Camera, useCameraDevice, useCodeScanner } from 'react-native-vision-camera';
import { runOnJS } from 'react-native-reanimated';
import { get, post } from "../networking/Server";
import { SafeAreaView } from "react-native-safe-area-context";
import { HollyChatStoreInstance } from "../stores/HollyChatStore";
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

export type BottomBar = {
    type: number,
    navigation: any
}

const BottomBar: React.FC<BottomBar> = ({
    type,
    navigation,
}) => {
    const [warn, setWarn] = useState(false);
    const [showQRScanner, setShowQRScanner] = useState(false);
    const [showOptionsModal, setShowOptionsModal] = useState(false);
    const [hasPermission, setHasPermission] = useState(false);
    const [isActive, setIsActive] = useState(false);
    const devices = useCameraDevice('back')
    const camera = useRef<Camera>(null);
    
    // Tema rengini belirle
    const themeColor = type == 1 ? green_t1 : 
                      type == 2 ? blue_t1 : 
                      type == 3 ? yellow_t1 : 
                      type == 4 ? brown_t1 : 
                      type == 5 ? gray_t1 : pink;
    
    const totalUnreadMessages = HollyChatStoreInstance.getTotalUnreadMessageCount();

    // Show alert instead of toast
    const showAlert = (message: string, type: string) => {
        Alert.alert(
            type === "success" ? "Başarılı" : "Hata",
            message,
            [{ text: "Tamam", style: "default" }],
            { cancelable: true }
        );
    }

    useEffect(() => {
        setTimeout(() => {
            if (warn) {
                setWarn(false)
            }
        }, 1500);
    }, [warn]);

    useEffect(() => {
        // İzinleri kontrol et
        checkCameraPermission();
    }, []);

    useEffect(() => {
        // QR Scanner modal açıldığında kamerayı aktif et
        if (showQRScanner) {
            setIsActive(true);
        } else {
            setIsActive(false);
        }
    }, [showQRScanner]);

    const checkCameraPermission = async () => {
        try {
            console.log('Kamera izni kontrolü başlatılıyor...');
            
            // Platform'a göre doğru izni seç
            const cameraPermission = Platform.select({
                ios: PERMISSIONS.IOS.CAMERA,
                android: PERMISSIONS.ANDROID.CAMERA,
                default: PERMISSIONS.ANDROID.CAMERA,
            });
            
            // İzin durumunu kontrol et
            const result = await check(cameraPermission);
            console.log('Kamera izni durumu:', result);
            
            switch (result) {
                case RESULTS.GRANTED:
                    console.log('Kamera izni verilmiş');
                    setHasPermission(true);
                    break;
                    
                case RESULTS.DENIED:
                    console.log('Kamera izni reddedilmiş, yeniden isteniyor...');
                    const requestResult = await request(cameraPermission);
                    console.log('İzin isteme sonucu:', requestResult);
                    
                    if (requestResult === RESULTS.GRANTED) {
                        setHasPermission(true);
                    } else {
                        setHasPermission(false);
                        showCameraPermissionAlert();
                    }
                    break;
                    
                case RESULTS.BLOCKED:
                case RESULTS.UNAVAILABLE:
                    console.log('Kamera izni engellendi veya kullanılamıyor');
                    setHasPermission(false);
                    showCameraPermissionAlert();
                    break;
                    
                default:
                    setHasPermission(false);
                    break;
            }
        } catch (error) {
            console.error('İzin kontrol hatası:', error);
            showAlert('Kamera izni kontrolü sırasında bir hata oluştu', 'error');
        }
    };
    
    const showCameraPermissionAlert = () => {
        Alert.alert(
            'Kamera Erişimi Gerekli',
            'QR kodu tarayabilmek için kamera erişimine izin vermelisiniz.',
            [
                {
                    text: 'Ayarlara Git',
                    onPress: () => Linking.openSettings(),
                },
                {
                    text: 'İptal',
                    style: 'cancel',
                },
            ]
        );
    };

    const openQRScanner = () => {
        if (hasPermission) {
            setShowQRScanner(true);
        } else {
            checkCameraPermission();
        }
    };

    // Seçenek modalını aç
    const openOptionsModal = () => {
        setShowOptionsModal(true);
    };

    // Seçenek seçildiğinde
    const handleOptionSelection = (option: string) => {
        setShowOptionsModal(false);
        
        switch(option) {
            case 'payment':
                navigation.navigate("Wallet", { screen: "PayMoney" });
                break;
            case 'menu':
                navigation.navigate("TheBar");
                break;
            case 'points':
                openQRScanner();
                break;
            default:
                break;
        }
    };

    const handleBarCodeScanned = useCallback((codes: any) => {
        if (codes && codes.length > 0 && codes[0].value) {
            const scannedData = codes[0].value;
            
            // Kamerayı duraklat ve QR Scanner'ı kapat
            setIsActive(false);
            setShowQRScanner(false);
            
            // Taranan QR kodu verilerini sunucuya gönder
            post('qr/qr-scanner', { qrData: scannedData }).then((res: any) => {
                console.log(res);

                if (res.success === true) {
                    // Success response
                    showAlert(res.message, "success");
                    navigation.navigate("Root", { refresh: true });
                } else if (res.success === false) {
                    // Error response
                    showAlert(res.message, "error");
                } else {
                    // Fallback for unexpected response format
                    console.error('Unexpected response format:', res);
                    showAlert("Beklenmeyen bir hata oluştu", "error");
                }
            }).catch((error) => {
                console.error('QR scanner error:', error);
                showAlert("Bağlantı hatası", "error");
            });
        }
    }, []);

    const codeScanner = useCodeScanner({
        codeTypes: ['qr'],
        onCodeScanned: (codes) => {
            if (codes && codes.length > 0) {
                handleBarCodeScanned(codes);
            }
        }
    });

    return (
        <ImageBackground
            style={styles.main}
            source={
                type == 1 ?
                    require('../assets/bottomBar/greenBack.png')
                    :
                    type == 2 ?
                        require('../assets/bottomBar/blueBack.png')
                        :
                        type == 3 ?
                            require('../assets/bottomBar/yellowBack.png')
                            :
                            type == 4 ?
                                require('../assets/bottomBar/brownBack.png')
                                :
                                type == 5 ?
                                require('../assets/bottomBar/grayBack.png')
                                :
                                require('../assets/bottomBar/pinkBack.png')
            }
        >
            <MotiView
                from={{
                    height: 0
                }}
                animate={{
                    height: warn ? 175 : 0,
                    zIndex: warn ? 10 : -10,
                    bottom: warn ? -50 : -100
                }}
                style={{
                    width: Layout.screen.width,
                    alignItems: 'center',
                    alignSelf: 'center',
                    position: 'absolute'
                }}
            >
                <View style={[{
                    padding: 10,
                    borderRadius: 10,
                    backgroundColor: white
                }, shadow]}>
                    <Text style={{
                        fontFamily: 'MADE TOMMY',
                        fontSize: 12,
                        color: themeColor
                    }}>Çok Yakında Holly Stone' da!</Text>
                </View>
            </MotiView>

            {/* YENİ: Seçenekler Modalı */}
            <Modal
                visible={showOptionsModal}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowOptionsModal(false)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.optionsContainer}>
                        <Text style={styles.optionsTitle}>Seçenek Seçiniz</Text>
                        
                        <TouchableOpacity 
                            style={[styles.optionButton, {backgroundColor: pink}]} 
                            onPress={() => handleOptionSelection('payment')}
                        >
                            <View style={styles.optionButtonContent}>
                                <Image
                                    resizeMode="contain"
                                    style={styles.cuzdanlogo}
                                    source={require('../assets/root/cuzdanwhite.png')}
                                />
                                <Text style={[styles.optionButtonText, {color: 'white', marginTop: 10}]}>Hesabınızı cüzdan ile ödemek için Qr kodu okutunuz.</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                            style={[styles.optionButton, {backgroundColor: '#e0d8cd'}]} 
                            onPress={() => handleOptionSelection('menu')}
                        >
                            <View style={styles.optionButtonContent}>
                                <Image
                                    resizeMode="contain"
                                    style={styles.thebarLogo}
                                    source={require('../assets/root/menuyatay1.png')}
                                />
                                <Text style={[styles.optionButtonText, {color: '#333', marginTop: 10}]}>Masadaki Qr kodu okutunuz.</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                            style={[styles.optionButton, { backgroundColor: white, borderWidth: 2, borderColor: green_t1 }]} 
                            onPress={() => handleOptionSelection('points')}
                        >
                            <View style={styles.optionButtonContent}>
                                <Image
                                    source={require('../assets/root/hollyPuanLogo.png')}
                                    style={styles.optionButtonIcon}
                                    resizeMode="contain"
                                />
                                <Text style={[styles.optionButtonText, {color: '#333', marginTop: 10}]}>Hesap ile birlikte gelen Qr kodu okutunuz.</Text>
                            </View>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                            style={styles.cancelButton} 
                            onPress={() => setShowOptionsModal(false)}
                        >
                            <Text style={styles.cancelButtonText}>İptal</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {/* QR Scanner Modal */}
            <Modal
                visible={showQRScanner}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowQRScanner(false)}
            >
                <View style={styles.modalContainer}>
                    <SafeAreaView style={styles.qrContainer}>
                        <View style={styles.headerContainer}>
                            <Text style={styles.centerText}>QR kodunu okutunuz.</Text>
                        </View>
                        
                        {devices && hasPermission ? (
                          <Camera
                            ref={camera}
                            style={styles.cameraStyle}
                            device={devices}
                            isActive={true} // Her zaman true olarak ayarlayın
                            codeScanner={codeScanner}
                            enableZoomGesture={false}
                            photo={false}
                            video={false}
                            audio={false}
                          />
                        ) : (
                          <View style={[styles.cameraStyle, {backgroundColor: 'gray', justifyContent: 'center', alignItems: 'center'}]}>
                            <Text style={{color: 'white'}}>Kamera kullanılamıyor</Text>
                          </View>
                        )}
                        
                        <View style={styles.scannerOverlay}>
                            <View style={styles.scannerFrame} />
                        </View>

                        <TouchableOpacity 
                            style={styles.buttonTouchable} 
                            onPress={() => setShowQRScanner(false)}
                        >
                            <Text style={styles.buttonText}>İptal</Text>
                        </TouchableOpacity>
                    </SafeAreaView>
                </View>
            </Modal>

            <TouchableOpacity
                onPress={() => {
                    navigation.navigate("Root");
                }}
                style={{ paddingTop: 10 }}
            >
                <Home size={27} color={white} />
            </TouchableOpacity>

            <TouchableOpacity
                onPress={() => {
                    navigation.navigate("HollyChat");
                }}
                style={styles.chatIconContainer}
            >
                <MessagesIcon size={27} color={themeColor} />
                {totalUnreadMessages > 0 && (
                    <View style={styles.unreadBadge}>
                        <Text style={styles.unreadBadgeText}>{totalUnreadMessages}</Text>
                    </View>
                )}
            </TouchableOpacity>

            <TouchableOpacity
                onPress={openOptionsModal} // QR modalı yerine seçenek modalını açıyoruz
                style={styles.qrTouch}
            >
                <Image
                    source={require('../assets/bottomBar/scan_qr_code_.png')}
                    style={styles.qrImage}
                />
            </TouchableOpacity>

            <TouchableOpacity
                onPress={() => {
                    navigation.navigate("Notifications");
                }}
            >
                <Notifications
                    size={28}
                    color={themeColor}
                />
            </TouchableOpacity>

            <TouchableOpacity
                onPress={() => {
                    navigation.navigate("Profile");
                }}
                style={{
                    paddingTop: 10
                }}
            >
                <Profile
                    size={25}
                    color={white}
                />
            </TouchableOpacity>
        </ImageBackground>
    )
}

export default BottomBar;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        position: 'absolute',
        bottom: 0,
        left: -1,
        zIndex: 100,
        width: Layout.screen.width * 1.01,
        height: Layout.screen.width / 5.06,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20
    },
    messageIcon: {
        width: 25.93,
        height: 26
    },
    qrImage: {
        top: 4,
        width: 40,
        height: 40,
        right: 1
    },
    qrTouch: {
        top: -10,
        width: 40,
        height: 40,
    },
    notifyIcon: {
        width: 21.08,
        height: 26
    },
    profileIcon: {
        marginTop: 10,
        width: 21,
        height: 25.18
    },
    centerText: {
        fontSize: 18,
        color: '#777',
        textAlign: 'center',
        padding: 16,
    },
    buttonText: {
        fontSize: 21,
        color: 'rgb(0,122,255)',
    },
    buttonTouchable: {
        padding: 16,
        alignSelf: 'center',
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    qrContainer: {
        width: '90%',
        height: '70%',
        backgroundColor: 'white',
        borderRadius: 10,
        overflow: 'hidden',
    },
    headerContainer: {
        backgroundColor: 'white',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
    },
    cameraStyle: {
        width: '100%',
        height: '80%',
    },
    scannerOverlay: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scannerFrame: {
        width: 200,
        height: 200,
        borderWidth: 2,
        borderColor: '#FFF',
        backgroundColor: 'transparent',
    },
    chatIconContainer: {
        position: 'relative',
    },
    unreadBadge: {
        position: 'absolute',
        right: -10,
        top: -10,
        backgroundColor: 'red',
        borderRadius: 12,
        width: 24,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
    },
    unreadBadgeText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 12,
    },
    // Seçenekler modalı için yeni stiller
    optionsContainer: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        alignItems: 'center',
    },
    optionsTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#333',
    },
    optionButton: {
        width: '100%',
        padding: 15,
        borderRadius: 8,
        marginBottom: 10,
        alignItems: 'center',
    },
    optionButtonContent: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    optionButtonText: {
        fontWeight: 'bold',
        fontSize: 12,
        textAlign: 'center'
    },
    cancelButton: {
        width: '100%',
        padding: 15,
        borderRadius: 8,
        marginTop: 10,
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
    },
    cancelButtonText: {
        color: '#333',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cuzdanlogo: {
        height: 23,
    },
    thebarLogo: {
        height: 23,
    },
    buttonContentRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    optionButtonIcon: {
        width: 120,
        height: 24,
    }
});

import React, { useRef } from 'react';
import {
  Animated,
  ImageBackground,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import Layout from '../constants/Layout';


interface Props {
  items: string[];
  onIndexChange: (index: number) => void;
  itemHeight: number;
}

const WheelPicker: React.FC<Props> = props => {

  const { items, onIndexChange, itemHeight } = props;
  const modifiedItems: any = ['', ...items, ''];

  const [selected, setSelected] = React.useState("Antalya");

  const scrollY = useRef(new Animated.Value(0)).current;

  const renderItem = ({ item, index }: any) => {
    const inputRange = [
      (index - 2) * itemHeight,
      (index - 1) * itemHeight,
      index * itemHeight,
      (index + 1) * itemHeight,
      (index + 2) * itemHeight
    ];
  
    // Giriş ve çıkış animasyonlarını ayarlayın
    const scale = scrollY.interpolate({
      inputRange,
      outputRange: [0.8, 1, 0.8, 1, 0.8]
    });
  
    const translateX = scrollY.interpolate({
      inputRange,
      outputRange: [-150, -65, -150, -160, -160]
    });
  
    // Opacity animasyonunu ayarlayın
    const opacity = scrollY.interpolate({
      inputRange,
      outputRange: [0.6, 1, 0.6, 1, 0.6]
    });
  
    if (index === 0 || index === modifiedItems.length - 1) {
      return (
        <View style={{ height: itemHeight }} />
      );
    }
  
    return (
      <Animated.View
        style={[
          {
            height: itemHeight,
            transform: [{ scale }, { translateX }],
            opacity // Opacity değerini ekleyin
          },
          styles.animatedContainer
        ]}
      >
        <View
          style={{
            height: Layout.screen.height > 880 ? 50 : Layout.screen.height > 600 ? 60 : 36,
            alignItems: 'center',
            justifyContent: 'center',
            width: Layout.screen.height > 880 ? 272 : Layout.screen.height > 900 ? 245 : 218
          }}
        >
          <Text style={
            [
              styles.pickerItem,
              {
                fontSize: Layout.screen.height > 880 ? 38 : Layout.screen.height > 900 ? 24 : 36
              }
            ]
          }
          >{item.name}</Text>
        </View>
      </Animated.View>
    );
  };
  
  

  const momentumScrollEnd = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const y = event.nativeEvent.contentOffset.y;
    const index = Math.round(y / itemHeight);
    onIndexChange(index);
    setSelected(modifiedItems[index + 1]?.name);
  };

  return (
    <View style={{ height: itemHeight * 3, width: '100%' }}>
      <Animated.FlatList
        data={modifiedItems}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        snapToInterval={itemHeight}
        onMomentumScrollEnd={momentumScrollEnd}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true },
        )}
        getItemLayout={(_, index) => ({
          length: itemHeight,
          offset: itemHeight * index,
          index,
        })}
      />
      <View style={[styles.indicatorHolder, { top: itemHeight }]}>
        {/* <View style={[styles.indicator]} /> */}
        <View style={[styles.indicator, { marginTop: itemHeight }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pickerItem: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    textAlignVertical: 'center',
    color: 'white',
    fontFamily: 'MADE TOMMY',
    padding: 5,
    borderRadius: 50,
    width: 200
  },
  indicatorHolder: {
    position: 'absolute',
  },
  indicator: {
    width: 120,
    height: 1,
  },
  animatedContainer: {
    justifyContent: 'center',
    alignItems: 'center',

  },
});

export default WheelPicker;
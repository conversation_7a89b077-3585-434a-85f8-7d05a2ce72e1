import { View } from "native-base";
import React from "react";
import { StyleSheet, Text, Image } from "react-native";
import { gray_t4, gray_t7 } from "../constants/Color";
import Layout from "../constants/Layout";
import Switch from "./Switch";
import { shadow } from "../constants/Shadow";
import { MotiView } from "moti";
import dayjs from "dayjs";
import { Calendar, MailIcon, PhoneIcon } from "./Svgs";

export type MenuProfileHeader = {
    nameLastName: string,
    email: string,
    dateOfBirth: string,
    phoneNumber: string,
    city: string,
    setVerify: any
}

const MenuProfileHeader: React.FC<MenuProfileHeader> = ({
    nameLastName,
    email,
    dateOfBirth,
    phoneNumber,
    city,
    setVerify = () => { }
}) => {
    return (
        <MotiView
            from={{
                left: -500
            }}
            animate={{
                left: 0
            }}
            style={{ marginTop: 50 }}
        >
            <View style={[styles.main, shadow]}>
                <View style={styles.topView}>
                    <Text style={styles.nameCityView}>{nameLastName}</Text>
                    <Text style={styles.city}>{city.toUpperCase()}</Text>
                </View>

                {/* MAIL */}
                <View style={styles.infoView}>
                    {/* <Image
                        style={styles.infoIcon}
                        resizeMode="contain"
                        source={require('../assets/menu/mail.png')}
                    /> */}
                    <MailIcon size={4} />
                    <Text style={styles.infoDesc}>{email}</Text>
                </View>

                {/* DATE */}
                <View style={styles.infoView}>
                    {/* <Image
                        style={[styles.infoIcon, { height: 17, width: 16 }]}
                        resizeMode="contain"
                        source={require('../assets/menu/date.png')}
                    /> */}
                    <Calendar size={4} color={'transparent'} />
                    <Text style={styles.infoDesc}>{dayjs(dateOfBirth).format("DD.MM.YYYY")}</Text>
                </View>

                {/* DATE */}
                <View style={styles.infoView}>
                    {/* <Image
                        style={[styles.infoIcon, { height: 16, width: 14 }]}
                        resizeMode="contain"
                        source={require('../assets/menu/phone.png')}
                    /> */}
                    <PhoneIcon size={4} />
                    <Text style={styles.infoDesc}>{phoneNumber}</Text>
                </View>

                {/* SWITCH */}
                <View style={styles.switchArea}>
                    <Text style={styles.approveText}>Kimlik bilgilerimi onaylıyorum.</Text>
                    <Switch setVerify={setVerify} />
                </View>
            </View>
        </MotiView>
    )
}
export default MenuProfileHeader;

// -- STYLES -- //
const styles = StyleSheet.create({
    main: {
        backgroundColor: gray_t7,
        padding: 15,
        borderRadius: 15,
        width: Layout.screen.width / 1.1,
        alignSelf: 'center',
        marginTop: 20
    },
    topView: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    nameCityView: {
        fontWeight: 'bold',
        fontSize: 20,
        color: gray_t4
    },
    city: {
        color: gray_t4,
        fontSize: 18,
        fontWeight: 'bold'
    },
    infoView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 15
    },
    infoIcon: {
        height: 13,
        width: 18
    },
    infoDesc: {
        marginLeft: 3,
        fontSize: 14,
        color: gray_t4
    },
    switchArea: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 15,
        paddingRight: 15
    },
    approveText: {
        color: gray_t4,
        fontSize: 14,
    },
});
import { MainStore } from "../stores/MainStore";
import ax from "axios"
let task: any;

const Axios = ax.create({
    baseURL: getURL(),
});

export async function get(adres: any) {
    Axios.defaults.headers.common['Authorization'] = "Bearer " + MainStore.token;

    return new Promise(function (resolve, reject) {
        setTimeout(function () {
            task = Axios.get(adres);
            resolve(
                task.then(({ data }: any) => {
                    try { data = JSON.parse(data) } catch { }
                    return data;
                }).catch((err: any) => {
                    console.warn(err)
                    return { result: false, error: "No_Connect 1" };
                })  
            )
        }, 1000);
    });

}

export async function post(adres: string, params: any = null, func = () => { }, getTask = false) {

    Axios.defaults.headers.common['Authorization'] = "Bearer " + MainStore.token;

    return new Promise(function (resolve, reject) {
        setTimeout(function () {
            task = Axios.post(adres, params);
            resolve(
                task.then(({ data }: any) => {
                    //IStore.setConnection(0);
                    try { data = JSON.parse(data) } catch { }
                    func()
                    return data;
                }).catch((err: any) => {
                    func()
                    //IStore.setConnection(1);
                    console.warn(err)
                    return { result: false, error: "No_Connect 2" };
                })
            )
        }, 1000);
    });
}


export function getTask() {
    return task;
}

export async function cancelPost(task: any) {
    task.cancel((res: any) => {
        console.warn("iptal")
    })
}

function getURL() {
    return "https://api.hollystone.com.tr/api/";
}

export function getSocketURL() {
    return "https://api.hollystone.com.tr/"
}
export function getImageURL(url: string) {
    return "https://api.hollystone.com.tr/resources/images/" + url;
}
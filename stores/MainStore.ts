import { configure, makeAutoObservable, observable } from "mobx";
import { create, persist } from "mobx-persist";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { get } from "../networking/Server"; // Kullanıcı bilgisini çekecek API fonksiyonu
import tr from '../localization/tr.json';
import en from '../localization/en.json';

configure({ enforceActions: "never" });

class MainStoreC {
    constructor() {
        makeAutoObservable(this);
    }

    // --------- TOKEN --------- //
    @persist token = "";
    setToken(set: string) {
        this.token = set;
    }

    // ------- CITY -------- // 
    @persist("object") city: any = {};
    setCity(set: object) {
        this.city = set;
    }

    // ------- LANGUAGE ------- //
    @persist("object") language: any = tr;
    setLanguage(set: string) {
        this.language = set == "tr" ? tr : en;
    }

    // --------- USER --------- //
    @persist("object") user: any = {
        id: null,
        firstName: '',
        lastName: '',
    };

    setUser(user: any) {
        this.user = user;
    }

    async fetchUserInfo() {
        try {
            const res = await get("users/profile");
            if (res.type === "success") {
                this.user = {
                    id: res.user.id,
                    firstName: res.user.firstName,
                    lastName: res.user.lastName,
                };
            } else {
                console.error("Kullanıcı bilgisi alınamadı:", res.error);
            }
        } catch (error) {
            console.error("Kullanıcı bilgisi çekilirken hata:", error);
        }
    }

    // --------- BASKET --------- // 
    @persist("list") @observable basket: any = [];

    emptyBasket() {
        this.basket = [];
    }

    setBasket(set: any, del: boolean, selected: string) {
        if (del) {
            let array = [...this.basket]; 
            let index = array.indexOf(set);
            if (index !== -1) {
                array.splice(index, 1);
                this.basket = array;
            }
        } else {
            let newSet = set;
            if (newSet.quantity == undefined) newSet.quantity = 1;
            newSet.selected = selected;
            this.basket = [...this.basket, newSet];
        }
    }

    setQuantity(id: number, quantity: number) {
        const result = this.basket.filter((item: any) => item.id == id);
        let newSet = result[0];
        newSet.quantity = quantity;
    }
}

const hydrate = create({ storage: AsyncStorage });

export const MainStore = new MainStoreC();

hydrate("MainStore", MainStore).then(() => {
    MainStore.fetchUserInfo(); // Kullanıcı bilgisi yoksa çek
});

// stores/HollyShopStore.ts
import { makeAutoObservable, runInAction } from "mobx";
import { get } from "../networking/Server";

class HollyShopStore {
  categories: any[] = [];
  products: any[] = [];
  products_: any[] = []; // products_ aynı veriyi tutuyor gibi görünüyor, farklı bir amaç varsa belirtin
  loading: boolean = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  // HollyShop verilerini API'den alma
  async fetchShopData() {
    runInAction(() => {
      this.loading = true;
      this.error = null;
    });

    // Categories çekme
    const categoriesRes: any = await get("shop/categories");
    runInAction(() => {
      if (categoriesRes.type === "success") {
        this.categories = categoriesRes.data;
      } else {
        this.error = categoriesRes.error || "Kategoriler alınamadı";
        console.error("Kategoriler alınamadı:", categoriesRes.error);
      }
    });

    // Products çekme
    const productsRes: any = await get("shop/products");
    runInAction(() => {
      if (productsRes.type === "success") {
        this.products = productsRes.data;
        this.products_ = productsRes.data; // products_ ile products aynı veriyi tutuyor
      } else {
        this.error = productsRes.error || "Ürünler alınamadı";
        console.error("Ürünler alınamadı:", productsRes.error);
      }
      this.loading = false;
    });
  }

  // Store'u sıfırlama (opsiyonel)
  reset() {
    runInAction(() => {
      this.categories = [];
      this.products = [];
      this.products_ = [];
      this.loading = false;
      this.error = null;
    });
  }
}

export const HollyShopStoreInstance = new HollyShopStore();
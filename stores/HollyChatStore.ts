import { makeAutoObservable, runInAction } from "mobx";
import { get, post } from "../networking/Server";
import { socket } from "../networking/Socket";

export interface IConversation {
  id: number | string;
  partner: {
    _id: number | string;
    name: string;
    avatar?: string;
  };
  messages: any[];
  updatedAt?: string;
  isTyping?: boolean;
}

// Kullanıcı objesi örnek arayüz
export interface IUser {
  id: number | string;
  firstName: string;
  lastName: string;
  avatar?: string; // varsa ekleyebilirsiniz
}

class HollyChatStore {
  activeUsers: any[] = [];
  friends: any[] = [];
  conversations: IConversation[] = [];
  requestCount: number = 0;
  senderId: number | null = null;
  loading: boolean = false;
  error: string | null = null;
  initialized: boolean = false;
  onlineUsers: string[] = [];
  typingUsers: Record<string, boolean> = {}; // chatRoomId: isTyping


  // Yeni e<PERSON>nen user alanı
  user: IUser | null = null;

  constructor() {
    makeAutoObservable(this);

    socket.on("connect", () => {
      console.log("Socket connected =>", socket.id);
      if (this.senderId) {
        socket.emit("user connected", this.senderId);
      }
    });

    socket.on("update online users", (onlineUserIds: string[]) => {
      runInAction(() => {
        this.onlineUsers = onlineUserIds;
      });
      console.log("Updated online users =>", onlineUserIds);
    });

    socket.on("user_typing", ({ userId, roomId }) => {
      runInAction(() => {
        // Yalnızca başka bir kullanıcı yazıyorsa işaretle
        if (userId?.toString() !== this.senderId?.toString()) {
          this.typingUsers[roomId] = true;
          
          // Konuşmayı bul ve isTyping değerini güncelle
          const conversationIndex = this.conversations.findIndex(
            (conv) => conv.id.toString() === roomId.toString()
          );
          
          if (conversationIndex !== -1) {
            this.conversations[conversationIndex].isTyping = true;
          }
        }
      });
    });

    socket.on("user_stopped", ({ userId, roomId }) => {
      runInAction(() => {
        if (userId?.toString() !== this.senderId?.toString()) {
          this.typingUsers[roomId] = false;
          
          // Konuşmayı bul ve isTyping değerini güncelle
          const conversationIndex = this.conversations.findIndex(
            (conv) => conv.id.toString() === roomId.toString()
          );
          
          if (conversationIndex !== -1) {
            this.conversations[conversationIndex].isTyping = false;
          }
        }
      });
    });



    socket.on("message received", (payload: any) => {
      runInAction(() => {
        if (!payload.chatRoomId) return;
        const chatIndex = this.conversations.findIndex(
          (c) => c.id.toString() === payload.chatRoomId.toString()
        );
    
        if (chatIndex !== -1) {
          // Yazıyor durumunu sıfırla
          this.conversations[chatIndex].isTyping = false;
          
          // GiftedChat için gerekli durum bayraklarını ayarla
          const messageWithStatus = {
            ...payload,
            // Duruma göre bayrakları ayarla
            sent: payload.messageStatus === 'sent' || 
                  payload.messageStatus === 'delivered' || 
                  payload.messageStatus === 'read',
            received: payload.messageStatus === 'delivered' || 
                      payload.messageStatus === 'read',
            pending: payload.messageStatus === 'pending',
          };
          
          // Mesajı ekle
          this.conversations[chatIndex].messages.push(messageWithStatus);
          
          // Zamana göre sırala
          this.conversations[chatIndex].messages.sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
          
          // Konuşma timestamp'ini güncelle
          this.conversations[chatIndex].updatedAt = new Date().toISOString();
        } else {
          console.log("Conversation not found. Fetching again...");
          this.getConversations();
        }
      });
    });

    socket.on('message_status_update', (payload) => {
      console.log('Mesaj durumu güncelleme alındı:', payload);
      
      runInAction(() => {
        const { messageId, messageIds, status, chatRoomId } = payload;
        
        const conversationIndex = this.conversations.findIndex(
          conv => conv.id?.toString() === chatRoomId?.toString()
        );
        
        if (conversationIndex !== -1) {
          const conversation = this.conversations[conversationIndex];
          
          // Tek bir mesaj ID'si (iletildi durumu için)
          if (messageId) {
            const msgIndex = conversation.messages.findIndex(
              msg => (msg._id?.toString() === messageId?.toString() || msg.id?.toString() === messageId?.toString())
            );
            
            if (msgIndex !== -1) {
              // String durumu güncelle
              conversation.messages[msgIndex].messageStatus = status;
              
              // GiftedChat için boolean bayrakları güncelle
              switch(status) {
                case 'pending':
                  conversation.messages[msgIndex].pending = true;
                  conversation.messages[msgIndex].sent = false;
                  conversation.messages[msgIndex].received = false;
                  break;
                case 'sent':
                  conversation.messages[msgIndex].pending = false;
                  conversation.messages[msgIndex].sent = true;
                  conversation.messages[msgIndex].received = false;
                  break;
                case 'delivered':
                  conversation.messages[msgIndex].pending = false;
                  conversation.messages[msgIndex].sent = true;
                  conversation.messages[msgIndex].received = true;
                  break;
                case 'read':
                  conversation.messages[msgIndex].pending = false;
                  conversation.messages[msgIndex].sent = true;
                  conversation.messages[msgIndex].received = true;
                  break;
              }
              
              console.log(`Mesaj ${messageId} durumu güncellendi: ${status}`);
            }
          }
          
          // Çoklu mesaj ID'leri (okundu durumu için)
          else if (messageIds && messageIds.length > 0) {
            let updateCount = 0;
            
            conversation.messages.forEach(msg => {
              const msgId = msg._id?.toString() || msg.id?.toString();
              if (messageIds.includes(msgId)) {
                // String durumu güncelle
                msg.messageStatus = status;
                
                // GiftedChat için boolean bayrakları güncelle
                if (status === 'read') {
                  msg.pending = false;
                  msg.sent = true;
                  msg.received = true;
                }
                
                updateCount++;
              }
            });
            
            console.log(`Toplam ${updateCount} mesaj 'okundu' olarak güncellendi`);
          }
        }
      });
    });

    socket.on("message deleted", (payload: any) => {
      runInAction(() => {
        
        // chatRoomId varsa ona göre konuşmayı bul
        if (payload.chatRoomId) {
          const convIndex = this.conversations.findIndex(
            (conv) => conv.id?.toString() === payload.chatRoomId?.toString()
          );
    
          if (convIndex !== -1) {
            const conversation = this.conversations[convIndex];
            const msgIndex = conversation.messages.findIndex(
              (msg) => (msg._id?.toString() === payload.messageId?.toString()) || 
                       (msg.id?.toString() === payload.messageId?.toString())
            );
            
            if (msgIndex !== -1) {
              conversation.messages[msgIndex].text = "Mesaj silindi";
              conversation.messages[msgIndex].isDeleted = true;
            } else {
              console.log("Belirtilen ID ile mesaj bulunamadı:", payload.messageId);
            }
          } else {
            console.log("Belirtilen ID ile konuşma bulunamadı:", payload.chatRoomId);
          }
        }
      });
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected");
    });
  }

  // Mevcut: Konuşma bilgisini çekme
  async getConversations() {
    this.loading = true;
    this.error = null;
    try {
      const res: any = await get("chat/conversations");
      runInAction(() => {
        if (res.type === "success") {
          // 1) Gelen verileri JSON biçiminde bas
          console.log("Debug => raw conversations:", JSON.stringify(res.conversations, null, 2));
  
          // 2) _id'yi id'ye kopyalayıp, yeni bir dizi oluştur
          const fixedConversations = res.conversations.map((conv: any) => ({
            ...conv,
            id: conv._id
          }));
  
          // 3) Dönüştürülmüş hali de bas
          console.log("Debug => fixedConversations:", JSON.stringify(fixedConversations, null, 2));
  
          // 4) State'e kaydet
          this.conversations = fixedConversations;
        } else {
          this.error = res.error || "Veriler alınamadı.";
        }
      });
      return res;
    } catch (err: any) {
      runInAction(() => {
        this.error = err.message;
      });
      return { error: err.message };
    } finally {
      runInAction(() => {
        this.loading = false;
      });
    }
  }


  

  // Yeni eklenen: Kullanıcı bilgisi çekme
  async fetchUserInfo() {
    try {
      const res: any = await get("users/profile");
      runInAction(() => {
        if (res.type === "success") {
          // Gelen user objesi store'a kaydedilir
          this.user = {
            id: res.user.id,
            firstName: res.user.firstName,
            lastName: res.user.lastName,
            // avatar: res.user.avatar, // var ise eklenebilir
          };
        } else {
          console.error("Kullanıcı bilgisi alınamadı:", res.error);
        }
      });
    } catch (error: any) {
      console.error("Kullanıcı bilgisi çekilirken hata:", error);
    }
  }

  async getChatInfo() {
    if (!this.initialized) {
      this.loading = true;
    }
    this.error = null;
    try {
      const res: any = await get("chat");
      runInAction(() => {
        if (res.type === "success") {
          this.activeUsers = res.activeUsers;
          this.friends = res.friends;
          this.requestCount = res.requestCount;
          this.senderId = res.userId;
          if (this.senderId) {
            socket.emit("user connected", this.senderId);
          }
          this.initialized = true;
        } else {
          this.error = res.error || "Veriler alınamadı.";
        }
      });
      return res;
    } catch (err: any) {
      runInAction(() => {
        this.error = err.message;
      });
      return { error: err.message };
    } finally {
      runInAction(() => {
        this.loading = false;
      });
    }
  }

  async deleteConversation(partnerId: number) {
    try {
      const res: any = await post("chat/delete-conversation", {
        conversationPartnerId: partnerId,
      });
      if (res.type === "success") {
        await this.getConversations();
        return { success: true, message: res.message };
      } else {
        return { success: false, message: res.error };
      }
    } catch (err: any) {
      return { success: false, message: "Hata oluştu" };
    }
  }

  getUnreadMessageCount(conversationId: string | number): number {
    if (!conversationId) return 0;
    
    // Konuşmayı bul
    const conversation = this.conversations.find(conv => 
      conv.id?.toString() === conversationId.toString()
    );
    
    if (!conversation || !conversation.messages || !conversation.messages.length) {
      return 0;
    }
    
    return conversation.messages.filter(msg => {
      // GiftedChat formatına göre, mesajı gönderen kişi msg.user._id
      // Bu ID ile store'da tutulan mevcut kullanıcı ID'sini karşılaştır
      const messageCreatorId = msg.user?._id?.toString();
      
      // Eğer store.senderId varsa onu kullan, yoksa store.user.id'yi kullan
      const currentUserId = this.senderId?.toString() || this.user?.id?.toString();
      
      // Mesaj mevcut kullanıcıdan mı geldi?
      const isSentByCurrentUser = messageCreatorId === currentUserId;
      
      // Okunmamış mesaj kontrolü
      const isUnread = msg.messageStatus !== 'read';
      
      // Silinmiş mesajları dahil etme
      const isNotDeleted = !msg.isDeleted;

      return !isSentByCurrentUser && isUnread && isNotDeleted;
    }).length;
  }
  
  // Tüm konuşmalardaki toplam okunmamış mesaj sayısını hesapla
  getTotalUnreadMessageCount(): number {
    if (!this.conversations.length) return 0;
    
    let total = 0;
    this.conversations.forEach(conv => {
      total += this.getUnreadMessageCount(conv.id);
    });
    
    return total;
  }

  reset() {
    runInAction(() => {
      this.activeUsers = [];
      this.friends = [];
      this.conversations = [];
      this.requestCount = 0;
      this.senderId = null;
      this.initialized = false;
      this.onlineUsers = [];
      this.user = null;
    });
  }
}

export const HollyChatStoreInstance = new HollyChatStore();

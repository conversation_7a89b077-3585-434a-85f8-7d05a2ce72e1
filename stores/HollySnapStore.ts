import { makeAutoObservable, runInAction } from "mobx";
import { get } from "../networking/Server";
import { MainStore } from "./MainStore";
import { socket } from "../networking/Socket";

class HollySnapStore {
  snaps: any[] = [];
  loading: boolean = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);

    // Socket'i dinle ve tetiklendiğinde verileri güncelle
    socket.on("updateData", () => {
      this.getSnaps();
    });
  }

  // Snap verilerini çekme fonksiyonu
  async getSnaps(navigation?: any) {
    runInAction(() => {
      this.loading = true;
      this.error = null;
    });

    try {
      const res: any = await get("/snaps");
      runInAction(() => {
        if (res.type === 'success') {
          this.snaps = res.snaps;
          this.loading = false;
        } else {
          this.error = res.error || "Snap verileri alınamadı";
          this.loading = false;
          console.error("Snap verileri alınamadı", res.error);
          if (navigation) navigation.pop();
        }
      });
    } catch (error: any) {
      runInAction(() => {
        this.error = error.message || "Beklenmeyen bir hata oluştu";
        this.loading = false;
        console.error("Beklenmeyen bir hata:", error);
        if (navigation) navigation.pop();
      });
    }
  }

  // Store'u sıfırlama (opsiyonel)
  reset() {
    runInAction(() => {
      this.snaps = [];
      this.loading = false;
      this.error = null;
    });
  }
}

export const HollySnapStoreInstance = new HollySnapStore();

// stores/ShamanStore.ts
import { makeAutoObservable, runInAction } from "mobx";
import { get } from "../networking/Server";

class ShamanStore {
  shamanWeekList: any[] = [];
  shamanMonthList: any[] = [];
  shamanAllList: any[] = [];
  lastWinners: any = {};
  loading: boolean = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
  }

  // Shaman verilerini API'den alma
  async fetchShamanData() {
    runInAction(() => {
      this.error = null;
    });

    const res: any = await get("shaman");
    runInAction(() => {
      if (res.type === "success") {
        this.shamanWeekList = res.data.week;
        this.shamanMonthList = res.data.month;
        this.shamanAllList = res.data.all;
      } else {
        this.error = res.error || "Shaman verileri alınamadı";
        console.error("Shaman verileri alınamadı:", res.error);
      }
    });

    // Son kazananları getiren API'yi <PERSON>
    const lastWinnersRes: any = await get("shaman/last-winners");
    runInAction(() => {
      if (lastWinnersRes.type === "success") {
        this.lastWinners = lastWinnersRes.data;
        console.log("Last Winners Data:", this.lastWinners);
      } else {
        this.error = lastWinnersRes.error || "Son kazanan verileri alınamadı";
        console.error("Son kazanan verileri alınamadı:", lastWinnersRes.error);
      }
      this.loading = false;
    });
  }

  reset() {
    runInAction(() => {
      this.shamanWeekList = [];
      this.shamanMonthList = [];
      this.shamanAllList = [];
      this.lastWinners = {};
      this.loading = false;
      this.error = null;
    });
  }
}

export const ShamanStoreInstance = new ShamanStore();

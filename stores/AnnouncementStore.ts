import { makeAutoObservable } from "mobx";
import { post } from "../networking/Server";
import { create, persist } from "mobx-persist";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface AnnouncementResponse {
    type: "success" | "error";
    data: any[];
}

class AnnouncementStoreC {
    @persist("list") announcements: any[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    async fetchAnnouncements() {
        try {
            const res = await post("announcements", {}) as AnnouncementResponse; 

            if (res.type === "success") {
                this.announcements = res.data;
            }
        } catch (error) {
            console.error("<PERSON><PERSON>ru verileri alınırken hata oluştu:", error);
        }
    }
}

const hydrate = create({ storage: AsyncStorage });

export const AnnouncementStore = new AnnouncementStoreC();

hydrate("AnnouncementStore", AnnouncementStore).then(() => {
    AnnouncementStore.fetchAnnouncements();
});

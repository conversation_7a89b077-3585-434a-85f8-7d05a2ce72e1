// stores/HollyPointsStore.ts
import { makeAutoObservable, runInAction } from "mobx";
import { get } from "../networking/Server";

class HollyPointsStore {
  points: any = null; // Holly points verisi (earnings, spendings, hollyPoints)
  hollyPointsValue: number = 0; // Holly points'in TL karşılığı
  contract: string = ""; // Sözleşme metni
  loading: boolean = false; // Yükleme durumu
  error: string | null = null; // Hata mesajı

  constructor() {
    makeAutoObservable(this);
  }

  // Holly points verisini API'den alma
  async fetchHollyPoints() {
    runInAction(() => {
      this.loading = true;
      this.error = null;
    });

    // Holly points verisini al
    const res = await get("holly-points");
    runInAction(() => {
      if (res.type === "success") {
        this.points = res.data;
      } else {
        this.error = res.error || "Holly points alınamadı";
        console.error("Holly points alınamadı:", res.error);
      }
    });

    // Holly points'in TL değerini ve sözleşmeyi al
    const settingsRes = await get("get-settings");
    runInAction(() => {
      if (settingsRes.type === "success") {
        this.hollyPointsValue = settingsRes.hollyPointsValue;
        this.contract = settingsRes.contract;
      } else {
        this.error = settingsRes.error || "Settings alınamadı";
        console.error("Settings alınamadı:", settingsRes.error);
      }
      this.loading = false;
    });
  }

  // Store'u sıfırlamak için metod (opsiyonel)
  reset() {
    runInAction(() => {
      this.points = null;
      this.hollyPointsValue = 0;
      this.contract = "";
      this.loading = false;
      this.error = null;
    });
  }
}

export const HollyPointsStoreInstance = new HollyPointsStore();
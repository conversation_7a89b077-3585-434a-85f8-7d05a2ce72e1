// stores/WheelStore.ts
import { makeAutoObservable, runInAction } from "mobx";
import { get, getImageURL } from "../networking/Server";

// Wheel verileri için tür tanımlamaları
interface Prize {
  name: string;
  image: string;
}

interface WheelResponse {
  type: "success" | "error";
  prizes?: Prize[];
  prize?: { image: string; name: string };
  date?: string;
  error?: string;
}

class WheelStore {
  participants: Prize[] = [];
  lastImage: string = "";
  lastName: string = "";
  daywheel: number = 6;
  hour: number = 24;
  minute: number = 59;
  second: number = 59;
  loading: boolean = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
    // setTimeout kaldırıldı, fetch manuel olarak <PERSON>ğ<PERSON>ı<PERSON>
  }

  // Wheel verisini API'den alma
  async fetchWheelInfo(): Promise<void> {
    runInAction(() => {
      this.loading = true;
      this.error = null;
    });

    const res: WheelResponse = await get("wheel");

    runInAction(() => {
      if (res.type === "success" && res.prizes) {
        this.participants = res.prizes.map((prize) => ({
          name: prize.name,
          image: getImageURL(prize.image),
        }));
      } else {
        if (res.prize) {
          this.lastImage = res.prize.image;
          this.lastName = res.prize.name;
        }
        this.error = res.error || "Çark bilgileri alınamadı";
        console.error("Çark bilgileri alınamadı:", res.error);
      }

      if (res.date) {
        const now = new Date().getTime();
        const wheelTime = new Date(res.date).getTime();
        let fMil = Math.abs(wheelTime - now);

        this.daywheel = Math.floor(fMil / (1000 * 60 * 60 * 24));
        fMil %= 1000 * 60 * 60 * 24;
        this.hour = Math.floor(fMil / (1000 * 60 * 60));
        fMil %= 1000 * 60 * 60;
        this.minute = Math.floor(fMil / (1000 * 60));
        fMil %= 1000 * 60;
        this.second = Math.floor(fMil / 1000);
      }

      this.loading = false;
    });
  }

  // Store'u sıfırlamak için metod (opsiyonel)
  reset(): void {
    runInAction(() => {
      this.participants = [];
      this.lastImage = "";
      this.lastName = "";
      this.daywheel = 6;
      this.hour = 24;
      this.minute = 59;
      this.second = 59;
      this.loading = false;
      this.error = null;
    });
  }
}

export const WheelStoreInstance = new WheelStore();
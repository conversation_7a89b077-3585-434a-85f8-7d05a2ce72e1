/**
 *        YILGIN YAZILIM VE BILGI TEKNOLOJILERI
 *          bilgi mail: <EMAIL> 
 *        destek mail: <EMAIL>
 *            website: www.yilginyaziim.com
 *              instagram: yilgin_yazilim
 *      tel: +90 (************* | +90 (*************
 *      adres: Burah mh. 06044 nolu sk. Fazilet Apt. 
 *                    DKN:1 BBN:3
 *      
 * DESIGNERS : RABIA PARLAK / BUĞRA CAN KUŞ
 * ENGINEERS : MEHMET ALI YILGIN / MURAT CAN AKKOÇ
 *
 * @format
 */
import { LogLevel, OneSignal } from 'react-native-onesignal';
import React from 'react';
import { StatusBar, StyleSheet } from 'react-native';
import Navigation from './navigation';
import { NativeBaseProvider } from 'native-base';
import { UnreadProvider } from './functions/UnreadContext';
import { Observer } from 'mobx-react-lite';




function App(): JSX.Element {
  // Remove this method to stop OneSignal Debugging
  OneSignal.Debug.setLogLevel(LogLevel.Verbose);

  // OneSignal Initialization
  OneSignal.initialize("************************************");

  // requestPermission will show the native iOS or Android notification permission prompt.
  // We recommend removing the following code and instead using an In-App Message to prompt for notification permission
  OneSignal.Notifications.requestPermission(true);

  // Method for listening for notification clicks
  OneSignal.Notifications.addEventListener('click', (event) => {
  });

  

  return (
    <>
     <UnreadProvider>
      <StatusBar hidden={true} />
      <NativeBaseProvider>
        <Navigation />
      </NativeBaseProvider>
     </UnreadProvider>
    </>
  );
}

export default App;

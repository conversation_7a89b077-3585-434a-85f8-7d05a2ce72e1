<svg width="25" height="27" viewBox="0 0 25 27" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_5096_25)">
<path d="M6 4H7.50014V8.52412C7.50014 9.15584 7.28514 9.76874 6.89051 10.262V10.262C5.99048 11.3871 5.50014 12.7849 5.50014 14.2257V14.8114L5.50014 23H4V14.3884V13.6008C4 12.5645 4.35267 11.5592 5 10.75V10.75C5.64733 9.94084 6 8.93545 6 7.89922V4Z" fill="black"/>
</g>
<g filter="url(#filter1_d_5096_25)">
<path d="M12.5 4H14.0001V8.52412C14.0001 9.15584 13.7851 9.76874 13.3905 10.262V10.262C12.4905 11.3871 12.0001 12.7849 12.0001 14.2257V14.8114L12.0001 23H10.5V14.3884V13.6008C10.5 12.5645 10.8527 11.5592 11.5 10.75V10.75C12.1473 9.94084 12.5 8.93545 12.5 7.89922V4Z" fill="black"/>
</g>
<g filter="url(#filter2_d_5096_25)">
<path d="M19 4H20.5001V8.52412C20.5001 9.15584 20.2851 9.76874 19.8905 10.262V10.262C18.9905 11.3871 18.5001 12.7849 18.5001 14.2257V14.8114L18.5001 23H17V14.3884V13.6008C17 12.5645 17.3527 11.5592 18 10.75V10.75C18.6473 9.94084 19 8.93545 19 7.89922V4Z" fill="black"/>
</g>
<defs>
<filter id="filter0_d_5096_25" x="0" y="0" width="11.5" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5096_25"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5096_25" result="shape"/>
</filter>
<filter id="filter1_d_5096_25" x="6.5" y="0" width="11.5" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5096_25"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5096_25" result="shape"/>
</filter>
<filter id="filter2_d_5096_25" x="13" y="0" width="11.5" height="27" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5096_25"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5096_25" result="shape"/>
</filter>
</defs>
</svg>

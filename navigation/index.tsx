import 'react-native-gesture-handler';
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StackNavigationProp, createStackNavigator } from '@react-navigation/stack';

// -- SCREENS -- //
import Initial from '../screens/Initial/Initial';
import Login from '../screens/Initial/Login';
import Register from '../screens/Initial/Register';
import ForgetPassword from '../screens/Initial/ForgetPassword';
import Verify from '../screens/Initial/Verify';
import UpdatePassword from '../screens/Initial/UpdatePassword';
import Root from '../screens/Root/Root';
import HollyPuan from '../screens/Root/HollyPuan/HollyPuan';
import HollyShop from '../screens/Root/HollyShop/HollyShop';
import HollyShopDetail from '../screens/Root/HollyShop/HollyShopDetail';
import Basket from '../screens/Root/HollyShop/Basket';
import Earned from '../screens/Root/Menu/Earned';
import GiftCard from '../screens/Root/Menu/GiftCard';
import Contact from '../screens/Root/Menu/Contact';
import Career from '../screens/Root/Menu/Career';
import ArtistApplication from '../screens/Root/Menu/ArtistApplication';
import Franchise from '../screens/Root/Menu/Franchise';
import Settings from '../screens/Root/Menu/Settings/Settings';
import Concerts from '../screens/Root/Concert/Concerts';
import ConcertDetail from '../screens/Root/Concert/ConcertDetail';
import HollySnap from '../screens/Root/HollySnap/HollySnap';
import EditProfile from '../screens/Root/Menu/Settings/EditProfile';
import Shaman from '../screens/Root/Shaman/Shaman';
import Profile from '../screens/Root/Menu/Profile';
import VerifyRegister from '../screens/Initial/VerifyRegister';
import ProfileUpdatePassword from '../screens/Root/Menu/Settings/ProfileUpdatePassword';
import HollyChat from '../screens/Root/HollyChat/HollyChat';
import CreditCards from '../screens/Root/Menu/Settings/CreditCards';
import AddCreditCard from '../screens/Root/Menu/Settings/AddCreditCard';
import DailyActivity from '../screens/Root/DailyActivity/DailyActivity';
import Wheel from '../screens/Root/Wheel/Wheel';
import ChangeCity from '../screens/Root/Menu/Settings/ChangeCity';
import Addresses from '../screens/Root/Menu/Settings/Addresses';
import DeleteAccount from '../screens/Root/Menu/Settings/DeleteAccount';
import AddAddress from '../screens/Root/Menu/Settings/AddAddress';
import AddressCard from '../screens/Root/HollyShop/AddressCard';
import BasketOkey from '../screens/Root/HollyShop/BasketOkey';
import MyOrders from '../screens/Root/HollyShop/MyOrders';
import OrderDetail from '../screens/Root/HollyShop/OrderDetail';
import Evaluate from '../screens/Root/HollyShop/Evaluate';
import Return from '../screens/Root/HollyShop/Return';
import NewFriend from '../screens/Root/HollyChat/NewFriend';
import Blocked from '../screens/Root/HollyChat/Blocked';
import Chat from '../screens/Root/HollyChat/Chat';
import AllFriends from '../screens/Root/HollyChat/AllFriends';
import DailyActivityDetail from '../screens/Root/DailyActivity/DailyActivityDetail';
import HollySnapDetail from '../screens/Root/HollySnap/HollySnapDetail';
import Notifications from '../screens/Root/Notifications/Notifications';
import ChangeLanguage from '../screens/Root/Menu/Settings/ChangeLanguage';
import ChooseCity from '../screens/Initial/ChooseCity';
import Level from '../screens/Root/Level/Level';
import Wallet from '../screens/Root/Wallet/Wallet';
import PayMoney from '../screens/Root/Wallet/PayMoney';
import PayMoneyWithCard from '../screens/Root/Wallet/PayMoneyWithCard';
import TransactionHistory from '../screens/Root/Wallet/Transaction';
import TheBar from '../screens/Root/TheBar/TheBar';
import HollyGarden from '../screens/Root/HollyGarden/HollyGarden';
import TicketPage from '../screens/Root/Menu/TicketPage';
import CardDetails from '../screens/Root/Wallet/CardDetails';




import SatisSozlesmesi from '../screens/Root/Menu/Settings/SatisSozlesmesi';
import IptalveIade from '../screens/Root/Menu/Settings/IptalveIade';
import GizlilikveGuvenlik from '../screens/Root/Menu/Settings/GizlilikveGuvenlik';

import ChatTwo from '../screens/Root/HollyChat/ChatTwo'






type screens_ = {
    Initial: undefined,
    Login: undefined,
    Register: undefined,
    VerifyRegister: undefined,
    ForgetPassword: undefined,
    ChooseCity: undefined,
    Verify: undefined,
    UpdatePassword: undefined,
    Root: undefined,
    HollyPuan: undefined,
    HollyShop: undefined,
    HollyShopDetail: undefined,
    Basket: undefined,
    Earned: undefined,
    GiftCard: undefined,
    Contact: undefined,
    Career: undefined,
    ArtistApplication: undefined,
    Franchise: undefined,
    Settings: undefined,
    Concerts: undefined,
    ConcertDetail: undefined,
    HollySnap: undefined,
    EditProfile: undefined,
    Shaman: undefined,
    Profile: undefined,
    ProfileUpdatePassword: undefined,
    HollyChat: undefined,
    CreditCards: undefined,
    AddCreditCard: undefined,
    DailyActivity: undefined,
    DailyActivityDetail: undefined,
    Wheel: undefined,
    ChangeCity: undefined,
    Addresses: undefined,
    AddAddress: undefined,
    AddressCard: undefined,
    BasketOkey: undefined,
    MyOrders: undefined,
    OrderDetail: undefined,
    Evaluate: undefined,
    Return: undefined,
    NewFriend: undefined,
    Blocked: undefined,
    Chat: undefined,
    AllFriends: undefined,
    HollySnapDetail: undefined,
    Notifications: undefined,
    ChangeLanguage: undefined,
    Level: undefined,
    Wallet: undefined,
    PayMoney: undefined,
    TransactionHistory: undefined,
    SatisSozlesmesi: undefined,
    IptalveIade: undefined,
    GizlilikveGuvenlik: undefined,
    TheBar: undefined,
    HollyGarden: undefined,
    ChatTwo: undefined,
    PayMoneyWithCard: undefined,
    DeleteAccount: undefined,
    TicketPage: undefined,
    CardDetails: undefined,


}

export type screens = StackNavigationProp<screens_>

const Navigation = () => {

    const Stack = createStackNavigator<screens_>();

    return (
        <NavigationContainer>
            <Stack.Navigator
                initialRouteName="Initial"
                screenOptions={{
                    headerShown: false
                }}
            >
                <Stack.Screen
                    name='Initial'
                    component={Initial}
                />

                <Stack.Screen
                    name='Login'
                    component={Login}
                />

                <Stack.Screen
                    name='Register'
                    component={Register}
                />

                <Stack.Screen
                    name='VerifyRegister'
                    component={VerifyRegister}
                />

                <Stack.Screen
                    name='ChooseCity'
                    component={ChooseCity}
                />


                <Stack.Screen
                    name='ForgetPassword'
                    component={ForgetPassword}
                />

                <Stack.Screen
                    name='Verify'
                    component={Verify}
                />

                <Stack.Screen
                    name='UpdatePassword'
                    component={UpdatePassword}
                />

                <Stack.Screen
                    name='Root'
                    component={Root}
                />

                <Stack.Screen
                    name='HollyPuan'
                    component={HollyPuan}
                    options={{
                        gestureEnabled: false,
                    }}
                />

                <Stack.Screen
                    name='HollyShop'
                    component={HollyShop}
                    
                />

                <Stack.Screen
                    name='HollyShopDetail'
                    component={HollyShopDetail}

                />

                <Stack.Screen
                    name='Basket'
                    component={Basket}
                    options={{
                        gestureEnabled: false,
                    }}
                />

                <Stack.Screen
                    name='Earned'
                    component={Earned}
                />

                <Stack.Screen
                    name='GiftCard'
                    component={GiftCard}
                />

                <Stack.Screen
                    name='Contact'
                    component={Contact}
                />

                <Stack.Screen
                    name='Career'
                    component={Career}
                />

                <Stack.Screen
                    name="ArtistApplication"
                    component={ArtistApplication}
                />

                <Stack.Screen
                    name="Franchise"
                    component={Franchise}
                />

                <Stack.Screen
                    name="Settings"
                    component={Settings}
                />

                <Stack.Screen
                    name="Concerts"
                    component={Concerts}
                />

                <Stack.Screen
                    name="ConcertDetail"
                    component={ConcertDetail}
                    options={{
                        gestureEnabled: false,
                    }}
                />

                <Stack.Screen
                    name="HollySnap"
                    component={HollySnap}
                />

                <Stack.Screen
                    name="EditProfile"
                    component={EditProfile}
                />

<Stack.Screen
                    name="DeleteAccount"
                    component={DeleteAccount}
                />

                <Stack.Screen
                    name="Shaman"
                    component={Shaman}
                />

                <Stack.Screen
                    name="Profile"
                    component={Profile}
                />

                <Stack.Screen
                    name="ProfileUpdatePassword"
                    component={ProfileUpdatePassword}
                />

                <Stack.Screen
                    name="CreditCards"
                    component={CreditCards}
                />

                <Stack.Screen
                    name="AddCreditCard"
                    component={AddCreditCard}
                />

                <Stack.Screen
                    name="HollyChat"
                    component={HollyChat}
                />

                <Stack.Screen
                    name="DailyActivity"
                    component={DailyActivity}
                />

                <Stack.Screen
                    name="DailyActivityDetail"
                    component={DailyActivityDetail}
                />

                <Stack.Screen
                    name='Wheel'
                    component={Wheel}
                />

                <Stack.Screen
                    name='ChangeCity'
                    component={ChangeCity}
                />

                <Stack.Screen
                    name='Addresses'
                    component={Addresses}
                />

                <Stack.Screen
                    name='AddAddress'
                    component={AddAddress}
                />

                <Stack.Screen
                    name='AddressCard'
                    component={AddressCard}
                />

                <Stack.Screen
                    name='BasketOkey'
                    component={BasketOkey}
                />

                <Stack.Screen
                    name='MyOrders'
                    component={MyOrders}
                />

                <Stack.Screen
                    name='OrderDetail'
                    component={OrderDetail}
                />

                <Stack.Screen
                    name='Evaluate'
                    component={Evaluate}
                />

                <Stack.Screen
                    name='Return'
                    component={Return}
                />

                <Stack.Screen
                    name='NewFriend'
                    component={NewFriend}
                />

                <Stack.Screen
                    name='Blocked'
                    component={Blocked}
                />


                <Stack.Screen
                    name='Chat'
                    component={Chat}
                />

                <Stack.Screen
                    name='AllFriends'
                    component={AllFriends}
                />

                <Stack.Screen
                    name='HollySnapDetail'
                    component={HollySnapDetail}
                />

                <Stack.Screen
                    name='Notifications'
                    component={Notifications}
                />

                <Stack.Screen
                    name='ChangeLanguage'
                    component={ChangeLanguage}
                />

                <Stack.Screen
                    name='Level'
                    component={Level}
                />

                <Stack.Screen
                    name='Wallet'
                    component={Wallet}
                />

                <Stack.Screen
                    name='PayMoney'
                    component={PayMoney}
                />
                <Stack.Screen
                    name='PayMoneyWithCard'
                    component={PayMoneyWithCard}


                />


                <Stack.Screen
                    name='TransactionHistory'
                    component={TransactionHistory}
                />

                <Stack.Screen
                    name='GizlilikveGuvenlik'
                    component={GizlilikveGuvenlik}
                />

                <Stack.Screen
                    name='IptalveIade'
                    component={IptalveIade}
                />

                <Stack.Screen
                    name='SatisSozlesmesi'
                    component={SatisSozlesmesi}
                />

                <Stack.Screen
                    name='TheBar'
                    component={TheBar}
                />

                <Stack.Screen
                    name='ChatTwo'
                    component={ChatTwo}
                />
                <Stack.Screen
                    name='HollyGarden'
                    component={HollyGarden}
                />

                <Stack.Screen
                    name='TicketPage'
                    component={TicketPage}
                />

                <Stack.Screen
                    name='CardDetails'
                    component={CardDetails}
                />










            </Stack.Navigator>
        </NavigationContainer>
    )
}
export default Navigation;
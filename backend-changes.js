// BACKEND CHANGES (app.js)

// 1. Update the "new message" event handler
socket.on('new message', async (newMessageReceived) => {
  try {
    const { senderId, receiverId, chatRoomId, text, messageStatus = MESSAGE_STATUS.SENT } = newMessageReceived;
    
    // Create chat room if it doesn't exist
    const roomId = await createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId);
    
    // Join sender to the room
    socket.join(roomId);
    
    // If receiver is connected, join them to the room too
    const receiverSocketId = connectedUsers[receiverId];
    if (receiverSocketId) {
      const receiverSocket = io.sockets.sockets.get(receiverSocketId);
      if (receiverSocket) {
        receiverSocket.join(roomId);
      }
    }
    
    // Filter inappropriate words
    const containsInappropriate = inappropriateWords.some((word) =>
      text.toLowerCase().includes(word)
    );
    if (containsInappropriate) {
      return socket.emit('message error', 'Mesaj uygunsuz kelimeler içeriyor!');
    }
    
    // Add message to database
    const messageId = await sendMessageToDB(
      pool, 
      senderId, 
      receiverId, 
      roomId, // Use the resolved roomId here
      text, 
      messageStatus
    );
    
    const now = new Date();
    const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000));
    
    // Create payload for GiftedChat
    const payload = {
      _id: messageId,
      text: text,
      createdAt: turkeyTime.toISOString(),
      user: {
        _id: senderId,
      },
      chatRoomId: roomId, // Use the resolved roomId here
      isDeleted: false,
      messageStatus: messageStatus,
    };
    
    // Send notification if receiver is not connected
    if (!connectedUsers[receiverId]) {
      const { receiverDetails, senderDetails } = await fetchNotificationDetails(receiverId, senderId);
      if (receiverDetails && receiverDetails.player_id) {
        const senderFullName = senderDetails 
          ? `${senderDetails.firstName} ${senderDetails.lastName}` 
          : 'Gönderen Adı';
        await sendNotification(receiverDetails.player_id, senderFullName, text);
      }
    }
    
    // Emit to the room - this will reach both sender and receiver if they're connected
    io.to(roomId).emit('message received', payload);
    
    // Also emit a "new conversation" event if this is a new chat room
    // This helps update the conversation list for both users
    if (!chatRoomId) {
      // Get user details for both sender and receiver
      const [users] = await pool.query(
        'SELECT id, firstName, lastName, avatar FROM users WHERE id IN (?, ?)',
        [senderId, receiverId]
      );
      
      let senderInfo = null;
      let receiverInfo = null;
      
      users.forEach(user => {
        if (user.id.toString() === senderId.toString()) {
          senderInfo = user;
        } else if (user.id.toString() === receiverId.toString()) {
          receiverInfo = user;
        }
      });
      
      // Emit to sender - with receiver as partner
      if (senderInfo && receiverInfo) {
        const senderPayload = {
          _id: roomId,
          partner: {
            _id: receiverInfo.id,
            name: `${receiverInfo.firstName} ${receiverInfo.lastName}`,
            avatar: receiverInfo.avatar || 'defaultUser.webp'
          },
          messages: [payload],
          updatedAt: turkeyTime.toISOString()
        };
        
        // Emit to receiver - with sender as partner
        const receiverPayload = {
          _id: roomId,
          partner: {
            _id: senderInfo.id,
            name: `${senderInfo.firstName} ${senderInfo.lastName}`,
            avatar: senderInfo.avatar || 'defaultUser.webp'
          },
          messages: [payload],
          updatedAt: turkeyTime.toISOString()
        };
        
        // Send to sender
        if (connectedUsers[senderId]) {
          io.to(connectedUsers[senderId]).emit('new conversation', senderPayload);
        }
        
        // Send to receiver
        if (connectedUsers[receiverId]) {
          io.to(connectedUsers[receiverId]).emit('new conversation', receiverPayload);
        }
      }
    }
    
  } catch (err) {
    logger.error('Error in new message:', err);
    socket.emit('message error', 'Internal Server Error');
  }
});

// 2. Update the createChatRoomIfNotExists function
async function createChatRoomIfNotExists(pool, senderId, receiverId, chatRoomId) {
  // If chatRoomId is provided and not null, return it
  if (chatRoomId) {
    return chatRoomId;
  }
  
  try {
    // Check if a chat room already exists
    const [rows] = await pool.query(
      `SELECT id FROM holly_chat_rooms
       WHERE (senderId=? AND receiverId=?) OR (senderId=? AND receiverId=?)`,
      [senderId, receiverId, receiverId, senderId]
    );
    
    if (rows.length > 0) {
      return rows[0].id;
    }
    
    // Create a new chat room
    const [insertResult] = await pool.query(
      `INSERT INTO holly_chat_rooms (senderId, receiverId, createdAt, updatedAt)
       VALUES (?, ?, CONVERT_TZ(NOW(), '+00:00', '+03:00'), CONVERT_TZ(NOW(), '+00:00', '+03:00'))`,
      [senderId, receiverId]
    );
    
    return insertResult.insertId;
  } catch (err) {
    logger.error(`Error creating chat room: ${err.message}`);
    logger.error(err.stack);
    throw err;
  }
}

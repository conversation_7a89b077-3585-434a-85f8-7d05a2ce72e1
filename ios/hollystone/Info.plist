<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleDisplayName</key>
    <string>hollystone</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(MARKETING_VERSION)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleVersion</key>
    <string>$(CURRENT_PROJECT_VERSION)</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSExceptionDomains</key>
        <dict>
            <key>localhost</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
            </dict>
        </dict>
    </dict>
    <key>NSCameraUsageDescription</key>
    <string>HollyStone uygulaması, QR kod tarayıcı ile puan kazanmak ve HollySnap bölümünde snap paylaşmak için kamera erişimine ihtiyaç duyar.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>HollyStone,konumunuzu belirleyerek haritada size yol tarifi ve daha iyi bir deneyim sunmak için izin istemektedir.</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>HollyStone, çektiğiniz fotoğrafları ve videoları galeriye kaydedebilmek için fotoğraf kütüphanesine erişim izni gerektirir.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>HollyStone, mevcut fotoğraflarınızı görüntülemek ve kullanmak için fotoğraf kütüphanesine erişim izni istemektedir.</string>
    <key>UIAppFonts</key>
    <array>
        <string>HelveticaNeueLTPro-BdCn.otf</string>
        <string>MADETOMMY-Bold.otf</string>
        <string>College.ttf</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen.storyboard</string>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
        <string>armv7</string>
    </array>
    <key>UIStatusBarStyle</key>
    <string></string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
        <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
</dict>
</plist>
PODS:
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.6)
  - FBReactNativeSpec (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.6)
    - RCTTypeSafety (= 0.72.6)
    - React-Core (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.72.6):
    - hermes-engine/Pre-built (= 0.72.6)
  - hermes-engine/Pre-built (0.72.6)
  - libevent (2.1.12)
  - lottie-ios (4.4.1)
  - lottie-react-native (6.7.2):
    - lottie-ios (= 4.4.1)
    - React-Core
  - OneSignalXCFramework (5.2.3):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.3)
  - OneSignalXCFramework/OneSignal (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.3):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.3)
  - OneSignalXCFramework/OneSignalExtension (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.3):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.3):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.3):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.6)
  - RCTTypeSafety (0.72.6):
    - FBLazyVector (= 0.72.6)
    - RCTRequired (= 0.72.6)
    - React-Core (= 0.72.6)
  - React (0.72.6):
    - React-Core (= 0.72.6)
    - React-Core/DevSupport (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-RCTActionSheet (= 0.72.6)
    - React-RCTAnimation (= 0.72.6)
    - React-RCTBlob (= 0.72.6)
    - React-RCTImage (= 0.72.6)
    - React-RCTLinking (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - React-RCTSettings (= 0.72.6)
    - React-RCTText (= 0.72.6)
    - React-RCTVibration (= 0.72.6)
  - React-callinvoker (0.72.6)
  - React-Codegen (0.72.6):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.6)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/CoreModulesHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTBlob
    - React-RCTImage (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.6):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-debug (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-jsinspector (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
    - React-runtimeexecutor (= 0.72.6)
  - React-debug (0.72.6)
  - React-hermes (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.6)
    - React-jsi
    - React-jsiexecutor (= 0.72.6)
    - React-jsinspector (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - React-jsi (0.72.6):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - React-jsinspector (0.72.6)
  - React-logger (0.72.6):
    - glog
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-cameraroll (5.10.0):
    - React-Core
  - react-native-image-picker (7.0.1):
    - React-Core
  - react-native-insta-story (1.1.9):
    - React
  - react-native-maps (1.8.0):
    - React-Core
  - react-native-onesignal (5.2.3):
    - OneSignalXCFramework (= 5.2.3)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (3.3.2):
    - React-Core
  - react-native-view-shot (3.7.0):
    - React-Core
  - react-native-webview (13.6.2):
    - React-Core
  - React-NativeModulesApple (0.72.6):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.6)
  - React-RCTActionSheet (0.72.6):
    - React-Core/RCTActionSheetHeaders (= 0.72.6)
  - React-RCTAnimation (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTAnimationHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTAppDelegate (0.72.6):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.6):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTBlobHeaders (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTImage (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTImageHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTLinking (0.72.6):
    - React-Codegen (= 0.72.6)
    - React-Core/RCTLinkingHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTNetwork (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTNetworkHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTSettings (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTSettingsHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTText (0.72.6):
    - React-Core/RCTTextHeaders (= 0.72.6)
  - React-RCTVibration (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTVibrationHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-rncore (0.72.6)
  - React-runtimeexecutor (0.72.6):
    - React-jsi (= 0.72.6)
  - React-runtimescheduler (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.6):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - ReactCommon/turbomodule/core (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - ReactNativeGetLocation (4.0.0):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCMaskedView (0.3.1):
    - React-Core
  - RNFlashList (1.7.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNGestureHandler (2.13.2):
    - React-Core
  - RNPermissions (5.2.6):
    - React-Core
  - RNReanimated (3.5.4):
    - DoubleConversion
    - FBLazyVector
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.25.0):
    - React-Core
    - React-RCTImage
  - RNShare (11.1.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSVG (14.2.0):
    - React-Core
  - SocketRocket (0.6.1)
  - VisionCamera (3.3.1):
    - React
    - React-callinvoker
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-insta-story (from `../node_modules/react-native-insta-story`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - fmt
    - libevent
    - lottie-ios
    - OneSignalXCFramework
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-insta-story:
    :path: "../node_modules/react-native-insta-story"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d9857fa6515e4f9985f29bf6f2e40cb03f54f859
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 748c0ef74f2bf4b36cfcccf37916806940a64c32
  FBReactNativeSpec: 966f29e4e697de53a3b366355e8f57375c856ad9
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 8057e75cfc1437b178ac86c8654b24e7fead7f60
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  lottie-react-native: 17547b2f3c7034e2ae8672833fdb63262164d18a
  OneSignalXCFramework: 356e59953e157f6e45a7fdfc863073b3168b2d01
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 28469809442eb4eb5528462705f7d852948c8a74
  RCTTypeSafety: e9c6c409fca2cc584e5b086862d562540cb38d29
  React: 769f469909b18edfe934f0539fffb319c4c61043
  React-callinvoker: e48ce12c83706401251921896576710d81e54763
  React-Codegen: a136b8094d39fd071994eaa935366e6be2239cb1
  React-Core: e548a186fb01c3a78a9aeeffa212d625ca9511bf
  React-CoreModules: d226b22d06ea1bc4e49d3c073b2c6cbb42265405
  React-cxxreact: 44a3560510ead6633b6e02f9fbbdd1772fb40f92
  React-debug: 238501490155574ae9f3f8dd1c74330eba30133e
  React-hermes: 46e66dc854124d7645c20bfec0a6be9542826ecd
  React-jsi: fbdaf4166bae60524b591b18c851b530c8cdb90c
  React-jsiexecutor: 3bf18ff7cb03cd8dfdce08fbbc0d15058c1d71ae
  React-jsinspector: 194e32c6aab382d88713ad3dd0025c5f5c4ee072
  React-logger: cebf22b6cf43434e471dc561e5911b40ac01d289
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-cameraroll: 4701ae7c3dbcd3f5e9e150ca17f250a276154b35
  react-native-image-picker: 1569cfade34b3a011191ce262423e6ce2f8db5a1
  react-native-insta-story: 67a7bc0b4ba052973aa54b613708a838f4774bf2
  react-native-maps: f699e0753c22c4d5c3a44d03895b193a4dbca6c2
  react-native-onesignal: ee856062d7ce0598becd569f4c4d1ecbb3221a3d
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: 584dc04881deb49474363f3be89e4ca0e854c057
  react-native-view-shot: f5507655f122e6b104888a11130f267a427f0d57
  react-native-webview: 8fc09f66a1a5b16bbe37c3878fda27d5982bb776
  React-NativeModulesApple: 02e35e9a51e10c6422f04f5e4076a7c02243fff2
  React-perflogger: e3596db7e753f51766bceadc061936ef1472edc3
  React-RCTActionSheet: 17ab132c748b4471012abbcdcf5befe860660485
  React-RCTAnimation: c8bbaab62be5817d2a31c36d5f2571e3f7dcf099
  React-RCTAppDelegate: af1c7dace233deba4b933cd1d6491fe4e3584ad1
  React-RCTBlob: 1bcf3a0341eb8d6950009b1ddb8aefaf46996b8c
  React-RCTImage: 670a3486b532292649b1aef3ffddd0b495a5cee4
  React-RCTLinking: bd7ab853144aed463903237e615fd91d11b4f659
  React-RCTNetwork: be86a621f3e4724758f23ad1fdce32474ab3d829
  React-RCTSettings: 4f3a29a6d23ffa639db9701bc29af43f30781058
  React-RCTText: adde32164a243103aaba0b1dc7b0a2599733873e
  React-RCTVibration: 6bd85328388ac2e82ae0ca11afe48ad5555b483a
  React-rncore: fda7b1ae5918fa7baa259105298a5487875a57c8
  React-runtimeexecutor: 57d85d942862b08f6d15441a0badff2542fd233c
  React-runtimescheduler: f23e337008403341177fc52ee4ca94e442c17ede
  React-utils: fa59c9a3375fb6f4aeb66714fd3f7f76b43a9f16
  ReactCommon: dd03c17275c200496f346af93a7b94c53f3093a4
  ReactNativeGetLocation: 87f83bee4dfecc88772de04afd076d9ae265cc42
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCMaskedView: 090213d32d8b3bb83a4dcb7d12c18f0152591906
  RNFlashList: eca7964dc9a64a533a14ab299ab95bf00b8560b3
  RNGestureHandler: bb86e378287f7713baf3ca205423eb8109790022
  RNPermissions: ddf6cb82598954630ba2c1d875225f61f1787a9b
  RNReanimated: ab2e96c6d5591c3dfbb38a464f54c8d17fb34a87
  RNScreens: 85d3880b52d34db7b8eeebe2f1a0e807c05e69fa
  RNShare: 9a305d73662c5ccc1c292af658b72df3f9d9f9d6
  RNSVG: 963a95f1f5d512a13d11ffd50d351c87fb5c6890
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  VisionCamera: ed0b360805f140c0108d9039fd8a58abbde2ff51
  Yoga: b76f1acfda8212aa16b7e26bcce3983230c82603

PODFILE CHECKSUM: 327dd5b2f42c1711bcf8be185b9bbc783f68172c

COCOAPODS: 1.16.2
